<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get_product':
            getProduct($pdo);
            break;
            
        case 'get_categories':
            getCategories($pdo);
            break;
            
        case 'upload_image':
            uploadProductImage($pdo);
            break;
            
        case 'delete_image':
            deleteProductImage($pdo);
            break;
            
        case 'get_product_images':
            getProductImages($pdo);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getProduct($pdo) {
    $product_id = (int)($_GET['id'] ?? 0);
    
    if (!$product_id) {
        throw new Exception('Product ID required');
    }
    
    $stmt = $pdo->prepare("
        SELECT p.*, pc.name as category_name,
               GROUP_CONCAT(pi.file_id) as image_ids
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        LEFT JOIN product_images pi ON p.id = pi.product_id
        WHERE p.id = ?
        GROUP BY p.id
    ");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        throw new Exception('Product not found');
    }
    
    echo json_encode([
        'success' => true,
        'product' => $product
    ]);
}

function getCategories($pdo) {
    $stmt = $pdo->query("SELECT * FROM product_categories ORDER BY name");
    $categories = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'categories' => $categories
    ]);
}

function uploadProductImage($pdo) {
    $product_id = (int)($_POST['product_id'] ?? 0);
    
    if (!$product_id) {
        throw new Exception('Product ID required');
    }
    
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No image uploaded or upload error');
    }
    
    $file = $_FILES['image'];
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    
    if (!in_array($file['type'], $allowed_types)) {
        throw new Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed');
    }
    
    // สร้างโฟลเดอร์ถ้าไม่มี
    $upload_dir = '../uploads/products/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // สร้างชื่อไฟล์ใหม่
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'product_' . $product_id . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
    $filepath = $upload_dir . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Failed to upload file');
    }
    
    // บันทึกข้อมูลไฟล์ในฐานข้อมูล
    $stmt = $pdo->prepare("
        INSERT INTO file_uploads (original_name, file_name, file_path, file_size, file_type, uploaded_by, upload_type, created_at)
        VALUES (?, ?, ?, ?, ?, ?, 'product', NOW())
    ");
    $stmt->execute([
        $file['name'],
        $filename,
        'uploads/products/' . $filename,
        $file['size'],
        $file['type'],
        $_SESSION['admin_id']
    ]);
    
    $file_id = $pdo->lastInsertId();
    
    // เชื่อมโยงกับสินค้า
    $image_type = $_POST['image_type'] ?? 'gallery';
    $stmt = $pdo->prepare("
        INSERT INTO product_images (product_id, file_id, image_type, sort_order, created_at)
        VALUES (?, ?, ?, 0, NOW())
    ");
    $stmt->execute([$product_id, $file_id, $image_type]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Image uploaded successfully',
        'file_id' => $file_id,
        'filename' => $filename,
        'file_path' => 'uploads/products/' . $filename
    ]);
}

function deleteProductImage($pdo) {
    $image_id = (int)($_POST['image_id'] ?? 0);
    
    if (!$image_id) {
        throw new Exception('Image ID required');
    }
    
    // ดึงข้อมูลไฟล์
    $stmt = $pdo->prepare("
        SELECT fu.file_path 
        FROM product_images pi
        JOIN file_uploads fu ON pi.file_id = fu.id
        WHERE pi.id = ?
    ");
    $stmt->execute([$image_id]);
    $image = $stmt->fetch();
    
    if (!$image) {
        throw new Exception('Image not found');
    }
    
    // ลบไฟล์จากระบบ
    $file_path = '../' . $image['file_path'];
    if (file_exists($file_path)) {
        unlink($file_path);
    }
    
    // ลบจากฐานข้อมูล
    $stmt = $pdo->prepare("DELETE FROM product_images WHERE id = ?");
    $stmt->execute([$image_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Image deleted successfully'
    ]);
}

function getProductImages($pdo) {
    $product_id = (int)($_GET['product_id'] ?? 0);
    
    if (!$product_id) {
        throw new Exception('Product ID required');
    }
    
    $stmt = $pdo->prepare("
        SELECT pi.*, fu.file_path, fu.original_name
        FROM product_images pi
        JOIN file_uploads fu ON pi.file_id = fu.id
        WHERE pi.product_id = ?
        ORDER BY pi.image_type DESC, pi.sort_order ASC
    ");
    $stmt->execute([$product_id]);
    $images = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'images' => $images
    ]);
}
?>
