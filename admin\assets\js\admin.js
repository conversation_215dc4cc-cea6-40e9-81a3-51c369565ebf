/**
 * GT Sport Design - Admin Panel JavaScript
 */

// ฟังก์ชันสำหรับแสดงการยืนยันก่อนลบข้อมูล
function confirmDelete(message, url) {
    if (confirm(message || 'คุณแน่ใจหรือไม่ที่จะลบรายการนี้?')) {
        window.location.href = url;
    }
    return false;
}

// ฟังก์ชันสำหรับแสดงตัวอย่างรูปภาพก่อนอัพโหลด
function previewImage(input, previewElement) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        
        reader.onload = function(e) {
            document.getElementById(previewElement).src = e.target.result;
            document.getElementById(previewElement).style.display = 'block';
        }
        
        reader.readAsDataURL(input.files[0]);
    }
}

// ฟังก์ชันสำหรับแปลงขนาดไฟล์เป็นหน่วยที่อ่านง่าย
function formatFileSize(bytes) {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
}

// ฟังก์ชันสำหรับแสดง/ซ่อนฟอร์มค้นหา
function toggleSearchForm() {
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.classList.toggle('d-none');
    }
}

// ฟังก์ชันสำหรับรีเซ็ตฟอร์ม
function resetForm(formId) {
    document.getElementById(formId).reset();
}

// ฟังก์ชันสำหรับคัดลอกข้อความไปยังคลิปบอร์ด
function copyToClipboard(text) {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    document.execCommand('copy');
    document.body.removeChild(textarea);
    
    // แสดงข้อความแจ้งเตือน
    alert('คัดลอกข้อความแล้ว: ' + text);
}

// ฟังก์ชันสำหรับเปลี่ยนสถานะการแสดงผล
function toggleVisibility(id, type, status) {
    fetch('ajax/toggle_visibility.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}&type=${type}&status=${status}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // รีโหลดหน้าเพื่อแสดงการเปลี่ยนแปลง
            location.reload();
        } else {
            alert('เกิดข้อผิดพลาด: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('เกิดข้อผิดพลาดในการเปลี่ยนสถานะ');
    });
}

// เมื่อโหลดหน้าเสร็จ
document.addEventListener('DOMContentLoaded', function() {
    // เพิ่ม tooltip ให้กับทุกปุ่มที่มี data-bs-toggle="tooltip"
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // เพิ่ม popover ให้กับทุกปุ่มที่มี data-bs-toggle="popover"
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // ซ่อนข้อความแจ้งเตือนหลังจาก 5 วินาที
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});
