<?php
session_start();
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>เกี่ยวกับเรา - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="เรื่องราวของ GT Sport Design ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬามากกว่า 10 ปี">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/banner.css" rel="stylesheet">
    
    <style>
        /* About Page Styles */
        .about-hero {
            background: var(--gradient-primary);
            color: white;
            padding: 100px 0 60px;
            position: relative;
            overflow: hidden;
        }
        
        .about-hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 400px;
            height: 400px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(150px, -150px);
        }
        
        .story-section {
            padding: 80px 0;
        }
        
        .story-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: 100%;
            transition: all 0.3s ease;
        }
        
        .story-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .story-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }
        
        .timeline {
            position: relative;
            padding: 40px 0;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--gradient-primary);
            transform: translateX(-50%);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 50px;
        }
        
        .timeline-item:nth-child(odd) .timeline-content {
            margin-right: calc(50% + 30px);
            text-align: right;
        }
        
        .timeline-item:nth-child(even) .timeline-content {
            margin-left: calc(50% + 30px);
            text-align: left;
        }
        
        .timeline-marker {
            position: absolute;
            left: 50%;
            top: 20px;
            width: 20px;
            height: 20px;
            background: var(--primary-color);
            border: 4px solid white;
            border-radius: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 0 4px var(--primary-color);
        }
        
        .timeline-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .team-section {
            background: var(--light-gray);
            padding: 80px 0;
        }
        
        .team-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .team-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .team-image {
            height: 250px;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
        }
        
        .team-info {
            padding: 30px;
        }
        
        .values-section {
            padding: 80px 0;
        }
        
        .value-card {
            text-align: center;
            padding: 40px 20px;
            border-radius: 15px;
            background: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .value-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .value-icon {
            width: 100px;
            height: 100px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2.5rem;
            color: white;
        }
        
        .stats-section {
            background: var(--gradient-dark);
            color: white;
            padding: 80px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 20px;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .cta-section {
            background: var(--gradient-primary);
            color: white;
            padding: 80px 0;
            text-align: center;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .about-hero {
                padding: 80px 0 40px;
            }
            
            .timeline::before {
                left: 30px;
            }
            
            .timeline-item:nth-child(odd) .timeline-content,
            .timeline-item:nth-child(even) .timeline-content {
                margin-left: 60px;
                margin-right: 0;
                text-align: left;
            }
            
            .timeline-marker {
                left: 30px;
            }
            
            .story-card,
            .team-info {
                padding: 30px 20px;
            }
        }
    </style>
</head>

<body>
<?php include 'includes/header.php'; ?>

<!-- About Hero Section -->
<section class="about-hero" data-aos="fade-in">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3" data-aos="fade-up">
                    เกี่ยวกับเรา
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="200">
                    GT Sport Design ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬา<br>
                    ด้วยประสบการณ์กว่า 10 ปี เราได้สร้างสรรค์ผลงานคุณภาพให้กับลูกค้ามากมาย
                </p>
                <div class="d-flex flex-wrap gap-3" data-aos="fade-up" data-aos-delay="400">
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-award me-2"></i>ประสบการณ์ 10+ ปี
                    </span>
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-users me-2"></i>ลูกค้า 1000+ ราย
                    </span>
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-heart me-2"></i>ความพึงพอใจ 99%
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-center" data-aos="fade-left">
                <i class="fas fa-building" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Story Section -->
<section class="story-section">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="display-5 fw-bold mb-3">เรื่องราวของเรา</h2>
            <p class="lead">จากความฝันสู่ความเป็นจริง</p>
        </div>
        
        <div class="row">
            <div class="col-lg-4">
                <div class="story-card" data-aos="fade-up">
                    <div class="story-icon">
                        <i class="fas fa-lightbulb"></i>
                    </div>
                    <h4 class="mb-3">จุดเริ่มต้น</h4>
                    <p class="text-muted">
                        เริ่มต้นจากความรักในกีฬาและความต้องการเสื้อกีฬาคุณภาพดี 
                        ที่ราคาเป็นมิตร เราจึงก่อตั้ง GT Sport Design ขึ้น
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="story-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="story-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h4 class="mb-3">การเติบโต</h4>
                    <p class="text-muted">
                        ด้วยความมุ่งมั่นและการพัฒนาอย่างต่อเนื่อง 
                        เราได้รับความไว้วางใจจากลูกค้ามากมายทั่วประเทศ
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="story-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="story-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h4 class="mb-3">ความสำเร็จ</h4>
                    <p class="text-muted">
                        วันนี้เราภูมิใจที่ได้เป็นส่วนหนึ่งในความสำเร็จของทีมกีฬา
                        และองค์กรต่างๆ มากมาย
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Timeline Section -->
<section class="py-5" style="background: var(--light-gray);">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="display-5 fw-bold mb-3">เส้นทางการเดินทาง</h2>
            <p class="lead">ประวัติความเป็นมาของเรา</p>
        </div>
        
        <div class="timeline">
            <div class="timeline-item" data-aos="fade-right">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h5 class="text-primary">2014</h5>
                    <h6>ก่อตั้งบริษัท</h6>
                    <p class="text-muted mb-0">เริ่มต้นธุรกิจด้วยทีมงานเพียง 3 คน และความมุ่งมั่นที่จะสร้างเสื้อกีฬาคุณภาพดี</p>
                </div>
            </div>
            
            <div class="timeline-item" data-aos="fade-left">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h5 class="text-primary">2017</h5>
                    <h6>ขยายธุรกิจ</h6>
                    <p class="text-muted mb-0">เพิ่มเครื่องจักรและทีมงาน รับงานจากองค์กรขนาดใหญ่และโรงเรียนต่างๆ</p>
                </div>
            </div>
            
            <div class="timeline-item" data-aos="fade-right">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h5 class="text-primary">2020</h5>
                    <h6>ระบบออนไลน์</h6>
                    <p class="text-muted mb-0">เปิดตัวระบบออกแบบออนไลน์ ให้ลูกค้าสามารถออกแบบเสื้อได้ด้วยตนเอง</p>
                </div>
            </div>
            
            <div class="timeline-item" data-aos="fade-left">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h5 class="text-primary">2025</h5>
                    <h6>ปัจจุบัน</h6>
                    <p class="text-muted mb-0">มีลูกค้ามากกว่า 1000 ราย และยังคงพัฒนาบริการอย่างต่อเนื่อง</p>
                </div>
            </div>
        </div>
    </div>
</section>
