<?php
/**
 * GT-SportDesign Deployment Dashboard
 * ไฟล์สำหรับตรวจสอบและติดตั้งระบบ
 */

// แสดงข้อผิดพลาดทั้งหมด
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// ตรวจสอบการดำเนินการ
$action = $_GET['action'] ?? '';
$message = '';
$error = '';

// ฟังก์ชันตรวจสอบไฟล์และโฟลเดอร์
function checkSystemRequirements() {
    $checks = [];
    
    // ตรวจสอบ PHP version
    $checks['php_version'] = [
        'name' => 'PHP Version',
        'status' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'message' => 'PHP ' . PHP_VERSION . (version_compare(PHP_VERSION, '7.4.0', '>=') ? ' (OK)' : ' (ต้องการ 7.4+)')
    ];
    
    // ตรวจสอบ extensions
    $required_extensions = ['pdo', 'pdo_mysql', 'gd', 'mbstring', 'json'];
    foreach ($required_extensions as $ext) {
        $checks['ext_' . $ext] = [
            'name' => 'Extension: ' . $ext,
            'status' => extension_loaded($ext),
            'message' => extension_loaded($ext) ? 'ติดตั้งแล้ว' : 'ไม่ได้ติดตั้ง'
        ];
    }
    
    // ตรวจสอบโฟลเดอร์
    $required_dirs = [
        'config' => 'โฟลเดอร์ config',
        'uploads' => 'โฟลเดอร์ uploads',
        'uploads/products' => 'โฟลเดอร์ uploads/products',
        'uploads/designs' => 'โฟลเดอร์ uploads/designs',
        'uploads/gallery' => 'โฟลเดอร์ uploads/gallery',
        'uploads/temp' => 'โฟลเดอร์ uploads/temp'
    ];
    
    foreach ($required_dirs as $dir => $name) {
        $exists = file_exists($dir);
        $writable = $exists ? is_writable($dir) : false;
        $checks['dir_' . str_replace('/', '_', $dir)] = [
            'name' => $name,
            'status' => $exists && $writable,
            'message' => $exists ? ($writable ? 'พร้อมใช้งาน' : 'ไม่สามารถเขียนได้') : 'ไม่พบโฟลเดอร์'
        ];
    }
    
    // ตรวจสอบไฟล์สำคัญ
    $required_files = [
        'database/db.sql' => 'ไฟล์ฐานข้อมูล',
        'install.php' => 'ไฟล์ติดตั้ง',
        'index.php' => 'ไฟล์หน้าแรก'
    ];
    
    foreach ($required_files as $file => $name) {
        $checks['file_' . str_replace(['/', '.'], '_', $file)] = [
            'name' => $name,
            'status' => file_exists($file),
            'message' => file_exists($file) ? 'พบไฟล์' : 'ไม่พบไฟล์'
        ];
    }
    
    return $checks;
}

// ฟังก์ชันสร้างโฟลเดอร์ที่จำเป็น
function createRequiredDirectories() {
    $directories = [
        'uploads',
        'uploads/products',
        'uploads/gallery',
        'uploads/designs',
        'uploads/profiles',
        'uploads/temp'
    ];
    
    $created = [];
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0755, true)) {
                $created[] = $dir;
                
                // สร้างไฟล์ index.php เพื่อป้องกัน
                $index_file = $dir . '/index.php';
                file_put_contents($index_file, "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n");
                
                // สร้าง .htaccess
                $htaccess_file = $dir . '/.htaccess';
                file_put_contents($htaccess_file, "Options -Indexes\n");
            }
        }
    }
    
    return $created;
}

// ประมวลผลการดำเนินการ
if ($action === 'create_dirs') {
    $created = createRequiredDirectories();
    if (!empty($created)) {
        $message = 'สร้างโฟลเดอร์สำเร็จ: ' . implode(', ', $created);
    } else {
        $message = 'โฟลเดอร์ทั้งหมดมีอยู่แล้ว';
    }
}

$checks = checkSystemRequirements();
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GT-SportDesign Deployment Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .dashboard-container { max-width: 1000px; margin: 20px auto; }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .check-item { padding: 10px; margin: 5px 0; border-radius: 5px; background: white; }
        .header-section { background: linear-gradient(135deg, #eb4e17, #ff6b35); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
    </style>
</head>
<body>
    <div class="container dashboard-container">
        <div class="header-section text-center">
            <h1><i class="fas fa-rocket"></i> GT-SportDesign</h1>
            <p class="mb-0">Deployment Dashboard</p>
        </div>
        
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-check"></i> ตรวจสอบระบบ</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($checks as $check): ?>
                        <div class="check-item d-flex justify-content-between align-items-center">
                            <span><?php echo $check['name']; ?></span>
                            <span class="<?php echo $check['status'] ? 'status-ok' : 'status-error'; ?>">
                                <i class="fas fa-<?php echo $check['status'] ? 'check' : 'times'; ?>"></i>
                                <?php echo $check['message']; ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> เครื่องมือ</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="?action=create_dirs" class="btn btn-primary">
                                <i class="fas fa-folder-plus"></i> สร้างโฟลเดอร์
                            </a>
                            <a href="install.php" class="btn btn-success">
                                <i class="fas fa-play"></i> เริ่มติดตั้ง
                            </a>
                            <a href="test_connection.php" class="btn btn-info">
                                <i class="fas fa-database"></i> ทดสอบฐานข้อมูล
                            </a>
                            <a href="fix_database.php" class="btn btn-warning">
                                <i class="fas fa-wrench"></i> แก้ไขฐานข้อมูล
                            </a>
                        </div>
                        
                        <hr>
                        
                        <h6>ลิงก์ด่วน</h6>
                        <div class="d-grid gap-1">
                            <a href="admin/login.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-user-shield"></i> เข้าสู่ระบบแอดมิน
                            </a>
                            <a href="index.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-home"></i> หน้าแรกเว็บไซต์
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> ข้อมูลระบบ</h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <strong>PHP:</strong> <?php echo PHP_VERSION; ?><br>
                            <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
                            <strong>Document Root:</strong> <?php echo $_SERVER['DOCUMENT_ROOT']; ?><br>
                            <strong>Current Path:</strong> <?php echo __DIR__; ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-warning mt-4">
            <h6><i class="fas fa-shield-alt"></i> คำเตือนด้านความปลอดภัย</h6>
            <ul class="mb-0">
                <li>ลบไฟล์ <code>deploy.php</code> หลังจากติดตั้งเสร็จสิ้น</li>
                <li>ลบไฟล์ <code>install.php</code> หลังจากติดตั้งเสร็จสิ้น</li>
                <li>เปลี่ยนรหัสผ่านเริ่มต้นของแอดมิน</li>
                <li>ตั้งค่า SSL Certificate</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
