<?php
/**
 * GT-SportDesign System Monitor
 * ระบบติดตามและแจ้งเตือนสถานะระบบ
 */

session_start();
require_once '../config/database.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// System monitoring functions
function getSystemStatus() {
    $status = [
        'overall' => 'healthy',
        'checks' => [],
        'alerts' => [],
        'metrics' => []
    ];
    
    // 1. Database Health
    try {
        $db = getDbConnection();
        $start_time = microtime(true);
        $stmt = $db->query("SELECT 1");
        $response_time = round((microtime(true) - $start_time) * 1000, 2);
        
        $status['checks']['database'] = [
            'status' => $response_time < 100 ? 'healthy' : ($response_time < 500 ? 'warning' : 'critical'),
            'response_time' => $response_time,
            'message' => "Database response: {$response_time}ms"
        ];
        
        // Check database size
        $stmt = $db->query("
            SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ");
        $db_size = $stmt->fetch(PDO::FETCH_ASSOC)['size_mb'] ?? 0;
        $status['metrics']['database_size'] = $db_size;
        
        // Check recent orders
        $stmt = $db->query("
            SELECT COUNT(*) as count 
            FROM payment_orders 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $recent_orders = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        $status['metrics']['recent_orders'] = $recent_orders;
        
    } catch (Exception $e) {
        $status['checks']['database'] = [
            'status' => 'critical',
            'message' => 'Database connection failed: ' . $e->getMessage()
        ];
        $status['overall'] = 'critical';
        $status['alerts'][] = 'Database connection failed';
    }
    
    // 2. File System Health
    $upload_dirs = ['../uploads', '../uploads/payment_slips', '../uploads/designs', '../uploads/products'];
    $fs_issues = [];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            $fs_issues[] = "Missing directory: " . basename($dir);
        } elseif (!is_writable($dir)) {
            $fs_issues[] = "Not writable: " . basename($dir);
        }
    }
    
    // Check disk space
    $disk_free = disk_free_space('.');
    $disk_total = disk_total_space('.');
    $disk_used_percent = round((($disk_total - $disk_free) / $disk_total) * 100, 2);
    
    $status['checks']['filesystem'] = [
        'status' => empty($fs_issues) && $disk_used_percent < 90 ? 'healthy' : 'warning',
        'disk_usage' => $disk_used_percent,
        'issues' => $fs_issues
    ];
    
    $status['metrics']['disk_usage'] = $disk_used_percent;
    $status['metrics']['disk_free_gb'] = round($disk_free / 1024 / 1024 / 1024, 2);
    
    if ($disk_used_percent > 95) {
        $status['overall'] = 'critical';
        $status['alerts'][] = 'Disk space critically low';
    } elseif ($disk_used_percent > 85) {
        $status['alerts'][] = 'Disk space running low';
    }
    
    // 3. PHP Health
    $php_issues = [];
    
    if (version_compare(PHP_VERSION, '8.0.0', '<')) {
        $php_issues[] = 'PHP version below 8.0';
    }
    
    $required_extensions = ['pdo', 'pdo_mysql', 'gd', 'curl'];
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $php_issues[] = "Missing extension: $ext";
        }
    }
    
    $memory_limit = ini_get('memory_limit');
    $memory_usage = round(memory_get_usage() / 1024 / 1024, 2);
    $memory_peak = round(memory_get_peak_usage() / 1024 / 1024, 2);
    
    $status['checks']['php'] = [
        'status' => empty($php_issues) ? 'healthy' : 'warning',
        'version' => PHP_VERSION,
        'memory_limit' => $memory_limit,
        'memory_usage' => $memory_usage,
        'memory_peak' => $memory_peak,
        'issues' => $php_issues
    ];
    
    $status['metrics']['memory_usage_mb'] = $memory_usage;
    $status['metrics']['memory_peak_mb'] = $memory_peak;
    
    // 4. Security Check
    $security_issues = [];
    
    // Check for sensitive files
    $sensitive_files = ['../install.php', '../deploy.php', '../test_connection.php'];
    foreach ($sensitive_files as $file) {
        if (file_exists($file)) {
            $security_issues[] = "Sensitive file exists: " . basename($file);
        }
    }
    
    $status['checks']['security'] = [
        'status' => empty($security_issues) ? 'healthy' : 'warning',
        'ssl_enabled' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
        'issues' => $security_issues
    ];
    
    // 5. Performance Metrics
    if (function_exists('sys_getloadavg')) {
        $load = sys_getloadavg();
        $status['metrics']['server_load'] = [
            '1min' => round($load[0], 2),
            '5min' => round($load[1], 2),
            '15min' => round($load[2], 2)
        ];
    }
    
    // Determine overall status
    foreach ($status['checks'] as $check) {
        if ($check['status'] === 'critical') {
            $status['overall'] = 'critical';
            break;
        } elseif ($check['status'] === 'warning' && $status['overall'] === 'healthy') {
            $status['overall'] = 'warning';
        }
    }
    
    return $status;
}

function logSystemEvent($event_type, $message, $severity = 'info') {
    try {
        $db = getDbConnection();
        $stmt = $db->prepare("
            INSERT INTO system_logs (event_type, message, severity, created_at) 
            VALUES (?, ?, ?, NOW())
        ");
        $stmt->execute([$event_type, $message, $severity]);
    } catch (Exception $e) {
        // Log to file if database fails
        error_log("System Event [$event_type]: $message");
    }
}

// Handle AJAX requests
if (isset($_GET['ajax'])) {
    header('Content-Type: application/json');
    
    if ($_GET['ajax'] === 'status') {
        echo json_encode(getSystemStatus());
        exit;
    }
    
    if ($_GET['ajax'] === 'metrics') {
        $status = getSystemStatus();
        echo json_encode($status['metrics']);
        exit;
    }
}

// Get current system status
$system_status = getSystemStatus();

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Monitor - GT-SportDesign</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .monitor-header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 20px 0; margin-bottom: 30px; }
        .monitor-card { background: white; border-radius: 10px; padding: 25px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .status-indicator { width: 12px; height: 12px; border-radius: 50%; display: inline-block; margin-right: 8px; }
        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-critical { background-color: #dc3545; }
        .metric-card { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px; text-align: center; }
        .metric-value { font-size: 2rem; font-weight: bold; color: #2c3e50; }
        .metric-label { font-size: 0.9rem; color: #6c757d; }
        .alert-item { border-left: 4px solid #dc3545; background: #f8d7da; padding: 10px; margin-bottom: 10px; border-radius: 4px; }
        .chart-container { position: relative; height: 200px; }
    </style>
</head>
<body>
    <div class="monitor-header">
        <div class="container">
            <h1><i class="fas fa-desktop me-2"></i>System Monitor</h1>
            <p class="mb-0">ระบบติดตามสถานะและประสิทธิภาพ GT-SportDesign</p>
        </div>
    </div>

    <div class="container">
        <!-- Overall Status -->
        <div class="monitor-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h4>
                        <span class="status-indicator status-<?php echo $system_status['overall']; ?>"></span>
                        System Status: 
                        <span class="text-<?php echo $system_status['overall'] === 'healthy' ? 'success' : ($system_status['overall'] === 'warning' ? 'warning' : 'danger'); ?>">
                            <?php echo ucfirst($system_status['overall']); ?>
                        </span>
                    </h4>
                    <p class="text-muted mb-0">Last updated: <span id="lastUpdate"><?php echo date('d/m/Y H:i:s'); ?></span></p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-primary" onclick="refreshStatus()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh
                    </button>
                    <button class="btn btn-success" onclick="toggleAutoRefresh()" id="autoRefreshBtn">
                        <i class="fas fa-play me-2"></i>Auto Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Alerts -->
        <?php if (!empty($system_status['alerts'])): ?>
        <div class="monitor-card">
            <h5><i class="fas fa-exclamation-triangle text-warning me-2"></i>System Alerts</h5>
            <?php foreach ($system_status['alerts'] as $alert): ?>
            <div class="alert-item">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $alert; ?>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- System Checks -->
        <div class="row">
            <?php foreach ($system_status['checks'] as $check_name => $check): ?>
            <div class="col-md-6">
                <div class="monitor-card">
                    <h6>
                        <span class="status-indicator status-<?php echo $check['status']; ?>"></span>
                        <?php echo ucfirst($check_name); ?> Health
                    </h6>
                    
                    <div class="mt-3">
                        <div class="d-flex justify-content-between">
                            <span>Status:</span>
                            <span class="text-<?php echo $check['status'] === 'healthy' ? 'success' : ($check['status'] === 'warning' ? 'warning' : 'danger'); ?>">
                                <?php echo ucfirst($check['status']); ?>
                            </span>
                        </div>
                        
                        <?php if (isset($check['response_time'])): ?>
                        <div class="d-flex justify-content-between">
                            <span>Response Time:</span>
                            <span><?php echo $check['response_time']; ?>ms</span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (isset($check['disk_usage'])): ?>
                        <div class="d-flex justify-content-between">
                            <span>Disk Usage:</span>
                            <span><?php echo $check['disk_usage']; ?>%</span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (isset($check['version'])): ?>
                        <div class="d-flex justify-content-between">
                            <span>Version:</span>
                            <span><?php echo $check['version']; ?></span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (isset($check['memory_usage'])): ?>
                        <div class="d-flex justify-content-between">
                            <span>Memory Usage:</span>
                            <span><?php echo $check['memory_usage']; ?> MB</span>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($check['issues'])): ?>
                        <div class="mt-2">
                            <small class="text-danger">Issues:</small>
                            <ul class="mb-0">
                                <?php foreach ($check['issues'] as $issue): ?>
                                <li><small><?php echo $issue; ?></small></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Metrics Dashboard -->
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="dbSize"><?php echo $system_status['metrics']['database_size'] ?? 0; ?></div>
                    <div class="metric-label">Database Size (MB)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="diskUsage"><?php echo $system_status['metrics']['disk_usage'] ?? 0; ?>%</div>
                    <div class="metric-label">Disk Usage</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="memoryUsage"><?php echo $system_status['metrics']['memory_usage_mb'] ?? 0; ?></div>
                    <div class="metric-label">Memory Usage (MB)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="recentOrders"><?php echo $system_status['metrics']['recent_orders'] ?? 0; ?></div>
                    <div class="metric-label">Orders (1h)</div>
                </div>
            </div>
        </div>

        <!-- Performance Chart -->
        <?php if (isset($system_status['metrics']['server_load'])): ?>
        <div class="monitor-card">
            <h5><i class="fas fa-chart-line me-2"></i>Server Load</h5>
            <div class="chart-container">
                <canvas id="loadChart"></canvas>
            </div>
        </div>
        <?php endif; ?>

        <!-- Quick Actions -->
        <div class="monitor-card">
            <h5><i class="fas fa-tools me-2"></i>System Actions</h5>
            <div class="row">
                <div class="col-md-3">
                    <a href="health_check.php" class="btn btn-info w-100">
                        <i class="fas fa-heartbeat me-2"></i>Health Check
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="backup.php" class="btn btn-warning w-100">
                        <i class="fas fa-download me-2"></i>Backup System
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="migrate.php" class="btn btn-primary w-100">
                        <i class="fas fa-database me-2"></i>Database Migration
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="dashboard.php" class="btn btn-secondary w-100">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        // Server Load Chart
        <?php if (isset($system_status['metrics']['server_load'])): ?>
        const loadCtx = document.getElementById('loadChart').getContext('2d');
        const loadChart = new Chart(loadCtx, {
            type: 'bar',
            data: {
                labels: ['1 min', '5 min', '15 min'],
                datasets: [{
                    label: 'Server Load',
                    data: [
                        <?php echo $system_status['metrics']['server_load']['1min']; ?>,
                        <?php echo $system_status['metrics']['server_load']['5min']; ?>,
                        <?php echo $system_status['metrics']['server_load']['15min']; ?>
                    ],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        <?php endif; ?>

        function refreshStatus() {
            fetch('?ajax=status')
                .then(response => response.json())
                .then(data => {
                    // Update status indicators and metrics
                    updateMetrics(data.metrics);
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleString('th-TH');
                })
                .catch(error => console.error('Error:', error));
        }

        function updateMetrics(metrics) {
            if (metrics.database_size) document.getElementById('dbSize').textContent = metrics.database_size;
            if (metrics.disk_usage) document.getElementById('diskUsage').textContent = metrics.disk_usage + '%';
            if (metrics.memory_usage_mb) document.getElementById('memoryUsage').textContent = metrics.memory_usage_mb;
            if (metrics.recent_orders !== undefined) document.getElementById('recentOrders').textContent = metrics.recent_orders;
        }

        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                btn.innerHTML = '<i class="fas fa-play me-2"></i>Auto Refresh';
                btn.className = 'btn btn-success';
                isAutoRefresh = false;
            } else {
                autoRefreshInterval = setInterval(refreshStatus, 30000); // 30 seconds
                btn.innerHTML = '<i class="fas fa-pause me-2"></i>Stop Auto';
                btn.className = 'btn btn-danger';
                isAutoRefresh = true;
            }
        }

        // Initial refresh after 5 seconds
        setTimeout(refreshStatus, 5000);
    </script>
</body>
</html>
