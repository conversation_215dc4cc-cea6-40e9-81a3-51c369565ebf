<?php
/**
 * ทดสอบการเชื่อมต่อฐานข้อมูล GT Sport Design
 */

// แสดงข้อผิดพลาดทั้งหมด
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>🔍 ทดสอบการเชื่อมต่อฐานข้อมูล</h1>";

// ข้อมูลฐานข้อมูลที่ให้มา
$configs = [
    'config1' => [
        'host' => 'localhost',
        'name' => 'gtsportd_new2',
        'user' => 'gtsportd_new2',
        'pass' => 'dXeSLQ5MXLYJdQ9ZCZKS'
    ],
    'config2' => [
        'host' => 'localhost',
        'name' => 'gtsportd_new2',
        'user' => 'gtsportd_new2',
        'pass' => ''  // ลองไม่ใส่รหัสผ่าน
    ],
    'config3' => [
        'host' => '127.0.0.1',
        'name' => 'gtsportd_new2',
        'user' => 'gtsportd_new2',
        'pass' => 'dXeSLQ5MXLYJdQ9ZCZKS'
    ]
];

foreach ($configs as $name => $config) {
    echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3>📋 ทดสอบ $name</h3>";
    echo "<strong>Host:</strong> {$config['host']}<br>";
    echo "<strong>Database:</strong> {$config['name']}<br>";
    echo "<strong>Username:</strong> {$config['user']}<br>";
    echo "<strong>Password:</strong> " . (empty($config['pass']) ? '(ไม่มี)' : str_repeat('*', strlen($config['pass']))) . "<br><br>";
    
    try {
        $dsn = "mysql:host={$config['host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        echo "✅ <span style='color: green;'>เชื่อมต่อ MySQL สำเร็จ!</span><br>";
        
        // ตรวจสอบฐานข้อมูล
        $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
        $stmt->execute([$config['name']]);
        $db_exists = $stmt->fetch();
        
        if ($db_exists) {
            echo "✅ <span style='color: green;'>พบฐานข้อมูล {$config['name']}</span><br>";
            
            // ลองเชื่อมต่อกับฐานข้อมูล
            $dsn_with_db = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
            $pdo_db = new PDO($dsn_with_db, $config['user'], $config['pass'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);
            
            echo "✅ <span style='color: green;'>เชื่อมต่อฐานข้อมูลสำเร็จ!</span><br>";
            
            // ตรวจสอบตาราง
            $tables = $pdo_db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            echo "<strong>ตารางในฐานข้อมูล:</strong> " . (count($tables) > 0 ? implode(', ', $tables) : 'ไม่มีตาราง') . "<br>";
            
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin-top: 10px;'>";
            echo "🎉 <strong>การเชื่อมต่อสำเร็จ! ใช้ config นี้ได้</strong>";
            echo "</div>";
            
        } else {
            echo "❌ <span style='color: red;'>ไม่พบฐานข้อมูล {$config['name']}</span><br>";
        }
        
    } catch (PDOException $e) {
        echo "❌ <span style='color: red;'>การเชื่อมต่อล้มเหลว:</span><br>";
        echo "<code style='background: #f8d7da; padding: 5px; border-radius: 3px;'>{$e->getMessage()}</code><br>";
    }
    
    echo "</div>";
}

echo "<hr>";

// ตรวจสอบไฟล์ config ปัจจุบัน
echo "<h2>📄 ตรวจสอบไฟล์ config ปัจจุบัน</h2>";

if (file_exists('config/database.php')) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
    echo "<h3>ไฟล์ config/database.php มีอยู่แล้ว</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    echo htmlspecialchars(file_get_contents('config/database.php'));
    echo "</pre>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ ไม่พบไฟล์ config/database.php</h3>";
    echo "</div>";
}

echo "<hr>";

// แสดงคำแนะนำ
echo "<div style='background: #cce5ff; padding: 20px; border-radius: 10px;'>";
echo "<h2>💡 คำแนะนำการแก้ไข</h2>";
echo "<ol>";
echo "<li><strong>ตรวจสอบข้อมูลใน DirectAdmin:</strong><br>";
echo "   - เข้า MySQL Management<br>";
echo "   - ตรวจสอบ Database name, Username, Password<br>";
echo "   - ตรวจสอบสิทธิ์ของ User</li>";
echo "<li><strong>ลองรีเซ็ตรหัสผ่าน:</strong><br>";
echo "   - ใน DirectAdmin → MySQL Management<br>";
echo "   - เลือก User → Change Password</li>";
echo "<li><strong>ตรวจสอบ Host:</strong><br>";
echo "   - ลอง localhost หรือ 127.0.0.1<br>";
echo "   - บางครั้งอาจเป็น IP อื่น</li>";
echo "<li><strong>ติดต่อ Hosting Provider:</strong><br>";
echo "   - ขอให้ตรวจสอบการตั้งค่า MySQL<br>";
echo "   - ขอข้อมูลการเชื่อมต่อที่ถูกต้อง</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";

// ปุ่มสร้าง config ใหม่
echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<h3>🔧 เครื่องมือช่วยเหลือ</h3>";
echo "<a href='?action=create_config' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>สร้างไฟล์ Config ใหม่</a>";
echo "<a href='test_install.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>ไปหน้าติดตั้ง</a>";
echo "<a href='fix_system.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>ตรวจสอบระบบ</a>";
echo "</div>";

// สร้างไฟล์ config ใหม่
if (isset($_GET['action']) && $_GET['action'] === 'create_config') {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📝 สร้างไฟล์ config ใหม่</h3>";
    
    $config_content = "<?php
/**
 * GT-SportDesign Database Configuration
 * Auto-generated on " . date('Y-m-d H:i:s') . "
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'gtsportd_new2');
define('DB_USER', 'gtsportd_new2');
define('DB_PASS', 'dXeSLQ5MXLYJdQ9ZCZKS');
define('DB_CHARSET', 'utf8mb4');

// PDO Connection Function
function getDbConnection() {
    try {
        \$dsn = 'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=' . DB_CHARSET;
        \$pdo = new PDO(\$dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        return \$pdo;
    } catch (PDOException \$e) {
        die('Database connection failed: ' . \$e->getMessage());
    }
}

// Global database connection
function getDB() {
    return getDbConnection();
}

// Backward compatibility
\$pdo = getDbConnection();
?>";

    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    if (file_put_contents('config/database.php', $config_content)) {
        echo "✅ สร้างไฟล์ config/database.php สำเร็จ!<br>";
        echo "<a href='test_install.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 3px;'>ทดสอบติดตั้งระบบ</a>";
    } else {
        echo "❌ ไม่สามารถสร้างไฟล์ config ได้";
    }
    echo "</div>";
}

echo "<p><small>สร้างเมื่อ: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Kanit', sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #ee501b;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
}
</style>
