<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

// ดึงข้อมูลการจอง
$bookings = [];
$total_bookings = 0;
$error = '';
$success = '';

try {
    // ลองดึงจากตาราง bookings
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM bookings");
        $total_bookings = $stmt->fetchColumn();
        
        $stmt = $pdo->query("
            SELECT b.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
            FROM bookings b
            LEFT JOIN customers c ON b.customer_id = c.id
            ORDER BY b.booking_date DESC, b.booking_time DESC
            LIMIT 50
        ");
        $bookings = $stmt->fetchAll();
    } catch (Exception $e) {
        // ถ้าไม่มีตาราง bookings ให้สร้างข้อมูลจำลอง
        $bookings = [
            [
                'id' => 1,
                'customer_name' => 'นาย ก ใจดี',
                'customer_phone' => '************',
                'customer_email' => '<EMAIL>',
                'booking_type' => 'design_consultation',
                'booking_date' => date('Y-m-d', strtotime('+1 day')),
                'booking_time' => '10:00:00',
                'status' => 'pending',
                'notes' => 'ต้องการปรึกษาการออกแบบเสื้อทีมฟุตบอล',
                'admin_notes' => '',
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'customer_name' => 'นางสาว ข สวยงาม',
                'customer_phone' => '************',
                'customer_email' => '<EMAIL>',
                'booking_type' => 'pickup',
                'booking_date' => date('Y-m-d'),
                'booking_time' => '14:00:00',
                'status' => 'confirmed',
                'notes' => 'มารับสินค้าที่ร้าน',
                'admin_notes' => 'เตรียมสินค้าไว้แล้ว',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'id' => 3,
                'customer_name' => 'นาย ค มีสุข',
                'customer_phone' => '************',
                'customer_email' => '<EMAIL>',
                'booking_type' => 'delivery',
                'booking_date' => date('Y-m-d', strtotime('+2 days')),
                'booking_time' => '09:00:00',
                'status' => 'pending',
                'notes' => 'ส่งสินค้าที่บ้าน',
                'admin_notes' => '',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours'))
            ]
        ];
        $total_bookings = count($bookings);
    }
} catch (Exception $e) {
    $error = 'เกิดข้อผิดพลาดในการโหลดข้อมูล: ' . $e->getMessage();
}

// จัดการการอัปเดตสถานะการจอง
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $booking_id = (int)($_POST['booking_id'] ?? 0);
    
    if ($action === 'update_status' && $booking_id > 0) {
        $new_status = $_POST['status'] ?? '';
        $admin_notes = $_POST['admin_notes'] ?? '';
        
        try {
            $stmt = $pdo->prepare("
                UPDATE bookings 
                SET status = ?, admin_notes = ?, confirmed_by = ?, confirmed_at = NOW(), updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $admin_notes, $_SESSION['admin_id'], $booking_id]);
            
            $success = 'อัปเดตสถานะการจองเรียบร้อยแล้ว';
            
            // รีเฟรชข้อมูล
            header('Location: bookings.php?success=1');
            exit();
        } catch (Exception $e) {
            $error = 'ไม่สามารถอัปเดตสถานะได้: ' . $e->getMessage();
        }
    }
}

function getBookingTypeText($type) {
    $types = [
        'design_consultation' => 'ปรึกษาการออกแบบ',
        'pickup' => 'รับสินค้าที่ร้าน',
        'delivery' => 'จัดส่งสินค้า'
    ];
    return $types[$type] ?? $type;
}

function getStatusBadge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning">รอยืนยัน</span>',
        'confirmed' => '<span class="badge bg-success">ยืนยันแล้ว</span>',
        'completed' => '<span class="badge bg-info">เสร็จสิ้น</span>',
        'cancelled' => '<span class="badge bg-danger">ยกเลิก</span>'
    ];
    return $badges[$status] ?? '<span class="badge bg-secondary">' . $status . '</span>';
}

function getBookingTypeIcon($type) {
    $icons = [
        'design_consultation' => 'fas fa-paint-brush',
        'pickup' => 'fas fa-store',
        'delivery' => 'fas fa-truck'
    ];
    return $icons[$type] ?? 'fas fa-calendar';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการการจอง - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .booking-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border-left: 5px solid #e9ecef;
        }
        .booking-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .booking-card.pending {
            border-left-color: #ffc107;
        }
        .booking-card.confirmed {
            border-left-color: #28a745;
        }
        .booking-card.completed {
            border-left-color: #17a2b8;
        }
        .booking-card.cancelled {
            border-left-color: #dc3545;
        }
        .booking-type-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
            margin-right: 15px;
        }
        .booking-type-design {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .booking-type-pickup {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .booking-type-delivery {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .booking-datetime {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 10px 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php" class="active"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">จัดการการจอง</h4>
                <small class="text-muted">ดูและจัดการการจองของลูกค้า</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>อัปเดตข้อมูลเรียบร้อยแล้ว
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $total_bookings; ?></h3>
                            <p class="mb-0">การจองทั้งหมด</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo count(array_filter($bookings, fn($b) => $b['status'] === 'pending')); ?></h3>
                            <p class="mb-0">รอยืนยัน</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo count(array_filter($bookings, fn($b) => $b['status'] === 'confirmed')); ?></h3>
                            <p class="mb-0">ยืนยันแล้ว</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?php echo count(array_filter($bookings, fn($b) => $b['booking_date'] === date('Y-m-d'))); ?></h3>
                            <p class="mb-0">วันนี้</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bookings List -->
            <?php if (empty($bookings)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                    <h3>ยังไม่มีการจอง</h3>
                    <p class="text-muted">เมื่อลูกค้าทำการจองเข้ามา จะแสดงที่นี่</p>
                </div>
            <?php else: ?>
                <?php foreach ($bookings as $booking): ?>
                <div class="booking-card <?php echo $booking['status']; ?>">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center mb-3">
                                <div class="booking-type-icon booking-type-<?php echo str_replace('_', '-', $booking['booking_type']); ?>">
                                    <i class="<?php echo getBookingTypeIcon($booking['booking_type']); ?>"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1"><?php echo getBookingTypeText($booking['booking_type']); ?></h6>
                                    <small class="text-muted">รหัสการจอง: #<?php echo str_pad($booking['id'], 4, '0', STR_PAD_LEFT); ?></small>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>ลูกค้า:</strong> <?php echo htmlspecialchars($booking['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                    <strong>โทร:</strong> <?php echo htmlspecialchars($booking['customer_phone'] ?? 'ไม่ระบุ'); ?><br>
                                    <strong>อีเมล:</strong> <?php echo htmlspecialchars($booking['customer_email'] ?? 'ไม่ระบุ'); ?>
                                </div>
                                <div class="col-md-6">
                                    <div class="booking-datetime">
                                        <i class="fas fa-calendar me-2"></i>
                                        <strong><?php echo date('d/m/Y', strtotime($booking['booking_date'])); ?></strong><br>
                                        <i class="fas fa-clock me-2"></i>
                                        <strong><?php echo date('H:i', strtotime($booking['booking_time'])); ?> น.</strong>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (!empty($booking['notes'])): ?>
                                <div class="mt-2">
                                    <strong>หมายเหตุ:</strong> <?php echo htmlspecialchars($booking['notes']); ?>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($booking['admin_notes'])): ?>
                                <div class="mt-2">
                                    <strong>หมายเหตุจากแอดมิน:</strong> 
                                    <span class="text-primary"><?php echo htmlspecialchars($booking['admin_notes']); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-3">
                                <?php echo getStatusBadge($booking['status']); ?>
                            </div>
                            <div class="mb-2">
                                <small class="text-muted">
                                    จองเมื่อ: <?php echo date('d/m/Y H:i', strtotime($booking['created_at'])); ?>
                                </small>
                            </div>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#bookingModal<?php echo $booking['id']; ?>">
                                <i class="fas fa-edit me-1"></i>จัดการ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Booking Modal -->
                <div class="modal fade" id="bookingModal<?php echo $booking['id']; ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">จัดการการจอง #<?php echo str_pad($booking['id'], 4, '0', STR_PAD_LEFT); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form method="POST">
                                <div class="modal-body">
                                    <input type="hidden" name="action" value="update_status">
                                    <input type="hidden" name="booking_id" value="<?php echo $booking['id']; ?>">
                                    
                                    <div class="alert alert-info">
                                        <strong>ข้อมูลการจอง:</strong><br>
                                        <strong>ประเภท:</strong> <?php echo getBookingTypeText($booking['booking_type']); ?><br>
                                        <strong>ลูกค้า:</strong> <?php echo htmlspecialchars($booking['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                        <strong>วันที่:</strong> <?php echo date('d/m/Y', strtotime($booking['booking_date'])); ?><br>
                                        <strong>เวลา:</strong> <?php echo date('H:i', strtotime($booking['booking_time'])); ?> น.<br>
                                        <?php if (!empty($booking['notes'])): ?>
                                            <strong>หมายเหตุ:</strong> <?php echo htmlspecialchars($booking['notes']); ?>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">สถานะ</label>
                                        <select name="status" class="form-select" required>
                                            <option value="pending" <?php echo $booking['status'] === 'pending' ? 'selected' : ''; ?>>รอยืนยัน</option>
                                            <option value="confirmed" <?php echo $booking['status'] === 'confirmed' ? 'selected' : ''; ?>>ยืนยันแล้ว</option>
                                            <option value="completed" <?php echo $booking['status'] === 'completed' ? 'selected' : ''; ?>>เสร็จสิ้น</option>
                                            <option value="cancelled" <?php echo $booking['status'] === 'cancelled' ? 'selected' : ''; ?>>ยกเลิก</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">หมายเหตุจากแอดมิน</label>
                                        <textarea name="admin_notes" class="form-control" rows="3" placeholder="เพิ่มหมายเหตุ..."><?php echo htmlspecialchars($booking['admin_notes'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                    <button type="submit" class="btn btn-primary">บันทึก</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
