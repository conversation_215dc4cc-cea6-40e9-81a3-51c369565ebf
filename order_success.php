<?php
session_start();

$order_number = $_GET['order'] ?? '';

// ถ้าไม่มีเลขที่คำสั่งซื้อ redirect กลับ
if (empty($order_number)) {
    header('Location: index.php');
    exit;
}

require_once 'includes/config.php';
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>สั่งซื้อสำเร็จ - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>

<body>
<?php include 'includes/header.php'; ?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Success Message -->
            <div class="text-center mb-5">
                <div class="success-icon mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                <h1 class="text-success mb-3">สั่งซื้อสำเร็จ!</h1>
                <p class="lead text-muted">ขอบคุณที่ไว้วางใจ GT Sport Design</p>
            </div>

            <!-- Order Information -->
            <div class="card mb-4">
                <div class="card-body text-center">
                    <h4 class="mb-3">เลขที่คำสั่งซื้อ</h4>
                    <h2 class="text-primary mb-4"><?php echo htmlspecialchars($order_number); ?></h2>
                    <p class="text-muted">
                        กรุณาเก็บเลขที่คำสั่งซื้อนี้ไว้สำหรับการติดตาม
                    </p>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list-ol me-2"></i>ขั้นตอนต่อไป</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>ได้รับคำสั่งซื้อ</h6>
                                <p class="text-muted small">เราได้รับคำสั่งซื้อของคุณเรียบร้อยแล้ว</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>ยืนยันคำสั่งซื้อ</h6>
                                <p class="text-muted small">เราจะติดต่อกลับภายใน 2-4 ชั่วโมง</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>ชำระเงิน</h6>
                                <p class="text-muted small">ชำระเงินตามวิธีที่เลือกไว้</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>เริ่มผลิต</h6>
                                <p class="text-muted small">ระยะเวลาผลิต 7-14 วันทำการ</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <div class="timeline-content">
                                <h6>จัดส่งสินค้า</h6>
                                <p class="text-muted small">จัดส่งถึงที่อยู่ที่ระบุไว้</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-phone me-2"></i>ติดต่อเรา</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="contact-item">
                                <i class="fas fa-phone text-primary me-3"></i>
                                <div>
                                    <strong>โทรศัพท์</strong>
                                    <br><a href="tel:0855599164">************</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="contact-item">
                                <i class="fab fa-line text-success me-3"></i>
                                <div>
                                    <strong>Line</strong>
                                    <br><a href="https://line.me/ti/p/@gtsport">@gtsport</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="contact-item">
                                <i class="fas fa-envelope text-info me-3"></i>
                                <div>
                                    <strong>อีเมล</strong>
                                    <br><a href="mailto:<EMAIL>"><EMAIL></a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="contact-item">
                                <i class="fab fa-facebook text-primary me-3"></i>
                                <div>
                                    <strong>Facebook</strong>
                                    <br><a href="https://www.facebook.com/GTSportDesign.1">GT Sport Design</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center">
                <div class="row g-3">
                    <div class="col-md-3">
                        <?php if (isset($_SESSION['user_id'])): ?>
                        <a href="customer_orders.php" class="btn btn-primary w-100">
                            <i class="fas fa-list me-2"></i>ดูคำสั่งซื้อ
                        </a>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-3">
                        <a href="products.php" class="btn btn-outline-primary w-100">
                            <i class="fas fa-shopping-cart me-2"></i>ช้อปต่อ
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="shirt-design.php" class="btn btn-outline-success w-100">
                            <i class="fas fa-paint-brush me-2"></i>ออกแบบใหม่
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="contact.php" class="btn btn-outline-info w-100">
                            <i class="fas fa-headset me-2"></i>ติดต่อเรา
                        </a>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="alert alert-info mt-4">
                <h6><i class="fas fa-info-circle me-2"></i>ข้อมูลสำคัญ</h6>
                <ul class="mb-0">
                    <li>เราจะส่งอีเมลยืนยันคำสั่งซื้อให้คุณ</li>
                    <li>สามารถติดตามสถานะคำสั่งซื้อได้ที่เว็บไซต์</li>
                    <li>หากมีคำถาม กรุณาติดต่อเราพร้อมแจ้งเลขที่คำสั่งซื้อ</li>
                    <li>การชำระเงินล่าช้าอาจส่งผลต่อระยะเวลาการผลิต</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Confetti Animation -->
<script>
// Simple confetti animation
function createConfetti() {
    const colors = ['#ff6b35', '#ee501b', '#28a745', '#007bff', '#ffc107'];
    
    for (let i = 0; i < 50; i++) {
        setTimeout(() => {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = Math.random() * 100 + 'vw';
            confetti.style.top = '-10px';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.borderRadius = '50%';
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '9999';
            confetti.style.animation = 'fall 3s linear forwards';
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.remove();
            }, 3000);
        }, i * 100);
    }
}

// CSS for confetti animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fall {
        0% {
            transform: translateY(-10px) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Start confetti when page loads
window.addEventListener('load', () => {
    setTimeout(createConfetti, 500);
});
</script>

<style>
.success-icon {
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #dee2e6;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    color: white;
}

.timeline-marker.completed {
    background: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.timeline-content h6 {
    margin-bottom: 5px;
    color: var(--dark-color);
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.contact-item i {
    font-size: 1.5rem;
}

.contact-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.contact-item a:hover {
    text-decoration: underline;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.card-header {
    background-color: var(--light-gray);
    border-bottom: 1px solid #eee;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
}

@media (max-width: 768px) {
    .timeline {
        padding-left: 20px;
    }
    
    .timeline-marker {
        left: -18px;
        width: 12px;
        height: 12px;
        font-size: 6px;
    }
    
    .success-icon i {
        font-size: 3rem !important;
    }
}
</style>

</body>
</html>
