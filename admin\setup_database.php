<?php
/**
 * Database Setup Script for GT-SportDesign
 * This file is used during installation to create tables and initial data
 */

if (!isset($db)) {
    die('Database connection not available');
}

try {
    // Read and execute SQL file
    $sql_content = file_get_contents(__DIR__ . '/../database/db.sql');
    
    if ($sql_content === false) {
        throw new Exception('Cannot read database schema file');
    }
    
    // Split SQL into individual statements
    $statements = explode(';', $sql_content);
    
    $db->beginTransaction();
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $db->exec($statement);
            } catch (PDOException $e) {
                // Skip if table already exists or other non-critical errors
                if (strpos($e->getMessage(), 'already exists') === false && 
                    strpos($e->getMessage(), 'Duplicate entry') === false) {
                    throw $e;
                }
            }
        }
    }
    
    // Create admin user if provided in POST data
    if (isset($_POST['admin_email']) && isset($_POST['admin_password']) && isset($_POST['admin_name'])) {
        $admin_email = $_POST['admin_email'];
        $admin_password = password_hash($_POST['admin_password'], PASSWORD_DEFAULT);
        $admin_name = $_POST['admin_name'];
        
        // Check if admin already exists
        $stmt = $db->prepare("SELECT id FROM admin_users WHERE email = ? OR username = 'admin'");
        $stmt->execute([$admin_email]);
        
        if (!$stmt->fetch()) {
            // Create new admin user
            $stmt = $db->prepare("
                INSERT INTO admin_users (username, email, fullname, password, role, status, created_at) 
                VALUES ('admin', ?, ?, ?, 'super_admin', 'active', NOW())
            ");
            $stmt->execute([$admin_email, $admin_name, $admin_password]);
        } else {
            // Update existing admin
            $stmt = $db->prepare("
                UPDATE admin_users 
                SET email = ?, fullname = ?, password = ?, updated_at = NOW() 
                WHERE username = 'admin' OR email = ?
            ");
            $stmt->execute([$admin_email, $admin_name, $admin_password, $admin_email]);
        }
    }
    
    $db->commit();
    
    // Create uploads directory if it doesn't exist
    $upload_dirs = [
        __DIR__ . '/../uploads',
        __DIR__ . '/../uploads/products',
        __DIR__ . '/../uploads/gallery',
        __DIR__ . '/../uploads/designs',
        __DIR__ . '/../uploads/profiles',
        __DIR__ . '/../uploads/temp'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // Create .htaccess for security
        $htaccess_file = $dir . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            file_put_contents($htaccess_file, "Options -Indexes\n");
        }
    }
    
    // Create index.php in uploads directory for security
    $index_file = __DIR__ . '/../uploads/index.php';
    if (!file_exists($index_file)) {
        file_put_contents($index_file, "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n");
    }
    
    echo "Database setup completed successfully!";
    
} catch (Exception $e) {
    if (isset($db) && $db->inTransaction()) {
        $db->rollback();
    }
    throw new Exception('Database setup failed: ' . $e->getMessage());
}
?>
