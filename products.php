<?php
/**
 * GT-SportDesign - Products Page
 * Professional product catalog system
 * Version: 2.0 - Production Ready
 */

session_start();
require_once './config/database.php';

$db = getDbConnection();
$products = [];
$categories = [];
$error_message = '';

// Get categories - ลองทั้งสองตาราง
try {
    $stmt = $db->query("
        SELECT pc.*, COUNT(p.id) as product_count
        FROM product_categories pc
        LEFT JOIN products p ON pc.id = p.category_id AND p.status = 'active'
        WHERE pc.status = 'active'
        GROUP BY pc.id
        ORDER BY pc.sort_order, pc.name
    ");
    $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    try {
        $stmt = $db->query("
            SELECT c.*, COUNT(p.id) as product_count
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id AND p.status = 'active'
            WHERE c.status = 'active'
            GROUP BY c.id
            ORDER BY c.sort_order, c.name
        ");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e2) {
        $categories = [];
        $error_message = 'เกิดข้อผิดพลาดในการโหลดหมวดหมู่';
    }
}

// Get filter parameters
$category_filter = intval($_GET['category'] ?? 0);
$search_query = trim($_GET['search'] ?? '');
$sort_by = $_GET['sort'] ?? 'name';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 12;
$offset = ($page - 1) * $per_page;

// Build WHERE clause
$where_conditions = ["p.status = 'active'"];
$params = [];

if ($category_filter > 0) {
    $where_conditions[] = 'p.category_id = ?';
    $params[] = $category_filter;
}

if (!empty($search_query)) {
    $where_conditions[] = '(p.name LIKE ? OR p.description LIKE ?)';
    $params[] = "%$search_query%";
    $params[] = "%$search_query%";
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get total count
try {
    $count_stmt = $db->prepare("
        SELECT COUNT(*)
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        LEFT JOIN categories c ON p.category_id = c.id
        $where_clause
    ");
    $count_stmt->execute($params);
    $total_products = $count_stmt->fetchColumn();
    $total_pages = ceil($total_products / $per_page);
} catch (Exception $e) {
    $total_products = 0;
    $total_pages = 0;
}

// Get products
try {
    $order_clause = match($sort_by) {
        'price_low' => 'ORDER BY p.price ASC',
        'price_high' => 'ORDER BY p.price DESC',
        'newest' => 'ORDER BY p.created_at DESC',
        default => 'ORDER BY p.name ASC'
    };
    
    $stmt = $db->prepare("
        SELECT p.*,
               CASE
                   WHEN pc.name IS NOT NULL THEN pc.name
                   WHEN c.name IS NOT NULL THEN c.name
                   ELSE 'ไม่ระบุ'
               END as category_name
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        LEFT JOIN categories c ON p.category_id = c.id
        $where_clause
        $order_clause
        LIMIT $per_page OFFSET $offset
    ");
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $error_message = 'เกิดข้อผิดพลาดในการโหลดสินค้า';
}
?>
<?php include './includes/header.php'; ?>
<body>
    <!-- Navigation -->
    
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">สินค้าของเรา</h1>
                    <p class="lead mb-4">เลือกสินค้าคุณภาพสูงสำหรับการออกแบบของคุณ</p>
                    <div class="d-flex gap-3">
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-check me-2"></i>คุณภาพพรีเมียม
                        </span>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-palette me-2"></i>ออกแบบได้ตามใจ
                        </span>
                        <span class="badge bg-light text-dark fs-6 px-3 py-2">
                            <i class="fas fa-shipping-fast me-2"></i>จัดส่งรวดเร็ว
                        </span>
                    </div>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-tshirt" style="font-size: 8rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </section>
    
    <div class="container">
        <!-- Filter Section -->
        <div class="filter-section">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <!-- Search -->
                    <form method="GET" class="search-box">
                        <input type="hidden" name="category" value="<?php echo $category_filter; ?>">
                        <input type="hidden" name="sort" value="<?php echo $sort_by; ?>">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" name="search" class="form-control" 
                               placeholder="ค้นหาสินค้า..." 
                               value="<?php echo htmlspecialchars($search_query); ?>">
                    </form>
                </div>
                <div class="col-lg-6">
                    <!-- Sort -->
                    <form method="GET" class="d-flex justify-content-end">
                        <input type="hidden" name="category" value="<?php echo $category_filter; ?>">
                        <input type="hidden" name="search" value="<?php echo htmlspecialchars($search_query); ?>">
                        <select name="sort" class="form-select w-auto" onchange="this.form.submit()">
                            <option value="name" <?php echo $sort_by === 'name' ? 'selected' : ''; ?>>เรียงตามชื่อ</option>
                            <option value="price_low" <?php echo $sort_by === 'price_low' ? 'selected' : ''; ?>>ราคาต่ำ - สูง</option>
                            <option value="price_high" <?php echo $sort_by === 'price_high' ? 'selected' : ''; ?>>ราคาสูง - ต่ำ</option>
                            <option value="newest" <?php echo $sort_by === 'newest' ? 'selected' : ''; ?>>ใหม่ล่าสุด</option>
                        </select>
                    </form>
                </div>
            </div>
            
            <!-- Category Filter -->
            <div class="category-filter mt-3">
                <a href="?search=<?php echo urlencode($search_query); ?>&sort=<?php echo $sort_by; ?>" 
                   class="category-btn <?php echo $category_filter === 0 ? 'active' : ''; ?>">
                    <i class="fas fa-th-large me-2"></i>ทั้งหมด
                </a>
                <?php foreach ($categories as $category): ?>
                <a href="?category=<?php echo $category['id']; ?>&search=<?php echo urlencode($search_query); ?>&sort=<?php echo $sort_by; ?>" 
                   class="category-btn <?php echo $category_filter === $category['id'] ? 'active' : ''; ?>">
                    <?php echo htmlspecialchars($category['name']); ?>
                    <span class="badge bg-secondary ms-2"><?php echo $category['product_count']; ?></span>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Results Info -->
        <?php if ($total_products > 0): ?>
        <div class="results-info">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        พบสินค้า <?php echo number_format($total_products); ?> รายการ
                        <?php if (!empty($search_query)): ?>
                        สำหรับ "<?php echo htmlspecialchars($search_query); ?>"
                        <?php endif; ?>
                    </h6>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        หน้า <?php echo $page; ?> จาก <?php echo $total_pages; ?>
                    </small>
                </div>
            </div>
        </div>
        <?php endif; ?>
        
        <!-- Error Message -->
        <?php if ($error_message): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
        </div>
        <?php endif; ?>
        
        <!-- Products Grid -->
        <?php if (!empty($products)): ?>
        <div class="row">
            <?php foreach ($products as $product): ?>
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="product-card">
                    <?php if ($product['is_featured'] ?? false): ?>
                    <div class="featured-badge">
                        <i class="fas fa-star me-1"></i>แนะนำ
                    </div>
                    <?php endif; ?>
                    
                    <div class="product-image">
                        <?php if (!empty($product['image_url'])): ?>
                        <img src="<?php echo htmlspecialchars($product['image_url']); ?>" 
                             alt="<?php echo htmlspecialchars($product['name']); ?>" 
                             class="w-100 h-100" style="object-fit: cover;">
                        <?php else: ?>
                        <i class="fas fa-tshirt"></i>
                        <?php endif; ?>
                    </div>
                    
                    <div class="product-info">
                        <div class="product-category">
                            <?php echo htmlspecialchars($product['category_name'] ?? 'ไม่ระบุ'); ?>
                        </div>
                        
                        <h5 class="product-title">
                            <?php echo htmlspecialchars($product['name']); ?>
                        </h5>
                        
                        <?php if (!empty($product['description'])): ?>
                        <p class="text-muted small mb-3">
                            <?php echo htmlspecialchars(substr($product['description'], 0, 100)); ?>
                            <?php if (strlen($product['description']) > 100): ?>...<?php endif; ?>
                        </p>
                        <?php endif; ?>
                        
                        <div class="product-price">
                            ฿<?php echo number_format($product['price']); ?>
                        </div>
                        
                        <a href="design.php?product_id=<?php echo $product['id']; ?>" 
                           class="btn btn-design">
                            <i class="fas fa-palette me-2"></i>เริ่มออกแบบ
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Product pagination">
            <ul class="pagination">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page-1; ?>&category=<?php echo $category_filter; ?>&search=<?php echo urlencode($search_query); ?>&sort=<?php echo $sort_by; ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&category=<?php echo $category_filter; ?>&search=<?php echo urlencode($search_query); ?>&sort=<?php echo $sort_by; ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page+1; ?>&category=<?php echo $category_filter; ?>&search=<?php echo urlencode($search_query); ?>&sort=<?php echo $sort_by; ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- No Products Found -->
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h3>ไม่พบสินค้า</h3>
            <p class="text-muted">ลองเปลี่ยนคำค้นหาหรือหมวดหมู่</p>
            <a href="products.php" class="btn btn-primary">
                <i class="fas fa-refresh me-2"></i>ดูสินค้าทั้งหมด
            </a>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Footer -->
    <?php include './includes/footer.php'; ?>