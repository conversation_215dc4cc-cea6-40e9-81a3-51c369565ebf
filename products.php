<?php
session_start();

// ข้อมูลสินค้าตัวอย่าง (ขยายเพิ่มเติม)
$products = [
    [
        'id' => 1,
        'name' => 'เสื้อกีฬาทีมฟุตบอล',
        'price' => 350,
        'image' => 'uploads/products/football-jersey.jpg',
        'category' => 'sport',
        'description' => 'เสื้อกีฬาทีมฟุตบอลคุณภาพสูง ผ้า Dry-fit ระบายอากาศดี',
        'features' => ['ผ้า Dry-fit', 'ระบายอากาศ', 'ทนทาน', 'ซักง่าย'],
        'colors' => ['แดง', 'น้ำเงิน', 'เขียว', 'เหลือง'],
        'sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
        'rating' => 4.8,
        'reviews' => 156,
        'featured' => true
    ],
    [
        'id' => 2,
        'name' => 'เสื้อบริษัทสีน้ำเงิน',
        'price' => 280,
        'image' => 'uploads/products/company-shirt.jpg',
        'category' => 'company',
        'description' => 'เสื้อบริษัทสีน้ำเงิน ดีไซน์เรียบหรู เหมาะสำหรับการทำงาน',
        'features' => ['ผ้าโพลีเอสเตอร์', 'ไม่ยับง่าย', 'ดูดซับเหงื่อ', 'สีไม่ตก'],
        'colors' => ['น้ำเงิน', 'ขาว', 'เทา', 'ดำ'],
        'sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
        'rating' => 4.6,
        'reviews' => 89,
        'featured' => false
    ],
    [
        'id' => 3,
        'name' => 'เสื้อกิจกรรมโรงเรียน',
        'price' => 250,
        'image' => 'uploads/products/school-activity.jpg',
        'category' => 'activity',
        'description' => 'เสื้อกิจกรรมโรงเรียน สีสันสดใส เหมาะสำหรับกิจกรรมต่างๆ',
        'features' => ['ผ้าคอตตอน', 'นุ่มสบาย', 'สีสดใส', 'ราคาประหยัด'],
        'colors' => ['แดง', 'เขียว', 'ฟ้า', 'ส้ม', 'ม่วง'],
        'sizes' => ['XS', 'S', 'M', 'L', 'XL'],
        'rating' => 4.5,
        'reviews' => 234,
        'featured' => true
    ],
    [
        'id' => 4,
        'name' => 'เสื้อกีฬาสีโรงเรียน',
        'price' => 320,
        'image' => 'uploads/products/school-sport.jpg',
        'category' => 'school',
        'description' => 'เสื้อกีฬาสีโรงเรียน ตามมาตรฐานกระทรวงศึกษาธิการ',
        'features' => ['ตามมาตรฐาน กศ.', 'ผ้าคุณภาพ', 'ทนทาน', 'ราคาเป็นมิตร'],
        'colors' => ['แดง', 'เหลือง', 'เขียว', 'น้ำเงิน'],
        'sizes' => ['XS', 'S', 'M', 'L', 'XL'],
        'rating' => 4.7,
        'reviews' => 178,
        'featured' => false
    ],
    [
        'id' => 5,
        'name' => 'เสื้อหน่วยงานราชการ',
        'price' => 380,
        'image' => 'uploads/products/government.jpg',
        'category' => 'government',
        'description' => 'เสื้อหน่วยงานราชการ ดีไซน์เป็นทางการ คุณภาพพรีเมียม',
        'features' => ['ผ้าพรีเมียม', 'ดีไซน์เป็นทางการ', 'ทนทาน', 'ไม่ยับง่าย'],
        'colors' => ['น้ำเงิน', 'ขาว', 'เทา'],
        'sizes' => ['S', 'M', 'L', 'XL', 'XXL'],
        'rating' => 4.9,
        'reviews' => 67,
        'featured' => true
    ],
    [
        'id' => 6,
        'name' => 'เสื้อดีไซน์พิเศษ',
        'price' => 450,
        'image' => 'uploads/products/custom-design.jpg',
        'category' => 'custom',
        'description' => 'เสื้อดีไซน์พิเศษ ออกแบบตามต้องการ ไม่ซ้ำใคร',
        'features' => ['ออกแบบเอง', 'ไม่ซ้ำใคร', 'คุณภาพสูง', 'บริการครบวงจร'],
        'colors' => ['ตามสั่ง'],
        'sizes' => ['ทุกไซส์'],
        'rating' => 5.0,
        'reviews' => 45,
        'featured' => true
    ]
];

$categories = [
    'all' => 'ทั้งหมด',
    'sport' => 'เสื้อกีฬา',
    'company' => 'เสื้อบริษัท',
    'activity' => 'เสื้อกิจกรรม',
    'school' => 'เสื้อโรงเรียน',
    'government' => 'เสื้อราชการ',
    'custom' => 'ดีไซน์พิเศษ'
];

$selected_category = $_GET['category'] ?? 'all';
$search_query = $_GET['search'] ?? '';
$sort_by = $_GET['sort'] ?? 'name';

// กรองสินค้าตามหมวดหมู่
$filtered_products = $selected_category === 'all' 
    ? $products 
    : array_filter($products, function($product) use ($selected_category) {
        return $product['category'] === $selected_category;
    });

// กรองตามคำค้นหา
if (!empty($search_query)) {
    $filtered_products = array_filter($filtered_products, function($product) use ($search_query) {
        return stripos($product['name'], $search_query) !== false || 
               stripos($product['description'], $search_query) !== false;
    });
}

// เรียงลำดับ
usort($filtered_products, function($a, $b) use ($sort_by) {
    switch ($sort_by) {
        case 'price_low':
            return $a['price'] - $b['price'];
        case 'price_high':
            return $b['price'] - $a['price'];
        case 'rating':
            return $b['rating'] - $a['rating'];
        case 'popular':
            return $b['reviews'] - $a['reviews'];
        default:
            return strcmp($a['name'], $b['name']);
    }
});
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>สินค้า - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="เลือกสินค้าเสื้อกีฬาคุณภาพสูงจาก GT Sport Design พร้อมบริการออกแบบตามต้องการ">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/banner.css" rel="stylesheet">
    
    <style>
        /* Products Page Styles */
        .products-hero {
            background: var(--gradient-primary);
            color: white;
            padding: 100px 0 60px;
            position: relative;
            overflow: hidden;
        }
        
        .products-hero::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            transform: translate(100px, -100px);
        }
        
        .filter-section {
            background: white;
            padding: 30px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 80px;
            z-index: 100;
        }
        
        .category-filter {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        
        .category-btn {
            padding: 10px 20px;
            border: 2px solid #dee2e6;
            border-radius: 25px;
            text-decoration: none;
            color: var(--dark-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .category-btn:hover,
        .category-btn.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        .search-box {
            position: relative;
        }
        
        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            z-index: 10;
        }
        
        .search-box input {
            padding-left: 45px;
            border-radius: 25px;
            border: 2px solid #dee2e6;
        }
        
        .search-box input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(238, 80, 27, 0.25);
        }
        
        .products-grid {
            padding: 40px 0;
        }
        
        .product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 30px;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .product-image {
            height: 250px;
            background: var(--light-gray);
            position: relative;
            overflow: hidden;
        }
        
        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        
        .product-card:hover .product-image img {
            transform: scale(1.1);
        }
        
        .product-image .placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            font-size: 4rem;
            color: #dee2e6;
        }
        
        .featured-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            background: var(--gradient-primary);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 10;
        }
        
        .product-info {
            padding: 25px;
        }
        
        .product-category {
            color: var(--primary-color);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 10px;
        }
        
        .product-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
        }
        
        .product-description {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .product-features {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .feature-tag {
            background: var(--light-gray);
            color: var(--dark-color);
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.75rem;
        }
        
        .product-rating {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .stars {
            color: #ffc107;
        }
        
        .rating-text {
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .product-price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        
        .btn-design {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-design:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(238, 80, 27, 0.4);
            color: white;
        }
        
        .results-info {
            background: var(--light-gray);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .no-products {
            text-align: center;
            padding: 80px 20px;
        }
        
        .no-products i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 20px;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .products-hero {
                padding: 80px 0 40px;
            }
            
            .filter-section {
                position: static;
            }
            
            .category-filter {
                justify-content: center;
            }
            
            .category-btn {
                font-size: 0.9rem;
                padding: 8px 15px;
            }
        }
    </style>
</head>

<body>
<?php include 'includes/header.php'; ?>

<!-- Products Hero Section -->
<section class="products-hero" data-aos="fade-in">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3" data-aos="fade-up">
                    สินค้าของเรา
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="200">
                    เลือกสินค้าเสื้อกีฬาคุณภาพสูงสำหรับการออกแบบของคุณ<br>
                    พร้อมบริการครบวงจรจากการออกแบบไปจนถึงการจัดส่ง
                </p>
                <div class="d-flex flex-wrap gap-3" data-aos="fade-up" data-aos-delay="400">
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-check me-2"></i>คุณภาพพรีเมียม
                    </span>
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-palette me-2"></i>ออกแบบได้ตามใจ
                    </span>
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-shipping-fast me-2"></i>จัดส่งรวดเร็ว
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-center" data-aos="fade-left">
                <i class="fas fa-tshirt" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="filter-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <!-- Search -->
                <form method="GET" class="search-box" data-aos="fade-right">
                    <input type="hidden" name="category" value="<?php echo $selected_category; ?>">
                    <input type="hidden" name="sort" value="<?php echo $sort_by; ?>">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" name="search" class="form-control"
                           placeholder="ค้นหาสินค้า..."
                           value="<?php echo htmlspecialchars($search_query); ?>">
                </form>
            </div>
            <div class="col-lg-6">
                <!-- Sort -->
                <form method="GET" class="d-flex justify-content-end" data-aos="fade-left">
                    <input type="hidden" name="category" value="<?php echo $selected_category; ?>">
                    <input type="hidden" name="search" value="<?php echo htmlspecialchars($search_query); ?>">
                    <select name="sort" class="form-select w-auto" onchange="this.form.submit()">
                        <option value="name" <?php echo $sort_by === 'name' ? 'selected' : ''; ?>>เรียงตามชื่อ</option>
                        <option value="price_low" <?php echo $sort_by === 'price_low' ? 'selected' : ''; ?>>ราคาต่ำ - สูง</option>
                        <option value="price_high" <?php echo $sort_by === 'price_high' ? 'selected' : ''; ?>>ราคาสูง - ต่ำ</option>
                        <option value="rating" <?php echo $sort_by === 'rating' ? 'selected' : ''; ?>>คะแนนสูงสุด</option>
                        <option value="popular" <?php echo $sort_by === 'popular' ? 'selected' : ''; ?>>ยอดนิยม</option>
                    </select>
                </form>
            </div>
        </div>

        <!-- Category Filter -->
        <div class="category-filter" data-aos="fade-up">
            <?php foreach ($categories as $key => $name): ?>
            <a href="?category=<?php echo $key; ?>&search=<?php echo urlencode($search_query); ?>&sort=<?php echo $sort_by; ?>"
               class="category-btn <?php echo $selected_category === $key ? 'active' : ''; ?>">
                <i class="fas fa-<?php echo $key === 'all' ? 'th-large' : ($key === 'sport' ? 'running' : ($key === 'company' ? 'building' : ($key === 'activity' ? 'users' : ($key === 'school' ? 'graduation-cap' : ($key === 'government' ? 'landmark' : 'palette'))))); ?> me-2"></i>
                <?php echo $name; ?>
            </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Products Section -->
<section class="products-grid">
    <div class="container">
        <!-- Results Info -->
        <?php if (!empty($filtered_products)): ?>
        <div class="results-info" data-aos="fade-up">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="mb-0">
                        <i class="fas fa-box me-2"></i>
                        พบสินค้า <?php echo count($filtered_products); ?> รายการ
                        <?php if (!empty($search_query)): ?>
                        สำหรับ "<strong><?php echo htmlspecialchars($search_query); ?></strong>"
                        <?php endif; ?>
                        <?php if ($selected_category !== 'all'): ?>
                        ในหมวด "<strong><?php echo $categories[$selected_category]; ?></strong>"
                        <?php endif; ?>
                    </h6>
                </div>
                <div class="col-md-4 text-end">
                    <small class="text-muted">
                        เรียงตาม: <?php
                        echo match($sort_by) {
                            'price_low' => 'ราคาต่ำ - สูง',
                            'price_high' => 'ราคาสูง - ต่ำ',
                            'rating' => 'คะแนนสูงสุด',
                            'popular' => 'ยอดนิยม',
                            default => 'ชื่อสินค้า'
                        };
                        ?>
                    </small>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="row">
            <?php foreach ($filtered_products as $index => $product): ?>
            <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                <div class="product-card">
                    <?php if ($product['featured']): ?>
                    <div class="featured-badge">
                        <i class="fas fa-star me-1"></i>แนะนำ
                    </div>
                    <?php endif; ?>

                    <div class="product-image">
                        <img src="<?php echo $product['image']; ?>"
                             alt="<?php echo htmlspecialchars($product['name']); ?>"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="placeholder" style="display: none;">
                            <i class="fas fa-tshirt"></i>
                        </div>
                    </div>

                    <div class="product-info">
                        <div class="product-category">
                            <?php echo $categories[$product['category']]; ?>
                        </div>

                        <h5 class="product-title">
                            <?php echo htmlspecialchars($product['name']); ?>
                        </h5>

                        <p class="product-description">
                            <?php echo htmlspecialchars($product['description']); ?>
                        </p>

                        <div class="product-features">
                            <?php foreach (array_slice($product['features'], 0, 3) as $feature): ?>
                            <span class="feature-tag"><?php echo $feature; ?></span>
                            <?php endforeach; ?>
                        </div>

                        <div class="product-rating">
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star<?php echo $i <= floor($product['rating']) ? '' : ($i - 0.5 <= $product['rating'] ? '-half-alt' : ' text-muted'); ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <span class="rating-text">
                                <?php echo $product['rating']; ?> (<?php echo $product['reviews']; ?> รีวิว)
                            </span>
                        </div>

                        <div class="product-price">
                            ฿<?php echo number_format($product['price']); ?>
                        </div>

                        <a href="shirt-design.php?product_id=<?php echo $product['id']; ?>"
                           class="btn btn-design">
                            <i class="fas fa-palette me-2"></i>เริ่มออกแบบ
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <?php else: ?>
        <!-- No Products Found -->
        <div class="no-products" data-aos="fade-up">
            <i class="fas fa-search"></i>
            <h3>ไม่พบสินค้า</h3>
            <p class="text-muted mb-4">
                <?php if (!empty($search_query)): ?>
                ไม่พบสินค้าที่ตรงกับ "<?php echo htmlspecialchars($search_query); ?>"
                <?php else: ?>
                ไม่มีสินค้าในหมวดหมู่นี้
                <?php endif; ?>
            </p>
            <div class="d-flex justify-content-center gap-3">
                <a href="products.php" class="btn btn-primary">
                    <i class="fas fa-refresh me-2"></i>ดูสินค้าทั้งหมด
                </a>
                <a href="shirt-design.php" class="btn btn-outline-primary">
                    <i class="fas fa-paint-brush me-2"></i>ออกแบบเอง
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5" style="background: var(--light-gray);">
    <div class="container text-center" data-aos="fade-up">
        <h2 class="mb-3">ไม่พบสินค้าที่ต้องการ?</h2>
        <p class="lead mb-4">เราสามารถออกแบบและผลิตเสื้อตามความต้องการของคุณได้</p>
        <div class="d-flex justify-content-center gap-3">
            <a href="shirt-design.php" class="btn btn-primary btn-lg">
                <i class="fas fa-paint-brush me-2"></i>ออกแบบเสื้อเอง
            </a>
            <a href="contact.php" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-phone me-2"></i>ปรึกษาฟรี
            </a>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<script>
// Initialize AOS
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// Auto-submit search form
document.querySelector('.search-box input[name="search"]').addEventListener('input', function() {
    clearTimeout(this.searchTimeout);
    this.searchTimeout = setTimeout(() => {
        this.form.submit();
    }, 1000);
});

// Smooth scrolling for category links
document.querySelectorAll('.category-btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
        // Add loading state
        this.style.opacity = '0.7';
        this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังโหลด...';
    });
});

// Product card hover effects
document.querySelectorAll('.product-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px) scale(1.02)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>

</body>
</html>
