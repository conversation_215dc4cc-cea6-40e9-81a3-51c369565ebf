<?php
session_start();
require_once './config/database.php';

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['customer_logged_in'])) {
    header("Location: login.php");
    exit;
}

$pdo = getDbConnection();
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// ตัวแปรสำหรับการค้นหาและกรอง
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 9; // แสดง 9 การออกแบบต่อหน้า (3x3 grid)
$offset = ($page - 1) * $limit;

// สร้าง SQL สำหรับการค้นหาและกรอง
$where_conditions = ["cd.customer_id = ?"];
$params = [$customer_id];

if ($search) {
    $where_conditions[] = "(cd.design_name LIKE ? OR p.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "cd.status = ?";
    $params[] = $status_filter;
}

if ($date_from) {
    $where_conditions[] = "DATE(cd.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(cd.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// ดึงข้อมูลการออกแบบ
try {
    // นับจำนวนทั้งหมด
    $count_sql = "
        SELECT COUNT(*)
        FROM customer_designs cd
        LEFT JOIN products p ON cd.product_id = p.id
        WHERE $where_clause
    ";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_records = $stmt->fetchColumn();
    $total_pages = ceil($total_records / $limit);

    // ดึงข้อมูลสำหรับแสดงผล
    $sql = "
        SELECT cd.*, p.name as product_name,
               pi.file_id as product_image
        FROM customer_designs cd
        LEFT JOIN products p ON cd.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.image_type = 'main'
        WHERE $where_clause
        ORDER BY cd.created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $designs = $stmt->fetchAll();

} catch (Exception $e) {
    $designs = [];
    $total_records = 0;
    $total_pages = 0;
    $error = "เกิดข้อผิดพลาด: " . $e->getMessage();
}

// ฟังก์ชันแสดงสถานะ
function getDesignStatusInfo($status) {
    $statuses = [
        'pending' => ['text' => 'รอตรวจสอบ', 'class' => 'warning', 'icon' => 'clock'],
        'in_progress' => ['text' => 'กำลังดำเนินการ', 'class' => 'info', 'icon' => 'cogs'],
        'approved' => ['text' => 'อนุมัติ', 'class' => 'success', 'icon' => 'check-circle'],
        'rejected' => ['text' => 'ปฏิเสธ', 'class' => 'danger', 'icon' => 'times-circle'],
        'revision' => ['text' => 'ต้องแก้ไข', 'class' => 'warning', 'icon' => 'edit']
    ];
    return $statuses[$status] ?? ['text' => 'ไม่ระบุ', 'class' => 'secondary', 'icon' => 'question'];
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>การออกแบบของฉัน - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: white !important;
        }

        .sidebar {
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 15px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar .nav-link {
            color: #6c757d;
            font-weight: 500;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .page-subtitle {
            color: #6c757d;
            margin-bottom: 0;
        }

        .filter-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .filter-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #11be97;
            box-shadow: 0 0 0 0.2rem rgba(17, 190, 151, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-outline-secondary {
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 500;
        }

        .design-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .design-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .design-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .design-image {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .design-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .design-image .placeholder {
            color: #dee2e6;
            font-size: 3rem;
        }

        .design-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .design-content {
            padding: 20px;
        }

        .design-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .design-product {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .design-date {
            color: #6c757d;
            font-size: 0.8rem;
            margin-bottom: 15px;
        }

        .design-actions {
            display: flex;
            gap: 10px;
        }

        .design-actions .btn {
            flex: 1;
            padding: 8px 12px;
            font-size: 0.85rem;
            border-radius: 8px;
        }

        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-flex;
            align-items: center;
        }

        .status-badge i {
            margin-right: 5px;
        }

        .pagination {
            justify-content: center;
            margin-top: 30px;
        }

        .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #11be97;
        }

        .page-link:hover {
            background: #11be97;
            color: white;
        }

        .page-item.active .page-link {
            background: #11be97;
            border-color: #11be97;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .empty-state i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 20px;
        }

        .stats-summary {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #11be97;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-bottom: 20px;
            }

            .main-content {
                padding: 15px;
            }

            .design-grid {
                grid-template-columns: 1fr;
            }

            .filter-card .row {
                gap: 15px;
            }

            .filter-card .col-md-3,
            .filter-card .col-md-2 {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= htmlspecialchars($customer_name) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="customer_profile.php">
                                <i class="fas fa-user-edit me-2"></i>แก้ไขโปรไฟล์
                            </a></li>
                            <li><a class="dropdown-item" href="customer_settings.php">
                                <i class="fas fa-cog me-2"></i>ตั้งค่า
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3">
                <div class="sidebar">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="customer_dashboard.php">
                            <i class="fas fa-tachometer-alt me-3"></i>แดชบอร์ด
                        </a>
                        <a class="nav-link" href="customer_orders.php">
                            <i class="fas fa-shopping-cart me-3"></i>คำสั่งซื้อของฉัน
                        </a>
                        <a class="nav-link active" href="customer_designs.php">
                            <i class="fas fa-palette me-3"></i>การออกแบบของฉัน
                        </a>
                        <a class="nav-link" href="customer_profile.php">
                            <i class="fas fa-user-edit me-3"></i>ข้อมูลส่วนตัว
                        </a>
                        <a class="nav-link" href="customer_settings.php">
                            <i class="fas fa-cog me-3"></i>ตั้งค่า
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="main-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="page-title">การออกแบบของฉัน</h1>
                                <p class="page-subtitle">จัดการและติดตามการออกแบบทั้งหมดของคุณ</p>
                            </div>
                            <div>
                                <a href="design.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>เริ่มออกแบบใหม่
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Summary -->
                    <?php
                    try {
                        $stmt = $pdo->prepare("
                            SELECT
                                COUNT(*) as total,
                                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                                SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
                                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
                            FROM customer_designs
                            WHERE customer_id = ?
                        ");
                        $stmt->execute([$customer_id]);
                        $stats = $stmt->fetch();
                    } catch (Exception $e) {
                        $stats = ['total' => 0, 'pending' => 0, 'in_progress' => 0, 'approved' => 0, 'rejected' => 0];
                    }
                    ?>

                    <div class="stats-summary">
                        <div class="stats-row">
                            <div class="stat-item">
                                <div class="stat-number"><?= $stats['total'] ?></div>
                                <div class="stat-label">ทั้งหมด</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?= $stats['pending'] ?></div>
                                <div class="stat-label">รอตรวจสอบ</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?= $stats['in_progress'] ?></div>
                                <div class="stat-label">กำลังดำเนินการ</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?= $stats['approved'] ?></div>
                                <div class="stat-label">อนุมัติ</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?= $stats['rejected'] ?></div>
                                <div class="stat-label">ปฏิเสธ</div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="filter-card">
                        <h5 class="filter-title">ค้นหาและกรองข้อมูล</h5>
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" name="search"
                                           placeholder="ค้นหาชื่อการออกแบบ/สินค้า"
                                           value="<?= htmlspecialchars($search) ?>">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" name="status">
                                        <option value="">สถานะทั้งหมด</option>
                                        <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>รอตรวจสอบ</option>
                                        <option value="in_progress" <?= $status_filter === 'in_progress' ? 'selected' : '' ?>>กำลังดำเนินการ</option>
                                        <option value="approved" <?= $status_filter === 'approved' ? 'selected' : '' ?>>อนุมัติ</option>
                                        <option value="rejected" <?= $status_filter === 'rejected' ? 'selected' : '' ?>>ปฏิเสธ</option>
                                        <option value="revision" <?= $status_filter === 'revision' ? 'selected' : '' ?>>ต้องแก้ไข</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input type="date" class="form-control" name="date_from"
                                           value="<?= htmlspecialchars($date_from) ?>" placeholder="จากวันที่">
                                </div>
                                <div class="col-md-2">
                                    <input type="date" class="form-control" name="date_to"
                                           value="<?= htmlspecialchars($date_to) ?>" placeholder="ถึงวันที่">
                                </div>
                                <div class="col-md-3">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>ค้นหา
                                        </button>
                                        <a href="customer_designs.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>ล้าง
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Designs Grid -->
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($designs): ?>
                        <div class="design-grid">
                            <?php foreach ($designs as $design): ?>
                                <?php $status_info = getDesignStatusInfo($design['status']); ?>
                                <div class="design-card" onclick="viewDesignDetail(<?= $design['id'] ?>)">
                                    <div class="design-image">
                                        <?php if ($design['design_file']): ?>
                                            <img src="../uploads/designs/<?= htmlspecialchars($design['design_file']) ?>"
                                                 alt="Design" loading="lazy">
                                        <?php else: ?>
                                            <i class="fas fa-palette placeholder"></i>
                                        <?php endif; ?>
                                        <span class="status-badge bg-<?= $status_info['class'] ?> text-white">
                                            <i class="fas fa-<?= $status_info['icon'] ?>"></i>
                                            <?= $status_info['text'] ?>
                                        </span>
                                    </div>
                                    <div class="design-content">
                                        <h6 class="design-title">
                                            <?= htmlspecialchars($design['design_name'] ?: 'การออกแบบ #' . $design['id']) ?>
                                        </h6>
                                        <p class="design-product">
                                            <?= htmlspecialchars($design['product_name'] ?: 'สินค้าที่ลบแล้ว') ?>
                                        </p>
                                        <p class="design-date">
                                            <?= date('d/m/Y H:i น.', strtotime($design['created_at'])) ?>
                                        </p>
                                        <div class="design-actions">
                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                    onclick="event.stopPropagation(); viewDesignDetail(<?= $design['id'] ?>)">
                                                <i class="fas fa-eye"></i> ดู
                                            </button>
                                            <?php if ($design['status'] === 'pending' || $design['status'] === 'revision'): ?>
                                                <button type="button" class="btn btn-outline-warning btn-sm"
                                                        onclick="event.stopPropagation(); editDesign(<?= $design['id'] ?>)">
                                                    <i class="fas fa-edit"></i> แก้ไข
                                                </button>
                                            <?php endif; ?>
                                            <?php if ($design['status'] === 'approved'): ?>
                                                <button type="button" class="btn btn-success btn-sm"
                                                        onclick="event.stopPropagation(); orderDesign(<?= $design['id'] ?>)">
                                                    <i class="fas fa-shopping-cart"></i> สั่งซื้อ
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Designs pagination">
                                <ul class="pagination">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $date_from ? '&date_from=' . urlencode($date_from) : '' ?><?= $date_to ? '&date_to=' . urlencode($date_to) : '' ?>">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $date_from ? '&date_from=' . urlencode($date_from) : '' ?><?= $date_to ? '&date_to=' . urlencode($date_to) : '' ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $date_from ? '&date_from=' . urlencode($date_from) : '' ?><?= $date_to ? '&date_to=' . urlencode($date_to) : '' ?>">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-palette"></i>
                            <h4>ไม่พบการออกแบบ</h4>
                            <p class="text-muted">คุณยังไม่มีการออกแบบ หรือไม่พบการออกแบบที่ตรงกับการค้นหา</p>
                            <a href="design.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เริ่มออกแบบใหม่
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Design Detail Modal -->
    <div class="modal fade" id="designDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รายละเอียดการออกแบบ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="designDetailContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // ฟังก์ชันดูรายละเอียดการออกแบบ
        function viewDesignDetail(designId) {
            // โหลดรายละเอียดการออกแบบ
            fetch(`design_detail_api.php?id=${designId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('designDetailContent').innerHTML = data.html;
                        new bootstrap.Modal(document.getElementById('designDetailModal')).show();
                    } else {
                        Swal.fire('เกิดข้อผิดพลาด!', data.message, 'error');
                    }
                })
                .catch(error => {
                    Swal.fire('เกิดข้อผิดพลาด!', 'ไม่สามารถโหลดข้อมูลได้', 'error');
                });
        }

        // ฟังก์ชันแก้ไขการออกแบบ
        function editDesign(designId) {
            window.location.href = `design.php?edit=${designId}`;
        }

        // ฟังก์ชันสั่งซื้อการออกแบบ
        function orderDesign(designId) {
            Swal.fire({
                title: 'สั่งซื้อการออกแบบนี้',
                text: 'คุณต้องการสั่งซื้อการออกแบบนี้หรือไม่?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#11be97',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'สั่งซื้อ',
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = `checkout.php?design_id=${designId}`;
                }
            });
        }
    </script>
</body>
</html>
