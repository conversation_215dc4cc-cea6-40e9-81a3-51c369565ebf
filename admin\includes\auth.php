<?php
/**
 * ไฟล์สำหรับจัดการการตรวจสอบสิทธิ์ของผู้ดูแลระบบ
 */

if (!function_exists('requireAdminLogin')) {
    /**
     * ตรวจสอบว่ามีการเข้าสู่ระบบของแอดมินหรือไม่
     * ถ้าไม่มีจะ redirect ไปยังหน้า login
     */
    function requireAdminLogin() {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            // บันทึก URL ที่พยายามเข้าถึง เพื่อ redirect กลับมาหลังจาก login
            $_SESSION['admin_redirect_after_login'] = $_SERVER['REQUEST_URI'];
            
            header('Location: login.php');
            exit;
        }
    }
}

if (!function_exists('requireSuperAdmin')) {
    /**
     * ตรวจสอบว่าเป็น Super Admin หรือไม่
     * ถ้าไม่ใช่จะแสดงข้อความแจ้งเตือนและ redirect กลับไปหน้าแดชบอร์ด
     */
    function requireSuperAdmin() {
        requireAdminLogin();
        
        if (!isset($_SESSION['admin_role']) || $_SESSION['admin_role'] !== 'super_admin') {
            $_SESSION['admin_error'] = 'คุณไม่มีสิทธิ์เข้าถึงหน้านี้ เฉพาะซูเปอร์แอดมินเท่านั้น';
            header('Location: index.php');
            exit;
        }
    }
}

if (!function_exists('getCurrentAdminId')) {
    /**
     * ดึง ID ของแอดมินที่กำลังใช้งานอยู่
     */
    function getCurrentAdminId() {
        return $_SESSION['admin_id'] ?? null;
    }
}

if (!function_exists('getCurrentAdminName')) {
    /**
     * ดึงชื่อของแอดมินที่กำลังใช้งานอยู่
     */
    function getCurrentAdminName() {
        return $_SESSION['admin_name'] ?? 'ไม่ระบุชื่อ';
    }
}

if (!function_exists('getCurrentAdminRole')) {
    /**
     * ดึงบทบาทของแอดมินที่กำลังใช้งานอยู่
     */
    function getCurrentAdminRole() {
        return $_SESSION['admin_role'] ?? 'admin';
    }
}

if (!function_exists('getCurrentAdmin')) {
    /**
     * ดึงข้อมูลทั้งหมดของแอดมินที่กำลังใช้งานอยู่
     */
    function getCurrentAdmin() {
        return [
            'id' => getCurrentAdminId(),
            'name' => getCurrentAdminName(),
            'role' => getCurrentAdminRole(),
            'email' => $_SESSION['admin_email'] ?? 'ไม่ระบุอีเมล'
        ];
    }
}

if (!function_exists('isAdmin')) {
    /**
     * ตรวจสอบว่าเป็นแอดมินหรือไม่
     */
    function isAdmin() {
        return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
    }
}

if (!function_exists('isSuperAdmin')) {
    /**
     * ตรวจสอบว่าเป็น Super Admin หรือไม่
     */
    function isSuperAdmin() {
        return isAdmin() && isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === 'super_admin';
    }
}
