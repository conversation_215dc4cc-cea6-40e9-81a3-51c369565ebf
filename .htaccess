# GT-SportDesign Security Configuration
# Version: 1.0

# ป้อง<lemmaการเข้าไฟล์
<FilesMatch "^(database\.php|config\.php|\.env|\.git|\.htaccess)">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้อง<lemmaการแสดงรายการไฟล์
Options -Indexes

# ป้อง<lemmaการเข้าไฟล์ SQL
<FilesMatch "\.(sql|bak|config|dist|fla|inc|ini|log|psd|sh|swp)$">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้อง<lemmaการเข้าโฟลเดอร์ config
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteRule ^config/.*$ - [F,L]
  RewriteRule ^database/.*$ - [F,L]
</IfModule>

# เ<lemma่มด้วย HTTP headers
<IfModule mod_headers.c>
  Header set X-Content-Type-Options "nosniff"
  Header set X-XSS-Protection "1; mode=block"
  Header set X-Frame-Options "SAMEORIGIN"
  Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# <lemmaให้เข้า<lemmaไฟล์ PHP ในโฟลเดอร์<lemma
<Files "*.php">
  Order Allow,<PERSON><PERSON>
  Allow from all
</Files>


