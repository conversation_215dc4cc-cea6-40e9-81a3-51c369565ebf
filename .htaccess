# GT-SportDesign Security Configuration
# Version: 1.0

# ป้องกันการเข้าถึงไฟล์สำคัญ
<FilesMatch "^(database\.php|config\.php|\.env|\.git|\.htaccess)">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้องกันการแสดงรายการไฟล์
Options -Indexes

# ป้องกันการเข้าถึงไฟล์ SQL
<FilesMatch "\.(sql|bak|config|dist|fla|inc|ini|log|psd|sh|swp)$">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้องกันการเข้าถึงโฟลเดอร์ config
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteRule ^config/.*$ - [F,L]
  RewriteRule ^database/.*$ - [F,L]

  # URL Rewriting - Remove .php extension
  # Redirect .php URLs to clean URLs
  RewriteCond %{THE_REQUEST} /([^.]+)\.php [NC]
  RewriteRule ^ /%1? [NC,L,R=301]

  # Handle clean URLs
  RewriteCond %{REQUEST_FILENAME} !-d
  RewriteCond %{REQUEST_FILENAME} !-f
  RewriteRule ^([^\.]+)$ $1.php [NC,L]

  # Specific page redirects
  RewriteRule ^products/?$ products.php [NC,L]
  RewriteRule ^contact/?$ contact.php [NC,L]
  RewriteRule ^about/?$ about.php [NC,L]
  RewriteRule ^design/?$ shirt-design.php [NC,L]
  RewriteRule ^shirt-design/?$ shirt-design.php [NC,L]
  RewriteRule ^login/?$ login.php [NC,L]
  RewriteRule ^register/?$ register.php [NC,L]
  RewriteRule ^checkout/?$ checkout.php [NC,L]

  # Admin pages
  RewriteRule ^admin/?$ admin/index.php [NC,L]
  RewriteRule ^admin/([^/]+)/?$ admin/$1.php [NC,L]

  # Customer pages
  RewriteRule ^customer/([^/]+)/?$ customer_$1.php [NC,L]
</IfModule>

# เ<lemma่มด้วย HTTP headers
<IfModule mod_headers.c>
  Header set X-Content-Type-Options "nosniff"
  Header set X-XSS-Protection "1; mode=block"
  Header set X-Frame-Options "SAMEORIGIN"
  Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# <lemmaให้เข้า<lemmaไฟล์ PHP ในโฟลเดอร์<lemma
<Files "*.php">
  Order Allow,Deny
  Allow from all
</Files>

php_value upload_max_filesize 512M
php_value post_max_size 512M
php_value max_file_uploads 1000
