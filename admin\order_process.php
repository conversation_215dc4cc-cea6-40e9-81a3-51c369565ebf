<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Ensure only POST requests are processed
if ($_SERVER["REQUEST_METHOD"] != "POST" || !isset($_POST['action']) || !isset($_POST['order_id'])) {
    $_SESSION['message'] = 'Invalid request for order processing.';
    $_SESSION['message_type'] = 'error';
    header('Location: orders.php');
    exit;
}

$action = $_POST['action'];
$order_id = (int)$_POST['order_id'];

// --- SIMULATED DATABASE INTERACTIONS ---

if ($order_id <= 0) {
    $_SESSION['message'] = 'Order ID ไม่ถูกต้อง';
    $_SESSION['message_type'] = 'error';
    header('Location: orders.php');
    exit;
}

if ($action === 'update_status') {
    $order_status = isset($_POST['order_status']) ? sanitize_input($_POST['order_status']) : '';
    $payment_status = isset($_POST['payment_status']) ? sanitize_input($_POST['payment_status']) : '';

    // Basic validation
    if (empty($order_status) || empty($payment_status)) {
        $_SESSION['message'] = 'กรุณาเลือกสถานะออเดอร์และการชำระเงิน';
        $_SESSION['message_type'] = 'error';
        header('Location: order_view.php?id=' . $order_id);
        exit;
    }

    // **Simulate Database Update for order_status and payment_status**
    // In real app: UPDATE orders SET order_status = ?, payment_status = ? WHERE id = ?
    $_SESSION['message'] = "สถานะของออเดอร์ #{$order_id} ถูกอัปเดตเป็น '{$order_status}' และการชำระเงินเป็น '{$payment_status}' (จำลอง).";
    $_SESSION['message_type'] = 'success';
    header('Location: order_view.php?id=' . $order_id);
    exit;

} elseif ($action === 'update_shipping') {
    $shipping_provider = isset($_POST['shipping_provider']) ? sanitize_input($_POST['shipping_provider']) : '';
    $tracking_number = isset($_POST['tracking_number']) ? sanitize_input($_POST['tracking_number']) : '';

    // **Simulate Database Update for shipping info**
    // In real app: UPDATE orders SET shipping_provider = ?, tracking_number = ? WHERE id = ? (or update shipments table)
    $_SESSION['message'] = "ข้อมูลการจัดส่งสำหรับออเดอร์ #{$order_id} ถูกอัปเดต (จำลอง).";
    $_SESSION['message_type'] = 'success';
    header('Location: order_view.php?id=' . $order_id);
    exit;

} else {
    $_SESSION['message'] = 'การดำเนินการออเดอร์ไม่รู้จัก';
    $_SESSION['message_type'] = 'error';
    header('Location: order_view.php?id=' . $order_id);
    exit;
}
?>
