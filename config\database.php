<?php
/**
 * GT-SportDesign Database Configuration
 * Auto-generated on <?php echo date('Y-m-d H:i:s'); ?>
 */

function getDbConnection() {
    $host = 'localhost';
    $dbname = 'gtsportd_web2';
    $username = 'gtsportd_public';
    $password = 'Zgw5D2WnhZBruXWpr7Fg';
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return $pdo;
    } catch (PDOException $e) {
        die('Database connection failed: ' . $e->getMessage());
    }
}

// Global database connection
function getDB() {
    return getDbConnection();
}

// Backward compatibility
$pdo = getDbConnection();

