<?php
/**
 * GT-SportDesign Database Configuration
 * Auto-generated on 2025-01-27 10:00:00
 */

function getDbConnection() {
    $host = 'localhost';
    $dbname = 'gt_sportdesign';
    $username = 'root';
    $password = '';
    
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return $pdo;
    } catch (PDOException $e) {
        die('Database connection failed: ' . $e->getMessage());
    }
}

// Global database connection
function getDB() {
    return getDbConnection();
}

// Backward compatibility
$pdo = getDbConnection();

