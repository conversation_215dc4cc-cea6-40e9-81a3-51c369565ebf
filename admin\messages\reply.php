<?php
session_start();
require_once '../../includes/config.php';
require_once '../../includes/db.php';

$admin_page_title = "ตอบข้อความ";
include '../includes/header.php';

// Get message ID from URL
$message_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Validate message ID
if ($message_id <= 0) {
    $_SESSION['message'] = 'ID ข้อความไม่ต้อง';
    $_SESSION['message_type'] = 'error';
    header('Location: index.php');
    exit;
}

// In a real application, fetch message from database
// $sql = "SELECT * FROM contacts_messages WHERE id = ?";
// $params = [$message_id];
// $message = db_query_one($sql, $params);

// if (!$message) {
//     $_SESSION['message'] = 'ไม่พบข้อความ';
//     $_SESSION['message_type'] = 'error';
//     header('Location: index.php');
//     exit;
// }

// Simulated message data
$message = [
    'id' => $message_id,
    'sender_name' => 'ค้า อย่าง',
    'sender_email' => '<EMAIL>',
    'sender_phone' => '************',
    'subject' => 'สอบถามเกี่ยวกับเสื้อ',
    'message' => 'ส่ง/ค่ะ ผม/ค่ะ สนใจอยากเสื้อประมาณ 50 ต้องการให้โลโก้ ทด้านหน้า และชื่อ ด้าน อยากทราบว่าราคาประมาณเท่าไหร่ และใช้เวลานานแค่ไหน/คะ',
    'created_at' => '2023-05-15 10:30:45',
    'status' => 'read'
];

// Quick reply templates
$reply_templates = [
    'ส่ง/ค่ะ ขอบคุณที่สนใจของเรา',
    'ขอบคุณข้อความ เราจะตรวจสอบและต่อกโดยเร็ว',
    'เสื้อลาย Sublimation ราคาเริ่มต้น 299 บาท/ตัว ขึ้นอยู่กับจำนวนและความซับซ้อนของลาย',
    'ออกแบบเสื้อได้หน้าออกแบบของเรา ส่งไฟล์ลายมาทาง email ได้ค่ะ',
    'หากมีข้อสงสัยเพิ่มเติม สามารถต่อเราได้เบอร์ 02-123-4567 ในเวลาทำการ'
];
?>

<div class="admin-content">
    <div class="admin-content-header">
        <h2><?php echo $admin_page_title; ?></h2>
        <div class="action-buttons">
            <a href="index.php" class="btn btn-secondary">รายการ</a>
            <a href="view.php?id=<?php echo $message_id; ?>" class="btn btn-info">ข้อความ</a>
        </div>
    </div>
    
    <?php if (isset($_SESSION['message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['message_type']; ?>">
            <?php 
                echo $_SESSION['message']; 
                unset($_SESSION['message']);
                unset($_SESSION['message_type']);
            ?>
        </div>
    <?php endif; ?>

    <div class="admin-card">
        <div class="admin-card-header">
            <h3>ตอบ: <?php echo htmlspecialchars($message['sender_name']); ?></h3>
        </div>
        <div class="admin-card-body">
            <div class="original-message">
                <h4>ข้อความต้น:</h4>
                <div class="message-meta">
                    <p><strong>จาก:</strong> <?php echo htmlspecialchars($message['sender_name']); ?> (<?php echo htmlspecialchars($message['sender_email']); ?>)</p>
                    <?php if (!empty($message['sender_phone'])): ?>
                        <p><strong>เบอร์โทร:</strong> <?php echo htmlspecialchars($message['sender_phone']); ?></p>
                    <?php endif; ?>
                    <p><strong>เวลา:</strong> <?php echo $message['created_at']; ?></p>
                    <p><strong>ข้อ:</strong> <?php echo htmlspecialchars($message['subject']); ?></p>
                </div>
                <div class="message-content">
                    <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                </div>
            </div>
            
            <div class="reply-form">
                <h4>ข้อความตอบ:</h4>
                <form action="process.php" method="post">
                    <input type="hidden" name="action" value="reply">
                    <input type="hidden" name="message_id" value="<?php echo $message_id; ?>">
                    <input type="hidden" name="recipient_email" value="<?php echo htmlspecialchars($message['sender_email']); ?>">
                    
                    <div class="form-group">
                        <label for="reply_text">ข้อความ:</label>
                        <textarea id="reply_text" name="reply_text" class="form-control" rows="10" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>เทมเพลตข้อความ:</label>
                        <div class="template-container">
                            <?php foreach ($reply_templates as $index => $template): ?>
                                <button type="button" class="template-btn" data-template="<?php echo htmlspecialchars($template); ?>">
                                    เทมเพลต <?php echo $index + 1; ?>
                                </button>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">ส่งการตอบ</button>
                        <a href="index.php" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.original-message {
    margin-bottom: 30px;
}

.message-meta {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.message-content {
    background-color: #fff;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 5px;
    line-height: 1.6;
}

.reply-form {
    margin-top: 30px;
}

.form-group {
    margin-bottom: 15px;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-actions {
    margin-top: 20px;
    display: flex;
    gap: 10px;
}

.template-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
}

.template-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.template-btn:hover {
    background-color: #e0e0e0;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Template buttons functionality
    const templateButtons = document.querySelectorAll('.template-btn');
    const replyTextarea = document.getElementById('reply_text');
    
    templateButtons.forEach(button => {
        button.addEventListener('click', function() {
            const templateText = this.getAttribute('data-template');
            
            // If textarea is empty, just insert the template
            // Otherwise, add a new line and then the template
            if (replyTextarea.value === '') {
                replyTextarea.value = templateText;
            } else {
                replyTextarea.value += '\n\n' + templateText;
            }
            
            // Focus on the textarea and move cursor to the end
            replyTextarea.focus();
            replyTextarea.scrollTop = replyTextarea.scrollHeight;
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>


