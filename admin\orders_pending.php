<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ดึงข้อมูลคำสั่งซื้อที่รอดำเนินการ
$orders = [];
try {
    $stmt = $pdo->query("
        SELECT o.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.status = 'pending'
        ORDER BY o.created_at DESC
    ");
    $orders = $stmt->fetchAll();
} catch (Exception $e) {
    // ข้อมูลจำลอง
    $orders = [
        [
            'id' => 1,
            'order_number' => 'ORD-2024-001',
            'customer_name' => 'นาย ก ใจดี',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'total_amount' => 450.00,
            'status' => 'pending',
            'payment_status' => 'pending',
            'created_at' => date('Y-m-d H:i:s'),
            'notes' => 'ต้องการเสื้อสีแดง ไซส์ M'
        ],
        [
            'id' => 2,
            'order_number' => 'ORD-2024-002',
            'customer_name' => 'นางสาว ข สวยงาม',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'total_amount' => 680.00,
            'status' => 'pending',
            'payment_status' => 'paid',
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 hours')),
            'notes' => 'ชุดทีมฟุตบอล 11 ตัว'
        ]
    ];
}

// จัดการการอัปเดตสถานะ
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $order_id = (int)($_POST['order_id'] ?? 0);
    
    if ($action === 'confirm_order' && $order_id > 0) {
        try {
            $stmt = $pdo->prepare("
                UPDATE orders 
                SET status = 'processing', updated_at = NOW(), updated_by = ?
                WHERE id = ? AND status = 'pending'
            ");
            $stmt->execute([$_SESSION['admin_id'], $order_id]);
            
            $success = 'ยืนยันคำสั่งซื้อเรียบร้อยแล้ว';
        } catch (Exception $e) {
            $error = 'ไม่สามารถยืนยันคำสั่งซื้อได้: ' . $e->getMessage();
        }
    }
    
    if ($action === 'cancel_order' && $order_id > 0) {
        $cancel_reason = trim($_POST['cancel_reason'] ?? '');
        
        try {
            $stmt = $pdo->prepare("
                UPDATE orders 
                SET status = 'cancelled', cancel_reason = ?, updated_at = NOW(), updated_by = ?
                WHERE id = ? AND status = 'pending'
            ");
            $stmt->execute([$cancel_reason, $_SESSION['admin_id'], $order_id]);
            
            $success = 'ยกเลิกคำสั่งซื้อเรียบร้อยแล้ว';
        } catch (Exception $e) {
            $error = 'ไม่สามารถยกเลิกคำสั่งซื้อได้: ' . $e->getMessage();
        }
    }
    
    // รีเฟรชหน้า
    if ($success || $error) {
        header('Location: orders_pending.php?msg=' . urlencode($success ?: $error) . '&type=' . ($success ? 'success' : 'error'));
        exit();
    }
}

// แสดงข้อความ
if (isset($_GET['msg'])) {
    $message = $_GET['msg'];
    $message_type = $_GET['type'] ?? 'info';
    if ($message_type === 'success') {
        $success = $message;
    } else {
        $error = $message;
    }
}

function getPaymentStatusBadge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning">รอชำระ</span>',
        'paid' => '<span class="badge bg-success">ชำระแล้ว</span>',
        'failed' => '<span class="badge bg-danger">ชำระไม่สำเร็จ</span>'
    ];
    return $badges[$status] ?? '<span class="badge bg-secondary">' . $status . '</span>';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>คำสั่งซื้อรอดำเนินการ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .order-card {
            border-left: 5px solid #ffc107;
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
        .urgent-order {
            border-left-color: #dc3545 !important;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php" class="active"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">คำสั่งซื้อรอดำเนินการ</h4>
                <small class="text-muted">จัดการคำสั่งซื้อที่รอการยืนยัน</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="orders.php">คำสั่งซื้อ</a></li>
                    <li class="breadcrumb-item active">รอดำเนินการ</li>
                </ol>
            </nav>

            <!-- Order Status Tabs -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <a href="orders_pending.php" class="btn btn-warning w-100">
                                <i class="fas fa-clock me-2"></i>รอดำเนินการ
                                <span class="badge bg-white text-warning ms-2"><?php echo count($orders); ?></span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders_processing.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-cogs me-2"></i>กำลังผลิต
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders_completed.php" class="btn btn-outline-success w-100">
                                <i class="fas fa-check me-2"></i>เสร็จสิ้น
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-2"></i>ทั้งหมด
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Orders List -->
            <?php if (empty($orders)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-check fa-3x text-muted mb-3"></i>
                    <h3>ไม่มีคำสั่งซื้อรอดำเนินการ</h3>
                    <p class="text-muted">คำสั่งซื้อใหม่จะแสดงที่นี่</p>
                </div>
            <?php else: ?>
                <?php foreach ($orders as $order): ?>
                    <?php 
                    $is_urgent = (strtotime($order['created_at']) < strtotime('-24 hours'));
                    ?>
                <div class="card order-card <?php echo $is_urgent ? 'urgent-order' : ''; ?>">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0">
                                        <?php echo htmlspecialchars($order['order_number']); ?>
                                        <?php if ($is_urgent): ?>
                                            <span class="badge bg-danger ms-2">เร่งด่วน</span>
                                        <?php endif; ?>
                                    </h6>
                                    <span class="text-muted small">
                                        <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?>
                                    </span>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>ลูกค้า:</strong> <?php echo htmlspecialchars($order['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                        <strong>โทร:</strong> <?php echo htmlspecialchars($order['customer_phone'] ?? 'ไม่ระบุ'); ?><br>
                                        <strong>อีเมล:</strong> <?php echo htmlspecialchars($order['customer_email'] ?? 'ไม่ระบุ'); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>ยอดรวม:</strong> ฿<?php echo number_format($order['total_amount'], 2); ?><br>
                                        <strong>สถานะการชำระ:</strong> <?php echo getPaymentStatusBadge($order['payment_status'] ?? 'pending'); ?><br>
                                        <?php if (!empty($order['notes'])): ?>
                                            <strong>หมายเหตุ:</strong> <?php echo htmlspecialchars($order['notes']); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group-vertical w-100">
                                    <button class="btn btn-success btn-sm mb-2" onclick="confirmOrder(<?php echo $order['id']; ?>)">
                                        <i class="fas fa-check me-1"></i>ยืนยันคำสั่งซื้อ
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm mb-2" data-bs-toggle="modal" data-bs-target="#orderDetailModal<?php echo $order['id']; ?>">
                                        <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#cancelModal<?php echo $order['id']; ?>">
                                        <i class="fas fa-times me-1"></i>ยกเลิก
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Detail Modal -->
                <div class="modal fade" id="orderDetailModal<?php echo $order['id']; ?>" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">รายละเอียดคำสั่งซื้อ: <?php echo htmlspecialchars($order['order_number']); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>ข้อมูลลูกค้า</h6>
                                        <p>
                                            <strong>ชื่อ:</strong> <?php echo htmlspecialchars($order['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                            <strong>โทร:</strong> <?php echo htmlspecialchars($order['customer_phone'] ?? 'ไม่ระบุ'); ?><br>
                                            <strong>อีเมล:</strong> <?php echo htmlspecialchars($order['customer_email'] ?? 'ไม่ระบุ'); ?>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>ข้อมูลคำสั่งซื้อ</h6>
                                        <p>
                                            <strong>เลขที่:</strong> <?php echo htmlspecialchars($order['order_number']); ?><br>
                                            <strong>วันที่สั่ง:</strong> <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?><br>
                                            <strong>ยอดรวม:</strong> ฿<?php echo number_format($order['total_amount'], 2); ?><br>
                                            <strong>สถานะการชำระ:</strong> <?php echo getPaymentStatusBadge($order['payment_status'] ?? 'pending'); ?>
                                        </p>
                                    </div>
                                </div>
                                
                                <?php if (!empty($order['notes'])): ?>
                                <div class="mt-3">
                                    <h6>หมายเหตุ</h6>
                                    <p class="text-muted"><?php echo htmlspecialchars($order['notes']); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                                <button type="button" class="btn btn-success" onclick="confirmOrder(<?php echo $order['id']; ?>)">
                                    <i class="fas fa-check me-1"></i>ยืนยันคำสั่งซื้อ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cancel Modal -->
                <div class="modal fade" id="cancelModal<?php echo $order['id']; ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">ยกเลิกคำสั่งซื้อ</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form method="POST">
                                <div class="modal-body">
                                    <input type="hidden" name="action" value="cancel_order">
                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                    
                                    <div class="alert alert-warning">
                                        <strong>คำเตือน:</strong> คุณต้องการยกเลิกคำสั่งซื้อ <?php echo htmlspecialchars($order['order_number']); ?> หรือไม่?
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">เหตุผลในการยกเลิก</label>
                                        <textarea name="cancel_reason" class="form-control" rows="3" required placeholder="กรุณาระบุเหตุผลในการยกเลิก"></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                    <button type="submit" class="btn btn-danger">ยืนยันการยกเลิก</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Confirm Form -->
    <form id="confirmForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="confirm_order">
        <input type="hidden" name="order_id" id="confirmOrderId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmOrder(orderId) {
            if (confirm('คุณต้องการยืนยันคำสั่งซื้อนี้หรือไม่?\n\nคำสั่งซื้อจะถูกส่งไปยังขั้นตอนการผลิต')) {
                document.getElementById('confirmOrderId').value = orderId;
                document.getElementById('confirmForm').submit();
            }
        }
    </script>
</body>
</html>
