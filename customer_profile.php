<?php
session_start();
require_once './config/database.php';
require_once './includes/config.php';

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['customer_logged_in'])) {
    header("Location: login.php");
    exit;
}

$pdo = getDbConnection();
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

$success = '';
$error = '';

// ดึงข้อมูลลูกค้า
try {
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();

    if (!$customer) {
        header("Location: logout.php");
        exit;
    }
} catch (Exception $e) {
    die("เกิดข้อผิดพลาด: " . $e->getMessage());
}

// จัดการการอัพเดตข้อมูล
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_profile') {
        $name = trim($_POST['name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $phone = trim($_POST['phone'] ?? '');
        $address = trim($_POST['address'] ?? '');

        if (!$name || !$email || !$phone) {
            $error = "กรุณากรอกข้อมูลให้ครบถ้วน";
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "รูปแบบอีเมลไม่ถูกต้อง";
        } else {
            try {
                // ตรวจสอบว่าอีเมลซ้ำหรือไม่ (ยกเว้นอีเมลตัวเอง)
                $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ? AND id != ?");
                $stmt->execute([$email, $customer_id]);
                if ($stmt->fetch()) {
                    $error = "อีเมลนี้ถูกใช้งานแล้ว";
                } else {
                    // อัพเดตข้อมูล
                    $stmt = $pdo->prepare("
                        UPDATE customers
                        SET name = ?, email = ?, phone = ?, address = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$name, $email, $phone, $address, $customer_id]);

                    // อัพเดต session
                    $_SESSION['customer_name'] = $name;
                    $_SESSION['customer_email'] = $email;

                    // อัพเดตข้อมูลในตัวแปร $customer
                    $customer['name'] = $name;
                    $customer['email'] = $email;
                    $customer['phone'] = $phone;
                    $customer['address'] = $address;

                    $success = "อัพเดตข้อมูลเรียบร้อยแล้ว";
                }
            } catch (Exception $e) {
                $error = "เกิดข้อผิดพลาด: " . $e->getMessage();
            }
        }
    }

    elseif ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';

        if (!$current_password || !$new_password || !$confirm_password) {
            $error = "กรุณากรอกข้อมูลให้ครบถ้วน";
        } elseif (!verifyPassword($current_password, $customer['password'])) {
            $error = "รหัสผ่านปัจจุบันไม่ถูกต้อง";
        } elseif ($new_password !== $confirm_password) {
            $error = "รหัสผ่านใหม่ไม่ตรงกัน";
        } elseif (strlen($new_password) < 6) {
            $error = "รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร";
        } else {
            try {
                $hashedPassword = hashPassword($new_password);
                $stmt = $pdo->prepare("
                    UPDATE customers
                    SET password = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$hashedPassword, $customer_id]);

                $success = "เปลี่ยนรหัสผ่านเรียบร้อยแล้ว";
            } catch (Exception $e) {
                $error = "เกิดข้อผิดพลาด: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ข้อมูลส่วนตัว - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: white !important;
        }

        .sidebar {
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 15px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar .nav-link {
            color: #6c757d;
            font-weight: 500;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .page-subtitle {
            color: #6c757d;
            margin-bottom: 0;
        }

        .profile-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            margin-right: 25px;
        }

        .profile-info h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .profile-info p {
            color: #6c757d;
            margin-bottom: 5px;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 15px;
            color: #11be97;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            height: auto;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #11be97;
            box-shadow: 0 0 0 0.2rem rgba(17, 190, 151, 0.25);
        }

        .form-floating > label {
            padding: 15px 20px;
            color: #6c757d;
        }

        .btn-primary {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(17, 190, 151, 0.3);
        }

        .btn-outline-danger {
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
        }

        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }

        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            margin-top: 5px;
        }

        .strength-fill {
            height: 100%;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #11be97;
        }

        .stat-label {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-bottom: 20px;
            }

            .main-content {
                padding: 15px;
            }

            .profile-header {
                flex-direction: column;
                text-align: center;
            }

            .profile-avatar {
                margin-right: 0;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= htmlspecialchars($customer_name) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="customer_profile.php">
                                <i class="fas fa-user-edit me-2"></i>แก้ไขโปรไฟล์
                            </a></li>
                            <li><a class="dropdown-item" href="customer_settings.php">
                                <i class="fas fa-cog me-2"></i>ตั้งค่า
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3">
                <div class="sidebar">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="customer_dashboard.php">
                            <i class="fas fa-tachometer-alt me-3"></i>แดชบอร์ด
                        </a>
                        <a class="nav-link" href="customer_orders.php">
                            <i class="fas fa-shopping-cart me-3"></i>คำสั่งซื้อของฉัน
                        </a>
                        <a class="nav-link" href="customer_designs.php">
                            <i class="fas fa-palette me-3"></i>การออกแบบของฉัน
                        </a>
                        <a class="nav-link active" href="customer_profile.php">
                            <i class="fas fa-user-edit me-3"></i>ข้อมูลส่วนตัว
                        </a>
                        <a class="nav-link" href="customer_settings.php">
                            <i class="fas fa-cog me-3"></i>ตั้งค่า
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="main-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <h1 class="page-title">ข้อมูลส่วนตัว</h1>
                        <p class="page-subtitle">จัดการข้อมูลส่วนตัวและรหัสผ่านของคุณ</p>
                    </div>

                    <!-- แสดงข้อความแจ้งเตือน -->
                    <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($success): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Profile Header -->
                    <div class="profile-card">
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <?= strtoupper(substr($customer['name'], 0, 1)) ?>
                            </div>
                            <div class="profile-info">
                                <h4><?= htmlspecialchars($customer['name']) ?></h4>
                                <p><i class="fas fa-envelope me-2"></i><?= htmlspecialchars($customer['email']) ?></p>
                                <p><i class="fas fa-phone me-2"></i><?= htmlspecialchars($customer['phone']) ?></p>
                                <p><i class="fas fa-calendar me-2"></i>สมาชิกตั้งแต่ <?= date('d/m/Y', strtotime($customer['created_at'])) ?></p>
                            </div>
                        </div>

                        <!-- Statistics -->
                        <?php
                        try {
                            // ดึงสถิติของลูกค้า
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE customer_id = ?");
                            $stmt->execute([$customer_id]);
                            $total_orders = $stmt->fetchColumn();

                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM customer_designs WHERE customer_id = ?");
                            $stmt->execute([$customer_id]);
                            $total_designs = $stmt->fetchColumn();

                            $stmt = $pdo->prepare("SELECT SUM(total_amount) FROM orders WHERE customer_id = ? AND status != 'cancelled'");
                            $stmt->execute([$customer_id]);
                            $total_spent = $stmt->fetchColumn() ?: 0;
                        } catch (Exception $e) {
                            $total_orders = $total_designs = $total_spent = 0;
                        }
                        ?>

                        <div class="stats-row">
                            <div class="stat-card">
                                <div class="stat-number"><?= number_format($total_orders) ?></div>
                                <div class="stat-label">คำสั่งซื้อ</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number"><?= number_format($total_designs) ?></div>
                                <div class="stat-label">การออกแบบ</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">฿<?= number_format($total_spent) ?></div>
                                <div class="stat-label">ยอดเงินรวม</div>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Profile Form -->
                    <div class="profile-card">
                        <h3 class="section-title">
                            <i class="fas fa-user-edit"></i>แก้ไขข้อมูลส่วนตัว
                        </h3>
                        <form method="POST" id="profileForm">
                            <input type="hidden" name="action" value="update_profile">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="name" name="name"
                                               value="<?= htmlspecialchars($customer['name']) ?>" required>
                                        <label for="name">ชื่อ-นามสกุล</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" class="form-control" id="email" name="email"
                                               value="<?= htmlspecialchars($customer['email']) ?>" required>
                                        <label for="email">อีเมล</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="tel" class="form-control" id="phone" name="phone"
                                               value="<?= htmlspecialchars($customer['phone']) ?>" required>
                                        <label for="phone">เบอร์โทรศัพท์</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <textarea class="form-control" id="address" name="address" style="height: 100px"
                                                  placeholder="ที่อยู่"><?= htmlspecialchars($customer['address'] ?? '') ?></textarea>
                                        <label for="address">ที่อยู่</label>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>บันทึกข้อมูล
                            </button>
                        </form>
                    </div>

                    <!-- Change Password Form -->
                    <div class="profile-card">
                        <h3 class="section-title">
                            <i class="fas fa-lock"></i>เปลี่ยนรหัสผ่าน
                        </h3>
                        <form method="POST" id="passwordForm">
                            <input type="hidden" name="action" value="change_password">

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-floating">
                                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                                        <label for="current_password">รหัสผ่านปัจจุบัน</label>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="password" class="form-control" id="new_password" name="new_password"
                                               required minlength="6">
                                        <label for="new_password">รหัสผ่านใหม่</label>
                                        <div class="password-strength">
                                            <div class="strength-bar">
                                                <div class="strength-fill"></div>
                                            </div>
                                            <small class="strength-text">ความปลอดภัยของรหัสผ่าน</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                        <label for="confirm_password">ยืนยันรหัสผ่านใหม่</label>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key me-2"></i>เปลี่ยนรหัสผ่าน
                            </button>
                        </form>
                    </div>

                    <!-- Account Actions -->
                    <div class="profile-card">
                        <h3 class="section-title">
                            <i class="fas fa-cog"></i>การจัดการบัญชี
                        </h3>
                        <div class="d-flex gap-3">
                            <a href="logout.php" class="btn btn-outline-danger">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password strength checker
        document.getElementById('new_password')?.addEventListener('input', function() {
            const password = this.value;
            const strengthBar = document.querySelector('.strength-fill');
            const strengthText = document.querySelector('.strength-text');

            if (!strengthBar) return;

            let strength = 0;
            let text = '';
            let color = '';

            if (password.length >= 6) strength += 20;
            if (password.match(/[a-z]/)) strength += 20;
            if (password.match(/[A-Z]/)) strength += 20;
            if (password.match(/[0-9]/)) strength += 20;
            if (password.match(/[^a-zA-Z0-9]/)) strength += 20;

            if (strength < 40) {
                text = 'อ่อน';
                color = '#dc3545';
            } else if (strength < 80) {
                text = 'ปานกลาง';
                color = '#ffc107';
            } else {
                text = 'แข็งแรง';
                color = '#28a745';
            }

            strengthBar.style.width = strength + '%';
            strengthBar.style.backgroundColor = color;
            strengthText.textContent = `ความปลอดภัย: ${text}`;
            strengthText.style.color = color;
        });

        // Password confirmation validation
        document.getElementById('confirm_password')?.addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = this.value;

            if (confirmPassword && password !== confirmPassword) {
                this.setCustomValidity('รหัสผ่านไม่ตรงกัน');
            } else {
                this.setCustomValidity('');
            }
        });

        // Phone number validation
        document.getElementById('phone')?.addEventListener('input', function() {
            const phone = this.value.replace(/\D/g, '');
            this.value = phone;

            if (phone.length < 9 || phone.length > 10) {
                this.setCustomValidity('เบอร์โทรศัพท์ต้องมี 9-10 หลัก');
            } else {
                this.setCustomValidity('');
            }
        });

        // Form submission handling
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"]');

                if (this.checkValidity()) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังดำเนินการ...';
                    submitBtn.disabled = true;

                    // Re-enable button after 3 seconds in case of form validation errors
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 3000);
                }
            });
        });
    </script>
</body>
</html>
