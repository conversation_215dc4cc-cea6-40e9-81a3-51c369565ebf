/*!
 * OverlayScrollbars
 * https://github.com/KingSora/OverlayScrollbars
 *
 * Version: 1.13.0
 *
 * Copyright KingSora | <PERSON>.
 * https://github.com/KingSora
 *
 * Released under the MIT license.
 * Date: 02.08.2020
 */
html.os-html,
html.os-html>.os-host {
    display: block;
    overflow: hidden;
    box-sizing: border-box;
    height: 100% !important;
    width: 100% !important;
    min-width: 100% !important;
    min-height: 100% !important;
    margin: 0 !important;
    position: absolute !important
}

html.os-html>.os-host>.os-padding {
    position: absolute
}

body.os-dragging,
body.os-dragging * {
    cursor: default
}

.os-host,
.os-host-textarea {
    position: relative;
    overflow: visible !important;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -ms-flex-line-pack: start;
    align-content: flex-start;
    -webkit-box-align: start;
    -ms-flex-align: start;
    -ms-grid-row-align: flex-start;
    align-items: flex-start
}

.os-host-flexbox {
    overflow: hidden !important;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.os-host-flexbox>.os-size-auto-observer {
    height: inherit !important
}

.os-host-flexbox>.os-content-glue {
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    -ms-flex-negative: 0;
    flex-shrink: 0
}

.os-host-flexbox>.os-size-auto-observer,
.os-host-flexbox>.os-content-glue {
    min-height: 0;
    min-width: 0;
    -webkit-box-flex: 0;
    -ms-flex-positive: 0;
    flex-grow: 0;
    -ms-flex-negative: 1;
    flex-shrink: 1;
    -ms-flex-preferred-size: auto;
    flex-basis: auto
}

#os-dummy-scrollbar-size {
    position: fixed;
    opacity: 0;
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=0)';
    visibility: hidden;
    overflow: scroll;
    height: 500px;
    width: 500px
}

#os-dummy-scrollbar-size>div {
    width: 200%;
    height: 200%;
    margin: 10px 0
}

#os-dummy-scrollbar-size:before,
#os-dummy-scrollbar-size:after,
.os-content:before,
.os-content:after {
    content: '';
    display: table;
    width: .01px;
    height: .01px;
    line-height: 0;
    font-size: 0;
    flex-grow: 0;
    flex-shrink: 0;
    visibility: hidden
}

#os-dummy-scrollbar-size,
.os-viewport {
    -ms-overflow-style: scrollbar !important
}

.os-viewport-native-scrollbars-invisible#os-dummy-scrollbar-size,
.os-viewport-native-scrollbars-invisible.os-viewport {
    scrollbar-width: none !important
}

.os-viewport-native-scrollbars-invisible#os-dummy-scrollbar-size::-webkit-scrollbar,
.os-viewport-native-scrollbars-invisible.os-viewport::-webkit-scrollbar,
.os-viewport-native-scrollbars-invisible#os-dummy-scrollbar-size::-webkit-scrollbar-corner,
.os-viewport-native-scrollbars-invisible.os-viewport::-webkit-scrollbar-corner {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    visibility: hidden !important;
    background: 0 0 !important
}

.os-content-glue {
    box-sizing: inherit;
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    pointer-events: none
}

.os-padding {
    box-sizing: inherit;
    direction: inherit;
    position: absolute;
    overflow: visible;
    padding: 0;
    margin: 0;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    width: auto !important;
    height: auto !important;
    z-index: 0
}

.os-host-overflow>.os-padding {
    overflow: hidden
}

.os-viewport {
    direction: inherit !important;
    box-sizing: inherit !important;
    resize: none !important;
    outline: 0 !important;
    position: absolute;
    overflow: hidden;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    padding: 0;
    margin: 0;
    -webkit-overflow-scrolling: touch
}

.os-content-arrange {
    position: absolute;
    z-index: -1;
    min-height: 1px;
    min-width: 1px;
    pointer-events: none
}

.os-content {
    direction: inherit;
    box-sizing: border-box !important;
    position: relative;
    display: block;
    height: 100%;
    width: 100%;
    height: 100%;
    width: 100%;
    visibility: visible
}

.os-content>.os-textarea {
    box-sizing: border-box !important;
    direction: inherit !important;
    background: 0 0 !important;
    outline: 0 transparent !important;
    overflow: hidden !important;
    position: absolute !important;
    display: block !important;
    top: 0 !important;
    left: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    float: none !important;
    -webkit-filter: none !important;
    filter: none !important;
    border: 0 !important;
    resize: none !important;
    -webkit-transform: none !important;
    transform: none !important;
    max-width: none !important;
    max-height: none !important;
    box-shadow: none !important;
    -webkit-perspective: none !important;
    perspective: none !important;
    opacity: 1 !important;
    z-index: 1 !important;
    clip: auto !important;
    vertical-align: baseline !important;
    padding: 0
}

.os-host-rtl>.os-padding>.os-viewport>.os-content>.os-textarea {
    right: 0 !important
}

.os-content>.os-textarea-cover {
    z-index: -1;
    pointer-events: none
}

.os-content>.os-textarea[wrap=off] {
    white-space: pre !important;
    margin: 0 !important
}

.os-text-inherit {
    font-family: inherit;
    font-size: inherit;
    font-weight: inherit;
    font-style: inherit;
    font-variant: inherit;
    text-transform: inherit;
    text-decoration: inherit;
    text-indent: inherit;
    text-align: inherit;
    text-shadow: inherit;
    text-overflow: inherit;
    letter-spacing: inherit;
    word-spacing: inherit;
    line-height: inherit;
    unicode-bidi: inherit;
    direction: inherit;
    color: inherit;
    cursor: text
}

.os-resize-observer,
.os-resize-observer-host {
    box-sizing: inherit;
    display: block;
    visibility: hidden;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: -1
}

.os-resize-observer-host {
    padding: inherit;
    border: inherit;
    border-color: transparent;
    border-style: solid;
    box-sizing: border-box
}

.os-resize-observer-host.observed {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start
}

.os-resize-observer-host>.os-resize-observer,
.os-resize-observer-host.observed>.os-resize-observer {
    height: 200%;
    width: 200%;
    padding: inherit;
    border: inherit;
    margin: 0;
    display: block;
    box-sizing: content-box
}

.os-resize-observer-host.observed>.os-resize-observer,
.os-resize-observer-host.observed>.os-resize-observer:before {
    display: flex;
    position: relative;
    flex-grow: 1;
    flex-shrink: 0;
    flex-basis: auto;
    box-sizing: border-box
}

.os-resize-observer-host.observed>.os-resize-observer:before {
    content: '';
    box-sizing: content-box;
    padding: inherit;
    border: inherit;
    margin: 0
}

.os-size-auto-observer {
    box-sizing: inherit !important;
    height: 100%;
    width: inherit;
    max-width: 1px;
    position: relative;
    float: left;
    max-height: 1px;
    overflow: hidden;
    z-index: -1;
    padding: 0;
    margin: 0;
    pointer-events: none;
    -webkit-box-flex: inherit;
    -ms-flex-positive: inherit;
    flex-grow: inherit;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -ms-flex-preferred-size: 0;
    flex-basis: 0
}

.os-size-auto-observer>.os-resize-observer {
    width: 1000%;
    height: 1000%;
    min-height: 1px;
    min-width: 1px
}

.os-resize-observer-item {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    z-index: -1;
    opacity: 0;
    direction: ltr !important;
    -webkit-box-flex: 0 !important;
    -ms-flex: none !important;
    flex: none !important
}

.os-resize-observer-item-final {
    position: absolute;
    left: 0;
    top: 0;
    -webkit-transition: none !important;
    transition: none !important;
    -webkit-box-flex: 0 !important;
    -ms-flex: none !important;
    flex: none !important
}

.os-resize-observer {
    -webkit-animation-duration: .001s;
    animation-duration: .001s;
    -webkit-animation-name: os-resize-observer-dummy-animation;
    animation-name: os-resize-observer-dummy-animation
}

object.os-resize-observer {
    box-sizing: border-box !important
}

@-webkit-keyframes os-resize-observer-dummy-animation {
    0% {
        z-index: 0
    }

    to {
        z-index: -1
    }
}

@keyframes os-resize-observer-dummy-animation {
    0% {
        z-index: 0
    }

    to {
        z-index: -1
    }
}

.os-host-transition>.os-scrollbar,
.os-host-transition>.os-scrollbar-corner {
    -webkit-transition: opacity .3s, visibility .3s, top .3s, right .3s, bottom .3s, left .3s;
    transition: opacity .3s, visibility .3s, top .3s, right .3s, bottom .3s, left .3s
}

html.os-html>.os-host>.os-scrollbar {
    position: absolute;
    z-index: 999999
}

.os-scrollbar,
.os-scrollbar-corner {
    position: absolute;
    opacity: 1;
    -ms-filter: 'progid:DXImageTransform.Microsoft.Alpha(Opacity=100)';
    z-index: 1
}

.os-scrollbar-corner {
    bottom: 0;
    right: 0
}

.os-scrollbar {
    pointer-events: none
}

.os-scrollbar-track {
    pointer-events: auto;
    position: relative;
    height: 100%;
    width: 100%;
    padding: 0 !important;
    border: 0 !important
}

.os-scrollbar-handle {
    pointer-events: auto;
    position: absolute;
    width: 100%;
    height: 100%
}

.os-scrollbar-handle-off,
.os-scrollbar-track-off {
    pointer-events: none
}

.os-scrollbar.os-scrollbar-unusable,
.os-scrollbar.os-scrollbar-unusable * {
    pointer-events: none !important
}

.os-scrollbar.os-scrollbar-unusable .os-scrollbar-handle {
    opacity: 0 !important
}

.os-scrollbar-horizontal {
    bottom: 0;
    left: 0
}

.os-scrollbar-vertical {
    top: 0;
    right: 0
}

.os-host-rtl>.os-scrollbar-horizontal {
    right: 0
}

.os-host-rtl>.os-scrollbar-vertical {
    right: auto;
    left: 0
}

.os-host-rtl>.os-scrollbar-corner {
    right: auto;
    left: 0
}

.os-scrollbar-auto-hidden,
.os-padding+.os-scrollbar-corner,
.os-host-resize-disabled.os-host-scrollbar-horizontal-hidden>.os-scrollbar-corner,
.os-host-scrollbar-horizontal-hidden>.os-scrollbar-horizontal,
.os-host-resize-disabled.os-host-scrollbar-vertical-hidden>.os-scrollbar-corner,
.os-host-scrollbar-vertical-hidden>.os-scrollbar-vertical,
.os-scrollbar-horizontal.os-scrollbar-auto-hidden+.os-scrollbar-vertical+.os-scrollbar-corner,
.os-scrollbar-horizontal+.os-scrollbar-vertical.os-scrollbar-auto-hidden+.os-scrollbar-corner,
.os-scrollbar-horizontal.os-scrollbar-auto-hidden+.os-scrollbar-vertical.os-scrollbar-auto-hidden+.os-scrollbar-corner {
    opacity: 0;
    visibility: hidden;
    pointer-events: none
}

.os-scrollbar-corner-resize-both {
    cursor: nwse-resize
}

.os-host-rtl>.os-scrollbar-corner-resize-both {
    cursor: nesw-resize
}

.os-scrollbar-corner-resize-horizontal {
    cursor: ew-resize
}

.os-scrollbar-corner-resize-vertical {
    cursor: ns-resize
}

.os-dragging .os-scrollbar-corner.os-scrollbar-corner-resize {
    cursor: default
}

.os-host-resize-disabled.os-host-scrollbar-horizontal-hidden>.os-scrollbar-vertical {
    top: 0;
    bottom: 0
}

.os-host-resize-disabled.os-host-scrollbar-vertical-hidden>.os-scrollbar-horizontal,
.os-host-rtl.os-host-resize-disabled.os-host-scrollbar-vertical-hidden>.os-scrollbar-horizontal {
    right: 0;
    left: 0
}

.os-scrollbar:hover,
.os-scrollbar-corner.os-scrollbar-corner-resize {
    opacity: 1 !important;
    visibility: visible !important
}

.os-scrollbar-corner.os-scrollbar-corner-resize {
    background-image: url(data:image/svg+xml;base64,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);
    background-repeat: no-repeat;
    background-position: 100% 100%;
    pointer-events: auto !important
}

.os-host-rtl>.os-scrollbar-corner.os-scrollbar-corner-resize {
    -webkit-transform: scale(-1, 1);
    transform: scale(-1, 1)
}

.os-host-overflow {
    overflow: hidden !important
}

.os-theme-none>.os-scrollbar-horizontal,
.os-theme-none>.os-scrollbar-vertical,
.os-theme-none>.os-scrollbar-corner {
    display: none !important
}

.os-theme-none>.os-scrollbar-corner-resize {
    display: block !important;
    min-width: 10px;
    min-height: 10px
}

.os-theme-dark>.os-scrollbar-horizontal,
.os-theme-light>.os-scrollbar-horizontal {
    right: 10px;
    height: 10px
}

.os-theme-dark>.os-scrollbar-vertical,
.os-theme-light>.os-scrollbar-vertical {
    bottom: 10px;
    width: 10px
}

.os-theme-dark.os-host-rtl>.os-scrollbar-horizontal,
.os-theme-light.os-host-rtl>.os-scrollbar-horizontal {
    left: 10px;
    right: 0
}

.os-theme-dark>.os-scrollbar-corner,
.os-theme-light>.os-scrollbar-corner {
    height: 10px;
    width: 10px
}

.os-theme-dark>.os-scrollbar-corner,
.os-theme-light>.os-scrollbar-corner {
    background-color: transparent
}

.os-theme-dark>.os-scrollbar,
.os-theme-light>.os-scrollbar {
    padding: 2px;
    box-sizing: border-box;
    background: 0 0
}

.os-theme-dark>.os-scrollbar.os-scrollbar-unusable,
.os-theme-light>.os-scrollbar.os-scrollbar-unusable {
    background: 0 0
}

.os-theme-dark>.os-scrollbar>.os-scrollbar-track,
.os-theme-light>.os-scrollbar>.os-scrollbar-track {
    background: 0 0
}

.os-theme-dark>.os-scrollbar-horizontal>.os-scrollbar-track>.os-scrollbar-handle,
.os-theme-light>.os-scrollbar-horizontal>.os-scrollbar-track>.os-scrollbar-handle {
    min-width: 30px
}

.os-theme-dark>.os-scrollbar-vertical>.os-scrollbar-track>.os-scrollbar-handle,
.os-theme-light>.os-scrollbar-vertical>.os-scrollbar-track>.os-scrollbar-handle {
    min-height: 30px
}

.os-theme-dark.os-host-transition>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle,
.os-theme-light.os-host-transition>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle {
    -webkit-transition: background-color .3s;
    transition: background-color .3s
}

.os-theme-dark>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle,
.os-theme-light>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle,
.os-theme-dark>.os-scrollbar>.os-scrollbar-track,
.os-theme-light>.os-scrollbar>.os-scrollbar-track {
    border-radius: 10px
}

.os-theme-dark>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle {
    background: rgba(0, 0, 0, .4)
}

.os-theme-light>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle {
    background: rgba(255, 255, 255, .4)
}

.os-theme-dark>.os-scrollbar:hover>.os-scrollbar-track>.os-scrollbar-handle {
    background: rgba(0, 0, 0, .55)
}

.os-theme-light>.os-scrollbar:hover>.os-scrollbar-track>.os-scrollbar-handle {
    background: rgba(255, 255, 255, .55)
}

.os-theme-dark>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle.active {
    background: rgba(0, 0, 0, .7)
}

.os-theme-light>.os-scrollbar>.os-scrollbar-track>.os-scrollbar-handle.active {
    background: rgba(255, 255, 255, .7)
}

.os-theme-dark>.os-scrollbar-horizontal .os-scrollbar-handle:before,
.os-theme-dark>.os-scrollbar-vertical .os-scrollbar-handle:before,
.os-theme-light>.os-scrollbar-horizontal .os-scrollbar-handle:before,
.os-theme-light>.os-scrollbar-vertical .os-scrollbar-handle:before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: block
}

.os-theme-dark.os-host-scrollbar-horizontal-hidden>.os-scrollbar-horizontal .os-scrollbar-handle:before,
.os-theme-dark.os-host-scrollbar-vertical-hidden>.os-scrollbar-vertical .os-scrollbar-handle:before,
.os-theme-light.os-host-scrollbar-horizontal-hidden>.os-scrollbar-horizontal .os-scrollbar-handle:before,
.os-theme-light.os-host-scrollbar-vertical-hidden>.os-scrollbar-vertical .os-scrollbar-handle:before {
    display: none
}

.os-theme-dark>.os-scrollbar-horizontal .os-scrollbar-handle:before,
.os-theme-light>.os-scrollbar-horizontal .os-scrollbar-handle:before {
    top: -6px;
    bottom: -2px
}

.os-theme-dark>.os-scrollbar-vertical .os-scrollbar-handle:before,
.os-theme-light>.os-scrollbar-vertical .os-scrollbar-handle:before {
    left: -6px;
    right: -2px
}

.os-host-rtl.os-theme-dark>.os-scrollbar-vertical .os-scrollbar-handle:before,
.os-host-rtl.os-theme-light>.os-scrollbar-vertical .os-scrollbar-handle:before {
    right: -6px;
    left: -2px
}