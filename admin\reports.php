<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการเข้าสู่ระบบ Admin
if (!isset($_SESSION['admin_logged_in'])) {
    header("Location: login.php");
    exit;
}

$pdo = getDbConnection();
$admin_name = $_SESSION['admin_name'] ?? 'Admin';

// ตัวแปรสำหรับกรองข้อมูล
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // วันแรกของเดือน
$date_to = $_GET['date_to'] ?? date('Y-m-d'); // วันนี้
$report_type = $_GET['report_type'] ?? 'daily';

// ฟังก์ชันดึงสถิติรวม
function getOverallStats($pdo, $date_from, $date_to) {
    try {
        $stats = [];

        // ยอดขายรวม
        $stmt = $pdo->prepare("
            SELECT SUM(total_amount) as total_sales, COUNT(*) as total_orders
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            AND status NOT IN ('cancelled')
        ");
        $stmt->execute([$date_from, $date_to]);
        $sales_data = $stmt->fetch();

        $stats['total_sales'] = $sales_data['total_sales'] ?: 0;
        $stats['total_orders'] = $sales_data['total_orders'] ?: 0;

        // ลูกค้าใหม่
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as new_customers
            FROM customers
            WHERE DATE(created_at) BETWEEN ? AND ?
        ");
        $stmt->execute([$date_from, $date_to]);
        $stats['new_customers'] = $stmt->fetchColumn() ?: 0;

        // การออกแบบใหม่
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as new_designs
            FROM customer_designs
            WHERE DATE(created_at) BETWEEN ? AND ?
        ");
        $stmt->execute([$date_from, $date_to]);
        $stats['new_designs'] = $stmt->fetchColumn() ?: 0;

        // เปรียบเทียบกับช่วงก่อนหน้า
        $days_diff = (strtotime($date_to) - strtotime($date_from)) / (60 * 60 * 24) + 1;
        $prev_date_from = date('Y-m-d', strtotime($date_from . " -{$days_diff} days"));
        $prev_date_to = date('Y-m-d', strtotime($date_from . " -1 day"));

        $stmt = $pdo->prepare("
            SELECT SUM(total_amount) as prev_sales, COUNT(*) as prev_orders
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            AND status NOT IN ('cancelled')
        ");
        $stmt->execute([$prev_date_from, $prev_date_to]);
        $prev_sales_data = $stmt->fetch();

        $prev_sales = $prev_sales_data['prev_sales'] ?: 0;
        $prev_orders = $prev_sales_data['prev_orders'] ?: 0;

        $stats['sales_growth'] = $prev_sales > 0 ? (($stats['total_sales'] - $prev_sales) / $prev_sales) * 100 : 0;
        $stats['orders_growth'] = $prev_orders > 0 ? (($stats['total_orders'] - $prev_orders) / $prev_orders) * 100 : 0;

        return $stats;
    } catch (Exception $e) {
        return [
            'total_sales' => 0,
            'total_orders' => 0,
            'new_customers' => 0,
            'new_designs' => 0,
            'sales_growth' => 0,
            'orders_growth' => 0
        ];
    }
}

// ฟังก์ชันดึงข้อมูลยอดขายตามช่วงเวลา
function getSalesData($pdo, $date_from, $date_to, $report_type) {
    try {
        $date_format = '';
        $group_by = '';

        switch ($report_type) {
            case 'daily':
                $date_format = '%Y-%m-%d';
                $group_by = 'DATE(created_at)';
                break;
            case 'weekly':
                $date_format = '%Y-%u';
                $group_by = 'YEARWEEK(created_at)';
                break;
            case 'monthly':
                $date_format = '%Y-%m';
                $group_by = 'DATE_FORMAT(created_at, "%Y-%m")';
                break;
        }

        $stmt = $pdo->prepare("
            SELECT
                DATE_FORMAT(created_at, '$date_format') as period,
                SUM(total_amount) as sales,
                COUNT(*) as orders
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            AND status NOT IN ('cancelled')
            GROUP BY $group_by
            ORDER BY period ASC
        ");
        $stmt->execute([$date_from, $date_to]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// ฟังก์ชันดึงสินค้าขายดี
function getTopProducts($pdo, $date_from, $date_to, $limit = 10) {
    try {
        $stmt = $pdo->prepare("
            SELECT
                p.name,
                p.price,
                SUM(o.quantity) as total_quantity,
                SUM(o.total_amount) as total_sales,
                COUNT(o.id) as order_count
            FROM orders o
            LEFT JOIN products p ON o.product_id = p.id
            WHERE DATE(o.created_at) BETWEEN ? AND ?
            AND o.status NOT IN ('cancelled')
            AND p.id IS NOT NULL
            GROUP BY o.product_id
            ORDER BY total_sales DESC
            LIMIT ?
        ");
        $stmt->execute([$date_from, $date_to, $limit]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// ฟังก์ชันดึงสถิติสถานะคำสั่งซื้อ
function getOrderStatusStats($pdo, $date_from, $date_to) {
    try {
        $stmt = $pdo->prepare("
            SELECT
                status,
                COUNT(*) as count,
                SUM(total_amount) as total_amount
            FROM orders
            WHERE DATE(created_at) BETWEEN ? AND ?
            GROUP BY status
            ORDER BY count DESC
        ");
        $stmt->execute([$date_from, $date_to]);
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

// ดึงข้อมูลทั้งหมด
$overall_stats = getOverallStats($pdo, $date_from, $date_to);
$sales_data = getSalesData($pdo, $date_from, $date_to, $report_type);
$top_products = getTopProducts($pdo, $date_from, $date_to, 10);
$order_status_stats = getOrderStatusStats($pdo, $date_from, $date_to);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>รายงานและสถิติ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: white !important;
        }

        .sidebar {
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 15px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar .nav-link {
            color: #6c757d;
            font-weight: 500;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3));
            border-radius: 50%;
            transform: translate(30px, -30px);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 20px;
        }

        .stats-sales .stats-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stats-orders .stats-icon {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stats-customers .stats-icon {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stats-designs .stats-icon {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stats-number {
            font-size: 2.2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .stats-growth {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .stats-growth.positive {
            color: #28a745;
        }

        .stats-growth.negative {
            color: #dc3545;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 10px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .table-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            border-top: none;
            border-bottom: 2px solid #e9ecef;
            font-weight: 600;
            color: #2c3e50;
            padding: 15px;
        }

        .table td {
            padding: 15px;
            vertical-align: middle;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-bottom: 20px;
            }

            .main-content {
                padding: 15px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .chart-header {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-tshirt me-2"></i>GT Sport Admin
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= htmlspecialchars($admin_name) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3">
                <div class="sidebar">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-3"></i>แดชบอร์ด
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-3"></i>คำสั่งซื้อ
                        </a>
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-box me-3"></i>สินค้า
                        </a>
                        <a class="nav-link" href="customers.php">
                            <i class="fas fa-users me-3"></i>ลูกค้า
                        </a>
                        <a class="nav-link" href="design.php">
                            <i class="fas fa-palette me-3"></i>การออกแบบ
                        </a>
                        <a class="nav-link active" href="reports.php">
                            <i class="fas fa-chart-bar me-3"></i>รายงาน
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="main-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <h1 class="page-title">รายงานและสถิติ</h1>
                        <p class="text-muted">ติดตามผลการดำเนินงานและการเติบโตของธุรกิจ</p>
                    </div>

                    <!-- Filter Section -->
                    <div class="filter-section">
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">จากวันที่</label>
                                    <input type="date" class="form-control" name="date_from"
                                           value="<?= htmlspecialchars($date_from) ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">ถึงวันที่</label>
                                    <input type="date" class="form-control" name="date_to"
                                           value="<?= htmlspecialchars($date_to) ?>">
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">ประเภทรายงาน</label>
                                    <select class="form-select" name="report_type">
                                        <option value="daily" <?= $report_type === 'daily' ? 'selected' : '' ?>>รายวัน</option>
                                        <option value="weekly" <?= $report_type === 'weekly' ? 'selected' : '' ?>>รายสัปดาห์</option>
                                        <option value="monthly" <?= $report_type === 'monthly' ? 'selected' : '' ?>>รายเดือน</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-2"></i>ดูรายงาน
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Overall Statistics -->
                    <div class="stats-grid">
                        <div class="stats-card stats-sales">
                            <div class="stats-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="stats-number">฿<?= number_format($overall_stats['total_sales']) ?></div>
                            <div class="stats-label">ยอดขายรวม</div>
                            <div class="stats-growth <?= $overall_stats['sales_growth'] >= 0 ? 'positive' : 'negative' ?>">
                                <i class="fas fa-arrow-<?= $overall_stats['sales_growth'] >= 0 ? 'up' : 'down' ?> me-2"></i>
                                <?= number_format(abs($overall_stats['sales_growth']), 1) ?>%
                            </div>
                        </div>

                        <div class="stats-card stats-orders">
                            <div class="stats-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stats-number"><?= number_format($overall_stats['total_orders']) ?></div>
                            <div class="stats-label">คำสั่งซื้อ</div>
                            <div class="stats-growth <?= $overall_stats['orders_growth'] >= 0 ? 'positive' : 'negative' ?>">
                                <i class="fas fa-arrow-<?= $overall_stats['orders_growth'] >= 0 ? 'up' : 'down' ?> me-2"></i>
                                <?= number_format(abs($overall_stats['orders_growth']), 1) ?>%
                            </div>
                        </div>

                        <div class="stats-card stats-customers">
                            <div class="stats-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stats-number"><?= number_format($overall_stats['new_customers']) ?></div>
                            <div class="stats-label">ลูกค้าใหม่</div>
                            <div class="stats-growth positive">
                                <i class="fas fa-user-plus me-2"></i>
                                สมาชิกใหม่
                            </div>
                        </div>

                        <div class="stats-card stats-designs">
                            <div class="stats-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <div class="stats-number"><?= number_format($overall_stats['new_designs']) ?></div>
                            <div class="stats-label">การออกแบบใหม่</div>
                            <div class="stats-growth positive">
                                <i class="fas fa-plus me-2"></i>
                                งานออกแบบ
                            </div>
                        </div>
                    </div>

                    <!-- Sales Chart -->
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">แนวโน้มยอดขาย</h3>
                        </div>
                        <canvas id="salesChart" height="100"></canvas>
                    </div>

                    <div class="row">
                        <!-- Top Products -->
                        <div class="col-lg-8">
                            <div class="table-card">
                                <h3 class="chart-title mb-4">สินค้าขายดี</h3>
                                <?php if ($top_products): ?>
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>สินค้า</th>
                                                    <th>ราคา</th>
                                                    <th>จำนวนขาย</th>
                                                    <th>ยอดขาย</th>
                                                    <th>คำสั่งซื้อ</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($top_products as $product): ?>
                                                    <tr>
                                                        <td><?= htmlspecialchars($product['name']) ?></td>
                                                        <td>฿<?= number_format($product['price']) ?></td>
                                                        <td><?= number_format($product['total_quantity']) ?> ชิ้น</td>
                                                        <td class="fw-bold text-success">฿<?= number_format($product['total_sales']) ?></td>
                                                        <td><?= number_format($product['order_count']) ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-chart-bar text-muted" style="font-size: 3rem;"></i>
                                        <p class="text-muted mt-3">ไม่มีข้อมูลในช่วงเวลาที่เลือก</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Order Status Distribution -->
                        <div class="col-lg-4">
                            <div class="table-card">
                                <h3 class="chart-title mb-4">สถานะคำสั่งซื้อ</h3>
                                <?php if ($order_status_stats): ?>
                                    <?php foreach ($order_status_stats as $status): ?>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <?php
                                                $status_text = 'ไม่ระบุ';
                                                $status_class = 'status-pending';
                                                switch ($status['status']) {
                                                    case 'pending':
                                                        $status_text = 'รอดำเนินการ';
                                                        $status_class = 'status-pending';
                                                        break;
                                                    case 'processing':
                                                        $status_text = 'กำลังผลิต';
                                                        $status_class = 'status-processing';
                                                        break;
                                                    case 'completed':
                                                        $status_text = 'เสร็จสิ้น';
                                                        $status_class = 'status-completed';
                                                        break;
                                                    case 'cancelled':
                                                        $status_text = 'ยกเลิก';
                                                        $status_class = 'status-cancelled';
                                                        break;
                                                }
                                                ?>
                                                <span class="status-badge <?= $status_class ?>">
                                                    <?= $status_text ?>
                                                </span>
                                            </div>
                                            <div class="text-end">
                                                <div class="fw-bold"><?= number_format($status['count']) ?></div>
                                                <small class="text-muted">฿<?= number_format($status['total_amount']) ?></small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-chart-pie text-muted" style="font-size: 3rem;"></i>
                                        <p class="text-muted mt-3">ไม่มีข้อมูล</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sales Chart
        const salesData = <?= json_encode($sales_data) ?>;
        const ctx = document.getElementById('salesChart').getContext('2d');

        const labels = salesData.map(item => item.period);
        const salesValues = salesData.map(item => parseFloat(item.sales) || 0);
        const orderValues = salesData.map(item => parseInt(item.orders) || 0);

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'ยอดขาย (บาท)',
                        data: salesValues,
                        borderColor: 'rgb(102, 126, 234)',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: 'จำนวนคำสั่งซื้อ',
                        data: orderValues,
                        borderColor: 'rgb(249, 147, 251)',
                        backgroundColor: 'rgba(249, 147, 251, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'ช่วงเวลา'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'ยอดขาย (บาท)'
                        },
                        ticks: {
                            callback: function(value) {
                                return '฿' + value.toLocaleString();
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'จำนวนคำสั่งซื้อ'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return 'ยอดขาย: ฿' + context.parsed.y.toLocaleString();
                                } else {
                                    return 'คำสั่งซื้อ: ' + context.parsed.y + ' รายการ';
                                }
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
