<?php
header('Content-Type: application/json');
require_once './config/database.php';

try {
    $pdo = getDbConnection();
    $order_number = $_GET['order'] ?? '';

    if (!$order_number) {
        throw new Exception('Missing order number');
    }

    $stmt = $pdo->prepare("SELECT payment_status FROM orders WHERE order_number = ?");
    $stmt->execute([$order_number]);
    $order = $stmt->fetch();

    if (!$order) {
        throw new Exception('Order not found');
    }

    echo json_encode([
        'success' => true,
        'status' => $order['payment_status']
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
