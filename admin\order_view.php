<?php
$admin_page_title = "รายละเอียดออเดอร์";
require_once '../includes/config.php';
require_once '../includes/functions.php';

$order_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Simulated full order data (replace with database calls)
$all_orders_detail = [
    1001 => [
        'id' => 1001,
        'customer_name' => 'สมชาย ใจดี',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '************',
        'customer_address' => "123 ถ.สุขุมวิท แขวงคลองเตย เขตคลองเตย กรุงเทพฯ 10110",
        'order_date' => '2024-05-25 10:30:00',
        'total_amount' => 1580.00,
        'order_status' => 'Pending',
        'payment_status' => 'Paid',
        'shipping_provider' => 'Kerry Express',
        'tracking_number' => 'KER001234567TH',
        'notes' => 'ลูกค้าขอเปลี่ยนสีแขนเสื้อเป็นสีน้ำเงิน',
        'items' => [
            ['product_id' => 1, 'product_name' => 'เสื้อฟุตบอลทีมชาติ V.1', 'quantity' => 2, 'unit_price' => 790.00, 'item_details' => 'ไซส์ L, สีแดง'],
        ]
    ],
    1002 => [
        'id' => 1002,
        'customer_name' => 'สมหญิง มีสุข',
        'customer_email' => '<EMAIL>',
        'customer_phone' => '************',
        'customer_address' => "45/6 หมู่ 7 ต.บางพูด อ.ปากเกร็ด จ.นนทบุรี 11120",
        'order_date' => '2024-05-24 14:15:00',
        'total_amount' => 890.00,
        'order_status' => 'Shipped',
        'payment_status' => 'Paid',
        'shipping_provider' => 'ไปรษณีย์ไทย EMS',
        'tracking_number' => 'EF582001234TH',
        'notes' => '',
        'items' => [
            ['product_id' => 'DESIGN005', 'product_name' => 'เสื้อวิ่งออกแบบเองลาย Galaxy', 'quantity' => 1, 'unit_price' => 890.00, 'item_details' => 'ไซส์ M, แขนสั้น'],
        ]
    ],
    // Add more orders for simulation if needed
];

$order = null;
if ($order_id > 0 && isset($all_orders_detail[$order_id])) {
    $order = $all_orders_detail[$order_id];
    $admin_page_title = "รายละเอียดออเดอร์ #" . $order['id']; // Update page title
} else {
    $_SESSION['message'] = "ไม่พบออเดอร์ ID: {$order_id}";
    $_SESSION['message_type'] = 'error';
    // header('Location: orders.php'); // Redirect or show error message here
    // exit;
}

$order_statuses = ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'];
$payment_statuses = ['Paid', 'Unpaid', 'Refunded', 'Partially Paid'];

?>
<style>
    .order-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }
    .detail-card {
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    .detail-card h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 1.2em;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    .detail-card p { margin: 0 0 10px 0; font-size: 0.95em; }
    .detail-card strong { color: #333; }

    .order-items-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 15px;
    }
    .order-items-table th, .order-items-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
        font-size: 0.9em;
    }
    .order-items-table th { background-color: #f8f8f8; }
    .order-items-table .text-right { text-align: right; }

    .actions-form select, .actions-form input[type="text"] {
        padding: 8px;
        margin-right: 10px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
</style>

<div class="breadcrumbs">
    <a href="dashboard.php">แผงควบคุม</a> &raquo;
    <a href="orders.php">จัดการออเดอร์</a> &raquo;
    <span><?php echo $order ? "รายละเอียดออเดอร์ #" . htmlspecialchars($order['id']) : "ไม่พบออเดอร์"; ?></span>
</div>

<?php if ($order): ?>
    <form action="order_process.php" method="POST" class="actions-form" style="margin-bottom:20px;">
        <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
        <label for="order_status">อัปเดตสถานะออเดอร์:</label>
        <select name="order_status" id="order_status">
            <?php foreach ($order_statuses as $status): ?>
            <option value="<?php echo $status; ?>" <?php echo ($order['order_status'] == $status) ? 'selected' : ''; ?>><?php echo $status; ?></option>
            <?php endforeach; ?>
        </select>

        <label for="payment_status" style="margin-left:15px;">สถานะการชำระเงิน:</label>
        <select name="payment_status" id="payment_status">
            <?php foreach ($payment_statuses as $status): ?>
            <option value="<?php echo $status; ?>" <?php echo ($order['payment_status'] == $status) ? 'selected' : ''; ?>><?php echo $status; ?></option>
            <?php endforeach; ?>
        </select>
        <button type="submit" name="action" value="update_status" class="btn-primary" style="padding:8px 12px; margin-left:10px;">อัปเดตสถานะ</button>
    </form>

    <div class="order-details-grid">
        <div class="detail-card customer-info">
            <h3>ข้อมูลลูกค้า</h3>
            <p><strong>ชื่อ:</strong> <?php echo htmlspecialchars($order['customer_name']); ?></p>
            <p><strong>อีเมล:</strong> <?php echo htmlspecialchars($order['customer_email']); ?></p>
            <p><strong>โทรศัพท์:</strong> <?php echo htmlspecialchars($order['customer_phone']); ?></p>
            <p><strong>ที่อยู่จัดส่ง:</strong><br><?php echo nl2br(htmlspecialchars($order['customer_address'])); ?></p>
        </div>

        <div class="detail-card order-summary">
            <h3>สรุปออเดอร์</h3>
            <p><strong>ID ออเดอร์:</strong> #<?php echo htmlspecialchars($order['id']); ?></p>
            <p><strong>วันที่สั่งซื้อ:</strong> <?php echo htmlspecialchars(date("d M Y H:i", strtotime($order['order_date']))); ?></p>
            <p><strong>ยอดรวม:</strong> <?php echo number_format($order['total_amount'], 2); ?> บาท</p>
            <p><strong>สถานะออเดอร์:</strong> <span class="status-<?php echo strtolower(htmlspecialchars($order['order_status'])); ?>"><?php echo htmlspecialchars($order['order_status']); ?></span></p>
            <p><strong>สถานะการชำระเงิน:</strong> <?php echo htmlspecialchars($order['payment_status']); ?></p>
            <?php if(!empty($order['notes'])): ?>
            <p><strong>หมายเหตุจากลูกค้า:</strong> <?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
            <?php endif; ?>
        </div>

        <div class="detail-card shipping-info" style="grid-column: span 2;">
            <h3>ข้อมูลการจัดส่ง</h3>
             <form action="order_process.php" method="POST" class="actions-form" style="margin-bottom:10px;">
                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                <label for="shipping_provider">บริษัทขนส่ง:</label>
                <input type="text" name="shipping_provider" id="shipping_provider" value="<?php echo htmlspecialchars($order['shipping_provider'] ?? ''); ?>">
                <label for="tracking_number" style="margin-left:10px;">Tracking Number:</label>
                <input type="text" name="tracking_number" id="tracking_number" value="<?php echo htmlspecialchars($order['tracking_number'] ?? ''); ?>">
                <button type="submit" name="action" value="update_shipping" class="btn-primary" style="padding:8px 12px; margin-left:10px;">บันทึกข้อมูลส่ง</button>
            </form>
            <p><strong>บริษัทขนส่ง:</strong> <?php echo htmlspecialchars($order['shipping_provider'] ?? 'N/A'); ?></p>
            <p><strong>Tracking Number:</strong> <?php echo htmlspecialchars($order['tracking_number'] ?? 'N/A'); ?>
                <?php if(!empty($order['tracking_number']) && $order['shipping_provider'] === 'Kerry Express'): ?>
                    <a href="https://th.kerryexpress.com/th/track/?track=<?php echo htmlspecialchars($order['tracking_number']); ?>" target="_blank">(ตรวจสอบ)</a>
                <?php elseif(!empty($order['tracking_number']) && strpos($order['shipping_provider'], 'ไปรษณีย์ไทย') !== false): ?>
                     <a href="https://track.thailandpost.co.th/?trackNumber=<?php echo htmlspecialchars($order['tracking_number']); ?>" target="_blank">(ตรวจสอบ)</a>
                <?php endif; ?>
            </p>
        </div>

        <div class="detail-card order-items" style="grid-column: span 2;">
            <h3>รายการสินค้าในออเดอร์</h3>
            <table class="order-items-table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>ชื่อสินค้า/รหัสดีไซน์</th>
                        <th>รายละเอียด</th>
                        <th>จำนวน</th>
                        <th>ราคาต่อหน่วย</th>
                        <th>ราคารวม</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $item_count = 1;
                    $subtotal = 0;
                    foreach ($order['items'] as $item):
                        $item_total = $item['quantity'] * $item['unit_price'];
                        $subtotal += $item_total;
                    ?>
                    <tr>
                        <td><?php echo $item_count++; ?></td>
                        <td><?php echo htmlspecialchars($item['product_name']); ?> (<?php echo htmlspecialchars($item['product_id']); ?>)</td>
                        <td><?php echo htmlspecialchars($item['item_details']); ?></td>
                        <td><?php echo htmlspecialchars($item['quantity']); ?></td>
                        <td class="text-right"><?php echo number_format($item['unit_price'], 2); ?></td>
                        <td class="text-right"><?php echo number_format($item_total, 2); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="5" class="text-right"><strong>ยอดรวมสินค้าย่อย:</strong></td>
                        <td class="text-right"><strong><?php echo number_format($subtotal, 2); ?></strong></td>
                    </tr>
                    <!-- Add rows for shipping, discount, tax if applicable -->
                    <tr>
                        <td colspan="5" class="text-right"><strong>ยอดรวมสุทธิ:</strong></td>
                        <td class="text-right"><strong><?php echo number_format($order['total_amount'], 2); ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

<?php else: ?>
    <div class="error-message">
        <p>ไม่พบข้อมูลออเดอร์ที่ระบุ (ID: <?php echo htmlspecialchars($order_id); ?>) <a href="orders.php">กลับไปหน้ารายการออเดอร์</a></p>
    </div>
<?php endif; ?>

<?php include 'includes/footer.php'; ?>
