# GT-SportDesign Security Configuration
# Version: 1.0

# ป้องการเข้าไฟล์
<FilesMatch "^(database\.php|config\.php|\.env|\.git|\.htaccess)">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้องการแสดงรายการไฟล์
Options -Indexes

# ป้องการเข้าไฟล์ SQL
<FilesMatch "\.(sql|bak|config|dist|fla|inc|ini|log|psd|sh|sql|swp)$">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้องการเข้าโฟลเดอร์ config
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteRule ^config/.*$ - [F,L]
  RewriteRule ^database/.*$ - [F,L]
</IfModule>

# ป้องการเข้าไฟล์ตั้งค่า
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteCond %{REQUEST_FILENAME} -f
  RewriteCond %{REQUEST_FILENAME} install\.php$
  RewriteCond %{DOCUMENT_ROOT}/config/database.php -f
  RewriteRule ^(.*)$ index.php [L,R=301]
</IfModule>

# เอมด้วย HTTP headers
<IfModule mod_headers.c>
  Header set X-Content-Type-Options "nosniff"
  Header set X-XSS-Protection "1; mode=block"
  Header set X-Frame-Options "SAMEORIGIN"
  Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# กำหนด PHP settings
<IfModule mod_php7.c>
  php_flag display_errors Off
  php_flag log_errors On
  php_value error_log logs/php_errors.log
  php_value upload_max_filesize 10M
  php_value post_max_size 10M
  php_value memory_limit 128M
  php_value max_execution_time 300
  php_value max_input_time 300
</IfModule>


