<?php
header('Content-Type: application/json');
require_once './config/db.php';

try {
    $pdo = getDbConnection();

    // รับข้อมูล JSON
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        throw new Exception('ข้อมูลไม่ถูกต้อง');
    }

    // ตรวจสอบข้อมูลที่จำเป็น
    if (empty($data['product_id'])) {
        throw new Exception('กรุณาเลือกสินค้า');
    }

    $product_id = (int)$data['product_id'];
    $color = $data['selectedColor'] ?? $data['color'] ?? 'white';
    $size = $data['selectedSize'] ?? $data['size'] ?? 'M';
    $quantity = (int)($data['quantity'] ?? 1);
    $elements = $data['elements'] ?? [];
    $view = $data['view'] ?? 'front';

    // สร้างชื่อการออกแบบ
    $design_name = 'การออกแบบ ' . date('d/m/Y H:i');

    // สร้าง design ID ใหม่
    $design_number = 'DSN' . date('Ymd') . rand(1000, 9999);

    // บันทึกการออกแบบหลัก
    $stmt = $pdo->prepare("
        INSERT INTO designs (design_number, design_name, product_id, customer_id, design_data, status, created_at)
        VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    ");

    $design_data = json_encode([
        'product_id' => $product_id,
        'selectedColor' => $color,
        'selectedSize' => $size,
        'quantity' => $quantity,
        'elements' => $elements,
        'view' => $view
    ]);

    $customer_id = null; // ยังไม่มีระบบ login ลูกค้า

    $stmt->execute([$design_number, $design_name, $product_id, $customer_id, $design_data]);
    $design_id = $pdo->lastInsertId();

    // บันทึกแต่ละ element
    foreach ($elements as $element) {
        $stmt = $pdo->prepare("
            INSERT INTO design_elements (design_id, element_type, content, position_x, position_y, properties, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");

        $element_type = $element['type'] ?? 'text';
        $content = $element['content'] ?? '';
        $position_x = $element['x'] ?? 0;
        $position_y = $element['y'] ?? 0;
        $properties = json_encode($element);

        $stmt->execute([$design_id, $element_type, $content, $position_x, $position_y, $properties]);
    }

    echo json_encode([
        'success' => true,
        'message' => 'บันทึกดีไซน์เรียบร้อยแล้ว',
        'design_id' => $design_id,
        'design_number' => $design_number
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
