<?php
require_once '../../includes/db.php';
require_once '../../includes/config.php';
require_once '../includes/auth.php';

// ตรวจสอบว่าเป็น AJAX request
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    http_response_code(403);
    die('Forbidden');
}

// ตรวจสอบสิทธิ์ผู้ใช้
requireAdmin();

// ตรวจสอบข้อมูลที่ส่งมา
if (!isset($_POST['image_id']) || !is_numeric($_POST['image_id'])) {
    echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ถูกต้อง']);
    exit;
}

$image_id = $_POST['image_id'];

try {
    $db = getDB();
    
    // ดึงข้อมูลรูปภาพ
    $stmt = $db->prepare("SELECT * FROM product_images WHERE id = ?");
    $stmt->execute([$image_id]);
    $image = $stmt->fetch();
    
    if (!$image) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรูปภาพ']);
        exit;
    }
    
    // ลบไฟล์รูปภาพ
    $file_path = '../../uploads/products/' . $image['image'];
    if (file_exists($file_path)) {
        unlink($file_path);
    }
    
    // ลบข้อมูลจากฐานข้อมูล
    $stmt = $db->prepare("DELETE FROM product_images WHERE id = ?");
    $stmt->execute([$image_id]);
    
    echo json_encode(['success' => true, 'message' => 'ลบรูปภาพเรียบร้อยแล้ว']);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}