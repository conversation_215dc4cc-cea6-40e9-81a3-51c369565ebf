<?php
/**
 * Generate pagination HTML for admin pages
 * 
 * @param int $total_items Total number of items
 * @param int $per_page Number of items per page
 * @param int $current_page Current page number
 * @param string $url_pattern URL pattern with :page placeholder
 * @return string Pagination HTML
 */
function admin_pagination($total_items, $per_page, $current_page, $url_pattern) {
    if ($total_items <= 0) {
        return '';
    }
    
    $total_pages = ceil($total_items / $per_page);
    
    if ($total_pages <= 1) {
        return '';
    }
    
    $html = '<nav aria-label="Page navigation">';
    $html .= '<ul class="pagination justify-content-center">';
    
    // Previous button
    if ($current_page > 1) {
        $prev_url = str_replace(':page', $current_page - 1, $url_pattern);
        $html .= '<li class="page-item"><a class="page-link" href="' . $prev_url . '">&laquo; ก่อนหน้า</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><a class="page-link" href="#" tabindex="-1">&laquo; ก่อนหน้า</a></li>';
    }
    
    // Page numbers
    $start_page = max(1, $current_page - 2);
    $end_page = min($total_pages, $current_page + 2);
    
    // Always show first page
    if ($start_page > 1) {
        $html .= '<li class="page-item"><a class="page-link" href="' . str_replace(':page', 1, $url_pattern) . '">1</a></li>';
        if ($start_page > 2) {
            $html .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
        }
    }
    
    // Page links
    for ($i = $start_page; $i <= $end_page; $i++) {
        if ($i == $current_page) {
            $html .= '<li class="page-item active"><a class="page-link" href="#">' . $i . '</a></li>';
        } else {
            $html .= '<li class="page-item"><a class="page-link" href="' . str_replace(':page', $i, $url_pattern) . '">' . $i . '</a></li>';
        }
    }
    
    // Always show last page
    if ($end_page < $total_pages) {
        if ($end_page < $total_pages - 1) {
            $html .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
        }
        $html .= '<li class="page-item"><a class="page-link" href="' . str_replace(':page', $total_pages, $url_pattern) . '">' . $total_pages . '</a></li>';
    }
    
    // Next button
    if ($current_page < $total_pages) {
        $next_url = str_replace(':page', $current_page + 1, $url_pattern);
        $html .= '<li class="page-item"><a class="page-link" href="' . $next_url . '">ถัดไป &raquo;</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><a class="page-link" href="#" tabindex="-1">ถัดไป &raquo;</a></li>';
    }
    
    $html .= '</ul>';
    $html .= '</nav>';
    
    return $html;
}

