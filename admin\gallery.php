<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

// ดึงข้อมูลแกลเลอรี่
$gallery_images = [];
$total_images = 0;
$error = '';
$success = '';

try {
    // ลองดึงจากตาราง gallery_images
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM gallery_images WHERE is_active = 1");
        $total_images = $stmt->fetchColumn();
        
        $stmt = $pdo->query("
            SELECT gi.*, fu.file_path, fu.original_name
            FROM gallery_images gi
            LEFT JOIN file_uploads fu ON gi.file_id = fu.id
            ORDER BY gi.sort_order, gi.created_at DESC
            LIMIT 20
        ");
        $gallery_images = $stmt->fetchAll();
    } catch (Exception $e) {
        // ถ้าไม่มีตาราง gallery_images ให้สร้างข้อมูลจำลอง
        $gallery_images = [
            [
                'id' => 1,
                'title' => 'เสื้อทีมฟุตบอล ABC FC',
                'description' => 'ชุดทีมฟุตบอลสีน้ำเงิน-ขาว ดีไซน์สวยงาม',
                'category' => 'football',
                'file_path' => 'uploads/gallery/sample1.jpg',
                'is_active' => 1,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'title' => 'เสื้อกีฬาบริษัท XYZ',
                'description' => 'เสื้อโปโลสำหรับพนักงาน สีแดง-ขาว',
                'category' => 'corporate',
                'file_path' => 'uploads/gallery/sample2.jpg',
                'is_active' => 1,
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'id' => 3,
                'title' => 'ชุดวิ่งมาราธอน',
                'description' => 'ชุดวิ่งสำหรับงานมาราธอน ระบายอากาศดี',
                'category' => 'running',
                'file_path' => 'uploads/gallery/sample3.jpg',
                'is_active' => 1,
                'sort_order' => 3,
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ]
        ];
        $total_images = count($gallery_images);
    }
} catch (Exception $e) {
    $error = 'เกิดข้อผิดพลาดในการโหลดข้อมูล: ' . $e->getMessage();
}

// จัดการการอัปโหลดรูปภาพใหม่
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    
    if ($action === 'add_image') {
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $category = $_POST['category'] ?? 'portfolio';
        
        if ($title) {
            try {
                // จำลองการเพิ่มรูปภาพ (ในระบบจริงจะต้องจัดการการอัปโหลดไฟล์)
                $stmt = $pdo->prepare("
                    INSERT INTO gallery_images (title, description, category, is_active, sort_order, created_at)
                    VALUES (?, ?, ?, 1, 0, NOW())
                ");
                $stmt->execute([$title, $description, $category]);
                
                $success = 'เพิ่มรูปภาพเรียบร้อยแล้ว';
            } catch (Exception $e) {
                $error = 'ไม่สามารถเพิ่มรูปภาพได้: ' . $e->getMessage();
            }
        } else {
            $error = 'กรุณากรอกชื่อรูปภาพ';
        }
    }
    
    if ($action === 'update_image') {
        $image_id = (int)($_POST['image_id'] ?? 0);
        $title = trim($_POST['title'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $category = $_POST['category'] ?? 'portfolio';
        $is_active = isset($_POST['is_active']) ? 1 : 0;
        
        if ($image_id > 0 && $title) {
            try {
                $stmt = $pdo->prepare("
                    UPDATE gallery_images 
                    SET title = ?, description = ?, category = ?, is_active = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$title, $description, $category, $is_active, $image_id]);
                
                $success = 'อัปเดตข้อมูลเรียบร้อยแล้ว';
            } catch (Exception $e) {
                $error = 'ไม่สามารถอัปเดตข้อมูลได้: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'delete_image') {
        $image_id = (int)($_POST['image_id'] ?? 0);
        
        if ($image_id > 0) {
            try {
                $stmt = $pdo->prepare("DELETE FROM gallery_images WHERE id = ?");
                $stmt->execute([$image_id]);
                
                $success = 'ลบรูปภาพเรียบร้อยแล้ว';
            } catch (Exception $e) {
                $error = 'ไม่สามารถลบรูปภาพได้: ' . $e->getMessage();
            }
        }
    }
}

$categories = [
    'portfolio' => 'ผลงาน',
    'football' => 'ฟุตบอล',
    'basketball' => 'บาสเกตบอล',
    'volleyball' => 'วอลเลย์บอล',
    'running' => 'วิ่ง',
    'corporate' => 'บริษัท/องค์กร',
    'school' => 'โรงเรียน',
    'other' => 'อื่นๆ'
];
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการแกลเลอรี่ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .gallery-item {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        .gallery-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .gallery-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 3rem;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php" class="active"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">จัดการแกลเลอรี่</h4>
                <small class="text-muted">จัดการรูปภาพผลงานและแกลเลอรี่</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Stats and Add Button -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-body text-center">
                            <h3 class="text-primary"><?php echo $total_images; ?></h3>
                            <p class="mb-0">รูปภาพทั้งหมด</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 d-flex align-items-center">
                    <button class="btn btn-primary btn-lg w-100" data-bs-toggle="modal" data-bs-target="#addImageModal">
                        <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพใหม่
                    </button>
                </div>
            </div>

            <!-- Gallery Grid -->
            <?php if (empty($gallery_images)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <h3>ยังไม่มีรูปภาพในแกลเลอรี่</h3>
                    <p class="text-muted">เริ่มต้นด้วยการเพิ่มรูปภาพผลงานของคุณ</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addImageModal">
                        <i class="fas fa-plus me-2"></i>เพิ่มรูปภาพแรก
                    </button>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($gallery_images as $image): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="gallery-item">
                            <div class="gallery-image">
                                <i class="fas fa-image"></i>
                            </div>
                            <div class="p-3">
                                <h6 class="mb-2"><?php echo htmlspecialchars($image['title']); ?></h6>
                                <p class="text-muted small mb-2"><?php echo htmlspecialchars($image['description'] ?? ''); ?></p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-secondary"><?php echo $categories[$image['category']] ?? $image['category']; ?></span>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editModal<?php echo $image['id']; ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" onclick="deleteImage(<?php echo $image['id']; ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Edit Modal -->
                    <div class="modal fade" id="editModal<?php echo $image['id']; ?>" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">แก้ไขรูปภาพ</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <form method="POST">
                                    <div class="modal-body">
                                        <input type="hidden" name="action" value="update_image">
                                        <input type="hidden" name="image_id" value="<?php echo $image['id']; ?>">
                                        
                                        <div class="mb-3">
                                            <label class="form-label">ชื่อรูปภาพ</label>
                                            <input type="text" name="title" class="form-control" 
                                                   value="<?php echo htmlspecialchars($image['title']); ?>" required>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">คำอธิบาย</label>
                                            <textarea name="description" class="form-control" rows="3"><?php echo htmlspecialchars($image['description'] ?? ''); ?></textarea>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">หมวดหมู่</label>
                                            <select name="category" class="form-select">
                                                <?php foreach ($categories as $key => $label): ?>
                                                    <option value="<?php echo $key; ?>" <?php echo $image['category'] === $key ? 'selected' : ''; ?>>
                                                        <?php echo $label; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="is_active" 
                                                       <?php echo $image['is_active'] ? 'checked' : ''; ?>>
                                                <label class="form-check-label">แสดงในเว็บไซต์</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                        <button type="submit" class="btn btn-primary">บันทึก</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Add Image Modal -->
    <div class="modal fade" id="addImageModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เพิ่มรูปภาพใหม่</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_image">
                        
                        <div class="mb-3">
                            <label class="form-label">ไฟล์รูปภาพ</label>
                            <input type="file" name="image_file" class="form-control" accept="image/*">
                            <small class="text-muted">รองรับไฟล์ JPG, PNG, GIF ขนาดไม่เกิน 5MB</small>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">ชื่อรูปภาพ</label>
                            <input type="text" name="title" class="form-control" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">คำอธิบาย</label>
                            <textarea name="description" class="form-control" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">หมวดหมู่</label>
                            <select name="category" class="form-select">
                                <?php foreach ($categories as $key => $label): ?>
                                    <option value="<?php echo $key; ?>"><?php echo $label; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">เพิ่มรูปภาพ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteImage(imageId) {
            if (confirm('คุณต้องการลบรูปภาพนี้หรือไม่?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_image">
                    <input type="hidden" name="image_id" value="${imageId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
