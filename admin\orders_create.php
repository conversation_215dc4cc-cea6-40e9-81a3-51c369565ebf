<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ดึงข้อมูลลูกค้า
$customers = [];
try {
    $stmt = $pdo->query("SELECT id, name, phone, email FROM customers ORDER BY name");
    $customers = $stmt->fetchAll();
} catch (Exception $e) {
    $customers = [
        ['id' => 1, 'name' => 'นาย ก ใจดี', 'phone' => '************', 'email' => '<EMAIL>'],
        ['id' => 2, 'name' => 'นางสาว ข สวยงาม', 'phone' => '************', 'email' => '<EMAIL>']
    ];
}

// ดึงข้อมูลสินค้า
$products = [];
try {
    $stmt = $pdo->query("
        SELECT p.*, pc.name as category_name 
        FROM products p 
        LEFT JOIN product_categories pc ON p.category_id = pc.id 
        WHERE p.status = 'active' 
        ORDER BY p.name
    ");
    $products = $stmt->fetchAll();
} catch (Exception $e) {
    $products = [
        ['id' => 1, 'name' => 'เสื้อกีฬา Syntex โปโล', 'price' => 210.00, 'stock_quantity' => 25, 'category_name' => 'เสื้อกีฬา'],
        ['id' => 2, 'name' => 'เสื้อกีฬา Dri-FIT', 'price' => 250.00, 'stock_quantity' => 15, 'category_name' => 'เสื้อกีฬา'],
        ['id' => 3, 'name' => 'กางเกงกีฬาขาสั้น', 'price' => 180.00, 'stock_quantity' => 20, 'category_name' => 'กางเกงกีฬา']
    ];
}

// จัดการการสร้างคำสั่งซื้อ
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_id = (int)($_POST['customer_id'] ?? 0);
    $customer_name = trim($_POST['customer_name'] ?? '');
    $customer_phone = trim($_POST['customer_phone'] ?? '');
    $customer_email = trim($_POST['customer_email'] ?? '');
    $order_items = $_POST['order_items'] ?? [];
    $notes = trim($_POST['notes'] ?? '');
    $discount_amount = (float)($_POST['discount_amount'] ?? 0);
    $shipping_cost = (float)($_POST['shipping_cost'] ?? 50);
    
    if (empty($order_items)) {
        $error = 'กรุณาเพิ่มสินค้าในคำสั่งซื้อ';
    } else {
        try {
            $pdo->beginTransaction();
            
            // ถ้าไม่มีลูกค้าในระบบ ให้สร้างใหม่
            if ($customer_id == 0 && $customer_name) {
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO customers (name, phone, email, created_at, updated_at)
                        VALUES (?, ?, ?, NOW(), NOW())
                    ");
                    $stmt->execute([$customer_name, $customer_phone, $customer_email]);
                    $customer_id = $pdo->lastInsertId();
                } catch (Exception $e) {
                    // ถ้าไม่สามารถสร้างลูกค้าได้ ให้ใช้ค่า 0
                    $customer_id = 0;
                }
            }
            
            // คำนวณยอดรวม
            $subtotal = 0;
            foreach ($order_items as $item) {
                $subtotal += $item['price'] * $item['quantity'];
            }
            $total_amount = $subtotal - $discount_amount + $shipping_cost;
            
            // สร้างเลขที่คำสั่งซื้อ
            $order_number = 'ORD-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // สร้างคำสั่งซื้อ
            $stmt = $pdo->prepare("
                INSERT INTO orders (
                    order_number, customer_id, customer_name, customer_phone, customer_email,
                    subtotal, discount_amount, shipping_cost, total_amount, status, payment_status,
                    notes, created_by, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'pending', ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                $order_number, $customer_id, $customer_name, $customer_phone, $customer_email,
                $subtotal, $discount_amount, $shipping_cost, $total_amount, $notes, $_SESSION['admin_id']
            ]);
            
            $order_id = $pdo->lastInsertId();
            
            // เพิ่มรายการสินค้า
            foreach ($order_items as $item) {
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO order_items (order_id, product_id, product_name, price, quantity, total_price)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $order_id, $item['product_id'], $item['product_name'], 
                        $item['price'], $item['quantity'], $item['price'] * $item['quantity']
                    ]);
                    
                    // อัปเดตสต็อกสินค้า
                    $stmt = $pdo->prepare("
                        UPDATE products 
                        SET stock_quantity = stock_quantity - ?, updated_at = NOW()
                        WHERE id = ? AND stock_quantity >= ?
                    ");
                    $stmt->execute([$item['quantity'], $item['product_id'], $item['quantity']]);
                } catch (Exception $e) {
                    // ถ้าไม่มีตาราง order_items ก็ข้าม
                }
            }
            
            $pdo->commit();
            $success = 'สร้างคำสั่งซื้อเรียบร้อยแล้ว เลขที่: ' . $order_number;
            
            // รีเฟรชหน้า
            header('Location: orders_create.php?success=' . urlencode($success));
            exit();
            
        } catch (Exception $e) {
            $pdo->rollBack();
            $error = 'ไม่สามารถสร้างคำสั่งซื้อได้: ' . $e->getMessage();
        }
    }
}

// แสดงข้อความ
if (isset($_GET['success'])) {
    $success = $_GET['success'];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>สร้างคำสั่งซื้อ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
        .form-control:focus {
            border-color: #ee501b;
            box-shadow: 0 0 0 0.2rem rgba(238, 80, 27, 0.25);
        }
        .order-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        .order-summary {
            background: linear-gradient(135deg, #ee501b 0%, #d63916 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php" class="active"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">สร้างคำสั่งซื้อ</h4>
                <small class="text-muted">สร้างคำสั่งซื้อใหม่สำหรับลูกค้า</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="orders.php">คำสั่งซื้อ</a></li>
                    <li class="breadcrumb-item active">สร้างคำสั่งซื้อ</li>
                </ol>
            </nav>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    <div class="mt-2">
                        <a href="orders.php" class="btn btn-sm btn-outline-success">ดูรายการคำสั่งซื้อ</a>
                        <a href="orders_create.php" class="btn btn-sm btn-outline-primary">สร้างคำสั่งซื้อใหม่</a>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Order Form -->
            <form method="POST" id="orderForm">
                <div class="row">
                    <!-- ข้อมูลลูกค้า -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">ข้อมูลลูกค้า</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">เลือกลูกค้า</label>
                                            <select class="form-select" id="customerSelect" onchange="selectCustomer()">
                                                <option value="">เลือกลูกค้าจากระบบ</option>
                                                <?php foreach ($customers as $customer): ?>
                                                    <option value="<?php echo $customer['id']; ?>"
                                                            data-name="<?php echo htmlspecialchars($customer['name']); ?>"
                                                            data-phone="<?php echo htmlspecialchars($customer['phone']); ?>"
                                                            data-email="<?php echo htmlspecialchars($customer['email']); ?>">
                                                        <?php echo htmlspecialchars($customer['name']); ?> (<?php echo htmlspecialchars($customer['phone']); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">หรือสร้างลูกค้าใหม่</label>
                                            <button type="button" class="btn btn-outline-primary w-100" onclick="newCustomer()">
                                                <i class="fas fa-plus me-2"></i>ลูกค้าใหม่
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <input type="hidden" name="customer_id" id="customerId" value="0">

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">ชื่อลูกค้า <span class="text-danger">*</span></label>
                                            <input type="text" name="customer_name" id="customerName" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">เบอร์โทร</label>
                                            <input type="text" name="customer_phone" id="customerPhone" class="form-control">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">อีเมล</label>
                                            <input type="email" name="customer_email" id="customerEmail" class="form-control">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- รายการสินค้า -->
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">รายการสินค้า</h5>
                                <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#productModal">
                                    <i class="fas fa-plus me-2"></i>เพิ่มสินค้า
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="orderItems">
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                        <p>ยังไม่มีสินค้าในคำสั่งซื้อ</p>
                                        <p>คลิก "เพิ่มสินค้า" เพื่อเริ่มต้น</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- หมายเหตุ -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">หมายเหตุ</h5>
                            </div>
                            <div class="card-body">
                                <textarea name="notes" class="form-control" rows="3" placeholder="หมายเหตุเพิ่มเติม เช่น สี ไซส์ ข้อความที่ต้องการปัก..."></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- สรุปคำสั่งซื้อ -->
                    <div class="col-lg-4">
                        <div class="order-summary">
                            <h5 class="mb-3">สรุปคำสั่งซื้อ</h5>

                            <div class="d-flex justify-content-between mb-2">
                                <span>ยอดรวมสินค้า:</span>
                                <span id="subtotalAmount">฿0.00</span>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-white">ส่วนลด:</label>
                                <div class="input-group">
                                    <span class="input-group-text">฿</span>
                                    <input type="number" name="discount_amount" id="discountAmount" class="form-control"
                                           value="0" min="0" step="0.01" onchange="calculateTotal()">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-white">ค่าจัดส่ง:</label>
                                <div class="input-group">
                                    <span class="input-group-text">฿</span>
                                    <input type="number" name="shipping_cost" id="shippingCost" class="form-control"
                                           value="50" min="0" step="0.01" onchange="calculateTotal()">
                                </div>
                            </div>

                            <hr class="border-white">

                            <div class="d-flex justify-content-between mb-3">
                                <strong>ยอดรวมทั้งสิ้น:</strong>
                                <strong id="totalAmount">฿50.00</strong>
                            </div>

                            <button type="submit" class="btn btn-light w-100 fw-bold" id="submitBtn" disabled>
                                <i class="fas fa-save me-2"></i>สร้างคำสั่งซื้อ
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
