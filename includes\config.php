<?php
/**
 * Configuration file for Finix Sports
 * Contains all system settings and constants
 */

// Site Configuration
define('SITE_NAME', 'GT-SportDesign');
define('SITE_URL', 'https://gtsportdesign.com');
define('SITE_DESCRIPTION', 'ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง พร้อมเทคโนโลยีการออกแบบที่ทันสมัย');

// Database Configuration
if (!defined('DB_HOST')) define('DB_HOST', 'localhost');
if (!defined('DB_NAME')) define('DB_NAME', 'gtsportd_new');
if (!defined('DB_USER')) define('DB_USER', 'gtsportd_new');
if (!defined('DB_PASS')) define('DB_PASS', 'j2wcGRygN2KDtTk2XuX4');
if (!defined('DB_CHARSET')) define('DB_CHARSET', 'utf8mb4');

// Site Configuration
if (!defined('SITE_URL')) define('SITE_URL', 'https://gtsportdesign.com/CustomDesign');

// Email Configuration
if (!defined('EMAIL_SMTP_HOST')) define('EMAIL_SMTP_HOST', 'smtp.gmail.com');
if (!defined('EMAIL_SMTP_PORT')) define('EMAIL_SMTP_PORT', 587);
if (!defined('EMAIL_SMTP_USERNAME')) define('EMAIL_SMTP_USERNAME', '<EMAIL>');
if (!defined('EMAIL_SMTP_PASSWORD')) define('EMAIL_SMTP_PASSWORD', 'your_app_password'); // Use App Password for Gmail
if (!defined('EMAIL_FROM')) define('EMAIL_FROM', '<EMAIL>');
if (!defined('EMAIL_FROM_NAME')) define('EMAIL_FROM_NAME', 'GT-SportDesign');

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('UPLOAD_PATH', '../uploads/');
define('PRODUCT_IMG_PATH', UPLOAD_PATH . 'products/');
define('CATEGORY_IMG_PATH', UPLOAD_PATH . 'categories/');
define('DESIGN_IMG_PATH', UPLOAD_PATH . 'designs/');

// Payment Configuration
define('PROMPTPAY_ID', '**********'); // Phone number or Tax ID
define('BANK_ACCOUNTS', json_encode([
    'scb' => [
        'bank_name' => 'ธนาคารไทยพาณิชย์',
        'account_number' => '************',
        'account_name' => 'GT-SportDesign'
    ],
    'kbank' => [
        'bank_name' => 'ธนาคารกสิกรไทย',
        'account_number' => '************',
        'account_name' => 'GT-SportDesign'
    ]
]));

// Security Configuration
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 6);
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Design Tool Configuration
define('CANVAS_WIDTH', 400);
define('CANVAS_HEIGHT', 500);
define('MAX_DESIGN_OBJECTS', 50);
define('DESIGN_HISTORY_LIMIT', 20);

// Business Information
define('COMPANY_NAME', 'GT-SportDesign');
define('COMPANY_ADDRESS', '339/7 ม.4 ต.บ้านดู่ อ.เมืองเชียงราย จ.เชียงราย');
define('COMPANY_PHONE', '************');
define('COMPANY_PHONE_2', '************');
define('COMPANY_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');
define('COMPANY_LINE', '@gtsport');
define('COMPANY_FACEBOOK', 'https://www.facebook.com/GTSportDesign.1');

// System Settings
define('TIMEZONE', 'Asia/Bangkok');
define('DATE_FORMAT', 'd/m/Y');
define('DATETIME_FORMAT', 'd/m/Y H:i');
define('CURRENCY', 'THB');
define('CURRENCY_SYMBOL', '฿');

// Environment Configuration
if (!defined('ENVIRONMENT')) define('ENVIRONMENT', 'production'); // development or production

// Error Reporting
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    define('DEBUG_MODE', true);
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', '../logs/error.log');
    define('DEBUG_MODE', false);
}

// Set timezone
date_default_timezone_set(TIMEZONE);

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
ini_set('session.use_strict_mode', 1);

// Helper Functions
function getSiteUrl($path = '') {
    return SITE_URL . ($path ? '/' . ltrim($path, '/') : '');
}

function getUploadUrl($filename) {
    return getSiteUrl('uploads/' . $filename);
}

function formatCurrency($amount) {
    return number_format($amount, 2) . ' ' . CURRENCY_SYMBOL;
}

function formatDate($date, $format = DATE_FORMAT) {
    return date($format, strtotime($date));
}

function formatDateTime($datetime, $format = DATETIME_FORMAT) {
    return date($format, strtotime($datetime));
}

function isLoggedIn() {
    return isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in'] === true;
}

function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

function getCurrentUserName() {
    return $_SESSION['user_name'] ?? null;
}

function getCurrentUserEmail() {
    return $_SESSION['user_email'] ?? null;
}

function redirectToLogin($returnUrl = null) {
    $url = getSiteUrl('auth/login.php');
    if ($returnUrl) {
        $url .= '?redirect=' . urlencode($returnUrl);
    }
    header('Location: ' . $url);
    exit;
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirectToLogin($_SERVER['REQUEST_URI']);
    }
}

function requireAdmin() {
    if (!isAdmin()) {
        if (!isLoggedIn()) {
            redirectToLogin($_SERVER['REQUEST_URI']);
        } else {
            header('HTTP/1.0 403 Forbidden');
            die('Access denied');
        }
    }
}

function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function validatePhone($phone) {
    // Thai phone number validation
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^(0[689]{1}[0-9]{8})$/', $phone);
}

function logError($message, $context = []) {
    $logMessage = date('Y-m-d H:i:s') . ' - ' . $message;
    if (!empty($context)) {
        $logMessage .= ' - Context: ' . json_encode($context);
    }
    error_log($logMessage);
}

// Admin authentication functions
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && !empty($_SESSION['admin_id']);
}

function isAdminRole($role) {
    return isAdminLoggedIn() && isset($_SESSION['admin_role']) && $_SESSION['admin_role'] === $role;
}

function isSuperAdmin() {
    return isAdminRole('super_admin');
}

function isAdminOrAbove() {
    return isAdminLoggedIn() && in_array($_SESSION['admin_role'] ?? '', ['super_admin', 'admin']);
}

function canManageUsers() {
    return isSuperAdmin(); // Only super admin can manage users
}

function getCurrentAdminId() {
    return $_SESSION['admin_id'] ?? null;
}

function getCurrentAdminName() {
    return $_SESSION['admin_name'] ?? null;
}

function getCurrentAdminRole() {
    return $_SESSION['admin_role'] ?? null;
}

function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

function requireSuperAdmin() {
    requireAdminLogin();
    if (!isSuperAdmin()) {
        http_response_code(403);
        die('Access denied: Super Admin access required');
    }
}

function requireAdminOrAbove() {
    requireAdminLogin();
    if (!isAdminOrAbove()) {
        http_response_code(403);
        die('Access denied: Admin access required');
    }
}

// Enhanced password functions
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    // Support both old MD5 (for migration) and new password_hash
    if (strlen($hash) === 32) {
        // Legacy MD5 hash
        return md5($password) === $hash;
    }
    return password_verify($password, $hash);
}

function needsPasswordUpgrade($hash) {
    return strlen($hash) === 32; // MD5 hashes are 32 characters
}

// System logging function
function logSystemAction($user_type, $user_id, $action, $description = null) {
    try {
        $db = getDB();
        $stmt = $db->prepare("
            INSERT INTO system_logs (user_type, user_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $user_type,
            $user_id,
            $action,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        logError('Failed to log system action: ' . $e->getMessage());
    }
}

// Application constants
define('APP_VERSION', '3.0.0');
define('APP_NAME', 'GT-SportDesign Professional Design Studio');
define('COPYRIGHT_YEAR', date('Y'));
define('COPYRIGHT_TEXT', '© ' . COPYRIGHT_YEAR . ' ' . COMPANY_NAME . '. All rights reserved.');

// Feature flags
define('FEATURE_PAYMENT_GATEWAY', true);
define('FEATURE_EMAIL_NOTIFICATIONS', true);
define('FEATURE_CHAT_SUPPORT', true);
define('FEATURE_DESIGN_COLLABORATION', false);
define('FEATURE_MOBILE_APP', false);

// Maintenance mode
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'เว็บไซต์อยู่ระหว่างการปรับปรุง กรุณากลับมาใหม่อีกครั้ง');

// Check maintenance mode
if (MAINTENANCE_MODE && !isAdmin()) {
    http_response_code(503);
    die('<h1>503 Service Unavailable</h1><p>' . MAINTENANCE_MESSAGE . '</p>');
}
