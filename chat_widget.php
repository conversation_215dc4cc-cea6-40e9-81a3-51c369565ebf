<?php
require_once './config/database.php';

$pdo = getDbConnection();

// ดึงข้อมูลการตั้งค่าแชท
$chat_settings = [
    'online_status' => true,
    'welcome_message' => 'สวัสดีครับ! มีอะไรให้ช่วยเหลือไหมครับ?',
    'offline_message' => 'ขณะนี้เราออฟไลน์ กรุณาฝากข้อความไว้ เราจะติดต่อกลับโดยเร็วที่สุด'
];
?>
  <?php include './includes/header.php'; ?>
    
<!-- Chat Widget -->
<div id="chatWidget" class="chat-widget">
    <!-- Chat Toggle Button -->
    <div id="chatToggle" class="chat-toggle" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
        <span class="chat-notification" id="chatNotification" style="display: none;">1</span>
    </div>

    <!-- Chat Window -->
    <div id="chatWindow" class="chat-window" style="display: none;">
        <!-- Chat Header -->
        <div class="chat-header">
            <div class="d-flex align-items-center">
                <div class="agent-avatar me-2">
                    <i class="fas fa-user-headset"></i>
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-0">GT Sport Design</h6>
                    <small class="online-status">
                        <span class="status-dot online"></span>
                        <?= $chat_settings['online_status'] ? 'ออนไลน์' : 'ออฟไลน์' ?>
                    </small>
                </div>
                <button class="btn-close-chat" onclick="toggleChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <!-- Chat Messages -->
        <div class="chat-messages" id="chatMessages">
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p><?= htmlspecialchars($chat_settings['welcome_message']) ?></p>
                    <span class="message-time">เมื่อสักครู่</span>
                </div>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="form-control" id="messageInput"
                       placeholder="พิมพ์ข้อความ..." onkeypress="handleKeyPress(event)">
                <button class="btn btn-primary" type="button" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <div class="quick-action-buttons">
                <button class="btn btn-outline-primary btn-sm" onclick="sendQuickMessage('สอบถามราคา')">
                    สอบถามราคา
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="sendQuickMessage('ขอดูผลงาน')">
                    ขอดูผลงาน
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="sendQuickMessage('สอบถามการจัดส่ง')">
                    การจัดส่ง
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 9999;
    font-family: 'Kanit', sans-serif;
}

.chat-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg,rgb(233, 85, 17) 0%,rgb(224, 68, 20) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgb(255, 84, 4);
    transition: all 0.3s ease;
    position: relative;
}

.chat-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(255, 78, 8, 0.87);
}

.chat-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid #e1e8ed;
}

.chat-header {
    background: linear-gradient(135deg,rgb(245, 65, 11) 0%,rgb(221, 66, 4) 100%);
    color: white;
    padding: 15px;
}

.agent-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.online-status {
    opacity: 0.9;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
}

.status-dot.online {
    background: #2ed573;
    box-shadow: 0 0 10px rgba(46, 213, 115, 0.5);
}

.status-dot.offline {
    background: #ff6b7a;
}

.btn-close-chat {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.btn-close-chat:hover {
    opacity: 1;
}

.chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 15px;
    animation: slideIn 0.3s ease;
}

.user-message {
    justify-content: flex-end;
}

.user-message .message-content {
    background: linear-gradient(135deg,rgb(190, 66, 17) 0%,rgb(202, 63, 8) 100%);
    color: white;
    margin-left: 50px;
}

.bot-message .message-content {
    background: white;
    color: #333;
    margin-right: 50px;
    border: 1px solid #e1e8ed;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-right: 10px;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    margin-left: 10px;
    order: 2;
}

.message-content {
    max-width: 250px;
    padding: 12px 15px;
    border-radius: 18px;
    position: relative;
}

.message-content p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    display: block;
    margin-top: 5px;
}

.chat-input {
    padding: 15px;
    background: white;
    border-top: 1px solid #e1e8ed;
}

.chat-input .form-control {
    border-radius: 25px;
    border: 1px solid #e1e8ed;
    padding: 12px 20px;
    font-size: 14px;
}

.chat-input .btn {
    border-radius: 50%;
    width: 45px;
    height: 45px;
    padding: 0;
    margin-left: 10px;
    background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
    border: none;
}

.quick-actions {
    padding: 10px 15px;
    background: white;
    border-top: 1px solid #e1e8ed;
}

.quick-action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.quick-action-buttons .btn {
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 15px;
    border-color: #11be97;
    color: #11be97;
}

.quick-action-buttons .btn:hover {
    background: #11be97;
    color: white;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive */
@media (max-width: 768px) {
    .chat-window {
        width: 300px;
        height: 450px;
        bottom: 70px;
        right: -10px;
    }

    .chat-toggle {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

/* Scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #11be97;
    border-radius: 10px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #0ea080;
}
</style>

<script>
let chatOpen = false;
let conversationId = null;
let lastMessageTime = 0;

function toggleChat() {
    const chatWindow = document.getElementById('chatWindow');
    const chatToggle = document.getElementById('chatToggle');

    chatOpen = !chatOpen;

    if (chatOpen) {
        chatWindow.style.display = 'flex';
        chatToggle.style.transform = 'rotate(180deg)';
        document.getElementById('messageInput').focus();

        // เริ่ม conversation ใหม่หากยังไม่มี
        if (!conversationId) {
            startConversation();
        }

        // ซ่อน notification
        document.getElementById('chatNotification').style.display = 'none';

        // โหลดข้อความเก่า
        loadMessages();

        // เริ่ม polling สำหรับข้อความใหม่
        startMessagePolling();
    } else {
        chatWindow.style.display = 'none';
        chatToggle.style.transform = 'rotate(0deg)';

        // หยุด polling
        stopMessagePolling();
    }
}

function startConversation() {
    fetch('chat_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'start_conversation'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            conversationId = data.conversation_id;
        }
    })
    .catch(error => {
        console.error('Error starting conversation:', error);
    });
}

function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    // แสดงข้อความของ user ทันที
    addMessage(message, 'user');
    messageInput.value = '';

    // ส่งไปยัง server
    fetch('chat_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'send_message',
            conversation_id: conversationId,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // อัพเดต lastMessageTime
            lastMessageTime = Date.now() / 1000;
        }
    })
    .catch(error => {
        console.error('Error sending message:', error);
    });
}

function sendQuickMessage(message) {
    document.getElementById('messageInput').value = message;
    sendMessage();
}

function addMessage(message, type, time = null) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');

    const timeString = time || new Date().toLocaleTimeString('th-TH', {
        hour: '2-digit',
        minute: '2-digit'
    });

    messageDiv.className = `message ${type}-message`;
    messageDiv.innerHTML = `
        <div class="message-avatar">
            ${type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-headset"></i>'}
        </div>
        <div class="message-content">
            <p>${message}</p>
            <span class="message-time">${timeString}</span>
        </div>
    `;

    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function loadMessages() {
    if (!conversationId) return;

    fetch(`chat_api.php?action=get_messages&conversation_id=${conversationId}&since=${lastMessageTime}`)
    .then(response => response.json())
    .then(data => {
        if (data.success && data.messages.length > 0) {
            data.messages.forEach(message => {
                const messageTime = new Date(message.created_at).toLocaleTimeString('th-TH', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const messageType = message.sender_type === 'customer' ? 'user' : 'bot';
                addMessage(message.message, messageType, messageTime);

                // อัพเดต lastMessageTime
                const messageTimestamp = new Date(message.created_at).getTime() / 1000;
                if (messageTimestamp > lastMessageTime) {
                    lastMessageTime = messageTimestamp;
                }
            });
        }
    })
    .catch(error => {
        console.error('Error loading messages:', error);
    });
}

let pollingInterval;

function startMessagePolling() {
    pollingInterval = setInterval(() => {
        loadMessages();
    }, 3000); // ตรวจสอบทุก 3 วินาที
}

function stopMessagePolling() {
    if (pollingInterval) {
        clearInterval(pollingInterval);
    }
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

// ตรวจสอบข้อความใหม่เมื่อไม่ได้เปิดแชท
function checkNewMessages() {
    if (!chatOpen && conversationId) {
        fetch(`chat_api.php?action=check_new_messages&conversation_id=${conversationId}&since=${lastMessageTime}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.has_new_messages) {
                document.getElementById('chatNotification').style.display = 'flex';
                document.getElementById('chatNotification').textContent = data.new_count;
            }
        })
        .catch(error => {
            console.error('Error checking new messages:', error);
        });
    }
}

// ตรวจสอบข้อความใหม่ทุก 5 วินาที
setInterval(checkNewMessages, 5000);

// Auto-start conversation เมื่อโหลดหน้า
document.addEventListener('DOMContentLoaded', function() {
    // รอ 2 วินาทีแล้วเริ่ม conversation
    setTimeout(() => {
        if (!conversationId) {
            startConversation();
        }
    }, 2000);
});
</script>
  <?php include './includes/.php'; ?>
    