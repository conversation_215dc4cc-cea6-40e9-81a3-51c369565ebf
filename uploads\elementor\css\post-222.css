.elementor-222 .elementor-element.elementor-element-c2a1f8f {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --overlay-opacity: 1;
    --padding-top: 80px;
    --padding-bottom: 180px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-222 .elementor-element.elementor-element-c2a1f8f:not(.elementor-motion-effects-element-type-background),
.elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("");
    background-position: center center;
    background-size: contain;
}
.elementor-222 .elementor-element.elementor-element-c2a1f8f::before,
.elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-background-video-container::before,
.elementor-222 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-video-container::before,
.elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-background-slideshow::before,
.elementor-222 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-slideshow::before,
.elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-motion-effects-container>.elementor-motion-effects-layer::before {
    background-image: url("");
    --background-overlay: '';
    background-position: top left;
    background-repeat: no-repeat;
    background-size: 500px auto;
}

.elementor-widget-heading .elementor-heading-title {
    font-family: var(--e-global-typography-primary-font-family), Kanit;
    font-weight: var(--e-global-typography-primary-font-weight);
    color: var(--e-global-color-primary);
}

.elementor-222 .elementor-element.elementor-element-da4348a {
    text-align: center;
}

.elementor-222 .elementor-element.elementor-element-da4348a .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    color: var(--e-global-color-text);
}

.elementor-widget-nested-tabs.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs[data-touch-mode='false']>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:hover {
    background: var(--e-global-color-accent);
}

.elementor-widget-nested-tabs.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading>.e-n-tab-title[aria-selected="true"],
.elementor-widget-nested-tabs.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs[data-touch-mode='true']>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:hover {
    background: var(--e-global-color-accent);
}

.elementor-widget-nested-tabs.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading> :is(.e-n-tab-title > .e-n-tab-title-text, .e-n-tab-title) {
    font-family: var(--e-global-typography-accent-font-family), Kanit;
    font-weight: var(--e-global-typography-accent-font-weight);
}

.elementor-222 .elementor-element.elementor-element-466e0d7 {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
}

.elementor-widget-gallery .elementor-gallery-item__title {
    font-family: var(--e-global-typography-primary-font-family), Kanit;
    font-weight: var(--e-global-typography-primary-font-weight);
}

.elementor-widget-gallery .elementor-gallery-item__description {
    font-family: var(--e-global-typography-text-font-family), Kanit;
    font-weight: var(--e-global-typography-text-font-weight);
}

.elementor-widget-gallery {
    --galleries-title-color-normal: var(--e-global-color-primary);
    --galleries-title-color-hover: var(--e-global-color-secondary);
    --galleries-pointer-bg-color-hover: var(--e-global-color-accent);
    --gallery-title-color-active: var(--e-global-color-secondary);
    --galleries-pointer-bg-color-active: var(--e-global-color-accent);
}

.elementor-widget-gallery .elementor-gallery-title {
    font-family: var(--e-global-typography-primary-font-family), Kanit;
    font-weight: var(--e-global-typography-primary-font-weight);
}

.elementor-222 .elementor-element.elementor-element-cbfac3b .e-gallery-item:hover .elementor-gallery-item__overlay,
.elementor-222 .elementor-element.elementor-element-cbfac3b .e-gallery-item:focus .elementor-gallery-item__overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.elementor-222 .elementor-element.elementor-element-cbfac3b {
    --image-border-radius: 10px;
    --image-transition-duration: 800ms;
    --overlay-transition-duration: 800ms;
    --content-text-align: center;
    --content-padding: 20px;
    --content-transition-duration: 800ms;
    --content-transition-delay: 800ms;
}

.elementor-222 .elementor-element.elementor-element-3e8ecbe {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
}

.elementor-222 .elementor-element.elementor-element-28fb4d0 .e-gallery-item:hover .elementor-gallery-item__overlay,
.elementor-222 .elementor-element.elementor-element-28fb4d0 .e-gallery-item:focus .elementor-gallery-item__overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.elementor-222 .elementor-element.elementor-element-28fb4d0 {
    --image-border-radius: 10px;
    --image-transition-duration: 800ms;
    --overlay-transition-duration: 800ms;
    --content-text-align: center;
    --content-padding: 20px;
    --content-transition-duration: 800ms;
    --content-transition-delay: 800ms;
}

.elementor-222 .elementor-element.elementor-element-4a256b5 {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
}

.elementor-222 .elementor-element.elementor-element-0bed00f .e-gallery-item:hover .elementor-gallery-item__overlay,
.elementor-222 .elementor-element.elementor-element-0bed00f .e-gallery-item:focus .elementor-gallery-item__overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.elementor-222 .elementor-element.elementor-element-0bed00f {
    --image-border-radius: 10px;
    --image-transition-duration: 800ms;
    --overlay-transition-duration: 800ms;
    --content-text-align: center;
    --content-padding: 20px;
    --content-transition-duration: 800ms;
    --content-transition-delay: 800ms;
}

.elementor-222 .elementor-element.elementor-element-ed5ef4c {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
}

.elementor-222 .elementor-element.elementor-element-f4f02c3 .e-gallery-item:hover .elementor-gallery-item__overlay,
.elementor-222 .elementor-element.elementor-element-f4f02c3 .e-gallery-item:focus .elementor-gallery-item__overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.elementor-222 .elementor-element.elementor-element-f4f02c3 {
    --image-border-radius: 10px;
    --image-transition-duration: 800ms;
    --overlay-transition-duration: 800ms;
    --content-text-align: center;
    --content-padding: 20px;
    --content-transition-duration: 800ms;
    --content-transition-delay: 800ms;
}

.elementor-222 .elementor-element.elementor-element-87520b8 {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
}

.elementor-222 .elementor-element.elementor-element-1d99684 .e-gallery-item:hover .elementor-gallery-item__overlay,
.elementor-222 .elementor-element.elementor-element-1d99684 .e-gallery-item:focus .elementor-gallery-item__overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.elementor-222 .elementor-element.elementor-element-1d99684 {
    --image-border-radius: 10px;
    --image-transition-duration: 800ms;
    --overlay-transition-duration: 800ms;
    --content-text-align: center;
    --content-padding: 20px;
    --content-transition-duration: 800ms;
    --content-transition-delay: 800ms;
}

.elementor-222 .elementor-element.elementor-element-8d463db {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --justify-content: center;
    --align-items: center;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
}

.elementor-222 .elementor-element.elementor-element-8018ea4 .e-gallery-item:hover .elementor-gallery-item__overlay,
.elementor-222 .elementor-element.elementor-element-8018ea4 .e-gallery-item:focus .elementor-gallery-item__overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.elementor-222 .elementor-element.elementor-element-8018ea4 {
    --image-border-radius: 10px;
    --image-transition-duration: 800ms;
    --overlay-transition-duration: 800ms;
    --content-text-align: center;
    --content-padding: 20px;
    --content-transition-duration: 800ms;
    --content-transition-delay: 800ms;
}

.elementor-222 .elementor-element.elementor-element-6ee09f8 {
    --n-tabs-heading-wrap: nowrap;
    --n-tabs-heading-overflow-x: scroll;
    --n-tabs-title-white-space: nowrap;
    --n-tabs-title-gap: 0px;
    --n-tabs-title-color: var(--e-global-color-text);
    --n-tabs-title-color-active: var(--e-global-color-primary);
}

.elementor-222 .elementor-element.elementor-element-6ee09f8>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading>.e-n-tab-title[aria-selected='false']:not( :hover) {
    background: #00000000;
}

.elementor-222 .elementor-element.elementor-element-6ee09f8.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs[data-touch-mode='false']>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:hover {
    background: #FFFFFF00;
    border-style: solid;
    border-width: 0px 0px 2px 0px;
    border-color: var(--e-global-color-primary);
}

.elementor-222 .elementor-element.elementor-element-6ee09f8.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading>.e-n-tab-title[aria-selected="true"],
.elementor-222 .elementor-element.elementor-element-6ee09f8.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs[data-touch-mode='true']>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:hover {
    background: #FFFFFF00;
    border-style: solid;
    border-width: 0px 0px 2px 0px;
    border-color: var(--e-global-color-primary);
}

.elementor-222 .elementor-element.elementor-element-6ee09f8.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:not( :hover) {
    border-style: solid;
    border-width: 0px 0px 2px 0px;
    border-color: #FFFFFF00;
}

.elementor-222 .elementor-element.elementor-element-6ee09f8 [data-touch-mode="false"] .e-n-tab-title[aria-selected="false"]:hover {
    --n-tabs-title-color-hover: var(--e-global-color-primary);
}

.elementor-222 .elementor-element.elementor-element-44101a0 {
    --display: flex;
    --min-height: 300px;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --align-items: center;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-222 .elementor-element.elementor-element-44101a0:not(.elementor-motion-effects-element-type-background),
.elementor-222 .elementor-element.elementor-element-44101a0>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("https://www.finixsports.com/wp-content/uploads/2024/07/Banner_BG_1-scaled.jpg");
    background-position: center center;
}

.elementor-widget-image .widget-image-caption {
    color: var(--e-global-color-text);
    font-family: var(--e-global-typography-text-font-family), Kanit;
    font-weight: var(--e-global-typography-text-font-weight);
}

.elementor-222 .elementor-element.elementor-element-d70e044>.elementor-widget-container {
    margin: -150px 0px 0px 0px;
}

.elementor-222 .elementor-element.elementor-element-d70e044.elementor-element {
    --align-self: flex-end;
}

.elementor-222 .elementor-element.elementor-element-d70e044 img {
    width: 450px;
}

.elementor-222 .elementor-element.elementor-element-92e456b.elementor-element {
    --flex-grow: 1;
    --flex-shrink: 0;
}

.elementor-222 .elementor-element.elementor-element-92e456b {
    text-align: center;
}

.elementor-222 .elementor-element.elementor-element-92e456b .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 48px;
    font-weight: 600;
    font-style: normal;
    line-height: 65px;
    color: var(--e-global-color-5677475);
}

@media(max-width:1150px) {

    .elementor-222 .elementor-element.elementor-element-c2a1f8f::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-background-video-container::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-video-container::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-background-slideshow::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-slideshow::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-motion-effects-container>.elementor-motion-effects-layer::before {
        background-size: 350px auto;
    }

    .elementor-222 .elementor-element.elementor-element-44101a0 {
        --min-height: 150px;
    }

    .elementor-222 .elementor-element.elementor-element-d70e044 img {
        width: 213px;
    }

    .elementor-222 .elementor-element.elementor-element-92e456b .elementor-heading-title {
        font-size: 35px;
        line-height: 1.3em;
    }
}

@media(max-width:767px) {

    .elementor-222 .elementor-element.elementor-element-c2a1f8f::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-background-video-container::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-video-container::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-background-slideshow::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-slideshow::before,
    .elementor-222 .elementor-element.elementor-element-c2a1f8f>.elementor-motion-effects-container>.elementor-motion-effects-layer::before {
        background-size: 250px auto;
    }

    .elementor-222 .elementor-element.elementor-element-6ee09f8 {
        --n-tabs-heading-wrap: nowrap;
        --n-tabs-heading-overflow-x: scroll;
        --n-tabs-title-white-space: nowrap;
    }

    .elementor-222 .elementor-element.elementor-element-44101a0 {
        --padding-top: 80px;
        --padding-bottom: 0px;
        --padding-left: 10px;
        --padding-right: 10px;
    }

    .elementor-222 .elementor-element.elementor-element-d70e044>.elementor-widget-container {
        margin: 0px 0px 0px 0px;
    }

    .elementor-222 .elementor-element.elementor-element-d70e044.elementor-element {
        --align-self: center;
        --order: 99999
            /* order end hack */
        ;
    }

    .elementor-222 .elementor-element.elementor-element-d70e044 {
        text-align: center;
    }

    .elementor-222 .elementor-element.elementor-element-d70e044 img {
        width: 360px;
    }

    .elementor-222 .elementor-element.elementor-element-92e456b .elementor-heading-title {
        font-size: 25px;
    }
}

/* Start custom CSS for heading, class: .elementor-element-da4348a */
.elementor-222 .elementor-element.elementor-element-da4348a .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 500;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-cbfac3b */
.elementor-222 .elementor-element.elementor-element-cbfac3b .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-28fb4d0 */
.elementor-222 .elementor-element.elementor-element-28fb4d0 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-0bed00f */
.elementor-222 .elementor-element.elementor-element-0bed00f .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-f4f02c3 */
.elementor-222 .elementor-element.elementor-element-f4f02c3 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-1d99684 */
.elementor-222 .elementor-element.elementor-element-1d99684 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-8018ea4 */
.elementor-222 .elementor-element.elementor-element-8018ea4 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-cbfac3b */
.elementor-222 .elementor-element.elementor-element-cbfac3b .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-28fb4d0 */
.elementor-222 .elementor-element.elementor-element-28fb4d0 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-0bed00f */
.elementor-222 .elementor-element.elementor-element-0bed00f .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-f4f02c3 */
.elementor-222 .elementor-element.elementor-element-f4f02c3 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-1d99684 */
.elementor-222 .elementor-element.elementor-element-1d99684 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-8018ea4 */
.elementor-222 .elementor-element.elementor-element-8018ea4 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-cbfac3b */
.elementor-222 .elementor-element.elementor-element-cbfac3b .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-28fb4d0 */
.elementor-222 .elementor-element.elementor-element-28fb4d0 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-0bed00f */
.elementor-222 .elementor-element.elementor-element-0bed00f .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-f4f02c3 */
.elementor-222 .elementor-element.elementor-element-f4f02c3 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-1d99684 */
.elementor-222 .elementor-element.elementor-element-1d99684 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-8018ea4 */
.elementor-222 .elementor-element.elementor-element-8018ea4 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-cbfac3b */
.elementor-222 .elementor-element.elementor-element-cbfac3b .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-28fb4d0 */
.elementor-222 .elementor-element.elementor-element-28fb4d0 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-0bed00f */
.elementor-222 .elementor-element.elementor-element-0bed00f .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-f4f02c3 */
.elementor-222 .elementor-element.elementor-element-f4f02c3 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-1d99684 */
.elementor-222 .elementor-element.elementor-element-1d99684 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-8018ea4 */
.elementor-222 .elementor-element.elementor-element-8018ea4 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-cbfac3b */
.elementor-222 .elementor-element.elementor-element-cbfac3b .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-28fb4d0 */
.elementor-222 .elementor-element.elementor-element-28fb4d0 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-0bed00f */
.elementor-222 .elementor-element.elementor-element-0bed00f .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-f4f02c3 */
.elementor-222 .elementor-element.elementor-element-f4f02c3 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-1d99684 */
.elementor-222 .elementor-element.elementor-element-1d99684 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-8018ea4 */
.elementor-222 .elementor-element.elementor-element-8018ea4 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-cbfac3b */
.elementor-222 .elementor-element.elementor-element-cbfac3b .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-28fb4d0 */
.elementor-222 .elementor-element.elementor-element-28fb4d0 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-0bed00f */
.elementor-222 .elementor-element.elementor-element-0bed00f .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-f4f02c3 */
.elementor-222 .elementor-element.elementor-element-f4f02c3 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-1d99684 */
.elementor-222 .elementor-element.elementor-element-1d99684 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-8018ea4 */
.elementor-222 .elementor-element.elementor-element-8018ea4 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-cbfac3b */
.elementor-222 .elementor-element.elementor-element-cbfac3b .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-28fb4d0 */
.elementor-222 .elementor-element.elementor-element-28fb4d0 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-0bed00f */
.elementor-222 .elementor-element.elementor-element-0bed00f .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-f4f02c3 */
.elementor-222 .elementor-element.elementor-element-f4f02c3 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-1d99684 */
.elementor-222 .elementor-element.elementor-element-1d99684 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */
/* Start custom CSS for gallery, class: .elementor-element-8018ea4 */
.elementor-222 .elementor-element.elementor-element-8018ea4 .elementor-widget-container div {
    --aspect-ratio: 85%;
}

/* End custom CSS */