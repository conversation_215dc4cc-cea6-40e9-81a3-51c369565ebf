<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ดึงการตั้งค่าปัจจุบัน
$settings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    // ถ้าไม่มีตาราง system_settings ให้ใช้ค่าเริ่มต้น
    $settings = [
        'site_name' => 'GT-SportDesign',
        'site_description' => 'ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง',
        'contact_email' => '<EMAIL>',
        'contact_phone' => '************',
        'contact_line' => '@gtsport',
        'facebook_page' => 'https://www.facebook.com/GTSportDesign.1',
        'min_order_amount' => '500',
        'shipping_cost' => '50',
        'free_shipping_amount' => '1000',
        'promptpay_id' => '0855599164',
        'primary_color' => '#eb4e17',
        'secondary_color' => '#ff6b35'
    ];
}

// จัดการการบันทึกการตั้งค่า
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        foreach ($_POST as $key => $value) {
            if ($key !== 'action') {
                // อัปเดตหรือเพิ่มการตั้งค่า
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO system_settings (setting_key, setting_value, updated_at)
                        VALUES (?, ?, NOW())
                        ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()
                    ");
                    $stmt->execute([$key, $value, $value]);
                } catch (Exception $e) {
                    // ถ้าไม่มีตาราง system_settings ให้ข้าม
                }
            }
        }
        $success = 'บันทึกการตั้งค่าเรียบร้อยแล้ว';

        // รีเฟรชข้อมูลการตั้งค่า
        foreach ($_POST as $key => $value) {
            if ($key !== 'action') {
                $settings[$key] = $value;
            }
        }
    } catch (Exception $e) {
        $error = 'เกิดข้อผิดพลาดในการบันทึก: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตั้งค่าระบบ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .form-control:focus {
            border-color: #ee501b;
            box-shadow: 0 0 0 0.2rem rgba(238, 80, 27, 0.25);
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php" class="active"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">ตั้งค่าระบบ</h4>
                <small class="text-muted">จัดการการตั้งค่าเว็บไซต์และระบบ</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="row">
                    <!-- ข้อมูลเว็บไซต์ -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-globe me-2"></i>ข้อมูลเว็บไซต์</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">ชื่อเว็บไซต์</label>
                                    <input type="text" name="site_name" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['site_name'] ?? ''); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">คำอธิบายเว็บไซต์</label>
                                    <textarea name="site_description" class="form-control" rows="3"><?php echo htmlspecialchars($settings['site_description'] ?? ''); ?></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">สีหลัก</label>
                                    <input type="color" name="primary_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['primary_color'] ?? '#eb4e17'); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">สีรอง</label>
                                    <input type="color" name="secondary_color" class="form-control form-control-color"
                                           value="<?php echo htmlspecialchars($settings['secondary_color'] ?? '#ff6b35'); ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ข้อมูลติดต่อ -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-phone me-2"></i>ข้อมูลติดต่อ</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">อีเมล</label>
                                    <input type="email" name="contact_email" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['contact_email'] ?? ''); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">เบอร์โทร</label>
                                    <input type="text" name="contact_phone" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['contact_phone'] ?? ''); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">LINE ID</label>
                                    <input type="text" name="contact_line" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['contact_line'] ?? ''); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Facebook Page</label>
                                    <input type="url" name="facebook_page" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['facebook_page'] ?? ''); ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- การตั้งค่าการสั่งซื้อ -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i>การตั้งค่าการสั่งซื้อ</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">ยอดขั้นต่ำในการสั่งซื้อ (บาท)</label>
                                    <input type="number" name="min_order_amount" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['min_order_amount'] ?? '500'); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ค่าจัดส่งมาตรฐาน (บาท)</label>
                                    <input type="number" name="shipping_cost" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['shipping_cost'] ?? '50'); ?>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ยอดสั่งซื้อที่ได้จัดส่งฟรี (บาท)</label>
                                    <input type="number" name="free_shipping_amount" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['free_shipping_amount'] ?? '1000'); ?>">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- การตั้งค่าการชำระเงิน -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>การตั้งค่าการชำระเงิน</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">PromptPay ID</label>
                                    <input type="text" name="promptpay_id" class="form-control"
                                           value="<?php echo htmlspecialchars($settings['promptpay_id'] ?? ''); ?>"
                                           placeholder="เบอร์โทรหรือเลขประจำตัวผู้เสียภาษี">
                                </div>
                                <div class="alert alert-info">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        PromptPay ID ใช้สำหรับการชำระเงินผ่าน QR Code
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>บันทึกการตั้งค่า
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>