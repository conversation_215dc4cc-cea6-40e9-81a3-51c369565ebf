.elementor-190 .elementor-element.elementor-element-c2a1f8f {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --overlay-opacity: 1;
    --padding-top: 80px;
    --padding-bottom: 80px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-c2a1f8f:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("https://www.finixsports.com/wp-content/uploads/2024/07/White_dot_BG-scaled.jpg");
    background-position: center center;
    background-size: contain;
}

.elementor-190 .elementor-element.elementor-element-c2a1f8f::before,
.elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-background-video-container::before,
.elementor-190 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-video-container::before,
.elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-background-slideshow::before,
.elementor-190 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-slideshow::before,
.elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-motion-effects-container>.elementor-motion-effects-layer::before {
    background-image: url("");
    --background-overlay: '';
    background-position: top left;
    background-repeat: no-repeat;
    background-size: 500px auto;
}

.elementor-widget-heading .elementor-heading-title {
    font-family: var(--e-global-typography-primary-font-family), Kanit;
    font-weight: var(--e-global-typography-primary-font-weight);
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-da4348a {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-da4348a .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    color: var(--e-global-color-text);
}

.elementor-widget-nested-tabs.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs[data-touch-mode='false']>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:hover {
    background: var(--e-global-color-accent);
}

.elementor-widget-nested-tabs.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading>.e-n-tab-title[aria-selected="true"],
.elementor-widget-nested-tabs.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs[data-touch-mode='true']>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:hover {
    background: var(--e-global-color-accent);
}

.elementor-widget-nested-tabs.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading> :is(.e-n-tab-title > .e-n-tab-title-text, .e-n-tab-title) {
    font-family: var(--e-global-typography-accent-font-family), Kanit;
    font-weight: var(--e-global-typography-accent-font-weight);
}

.elementor-190 .elementor-element.elementor-element-466e0d7 {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-e3848ac {
    --display: flex;
}

.elementor-190 .elementor-element.elementor-element-3b7fe59 {
    --spacer-size: 20px;
}

.elementor-190 .elementor-element.elementor-element-e5f07f8 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-e5f07f8 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-widget-text-editor {
    font-family: var(--e-global-typography-text-font-family), Kanit;
    font-weight: var(--e-global-typography-text-font-weight);
    color: var(--e-global-color-text);
}

.elementor-widget-text-editor.elementor-drop-cap-view-stacked .elementor-drop-cap {
    background-color: var(--e-global-color-primary);
}

.elementor-widget-text-editor.elementor-drop-cap-view-framed .elementor-drop-cap,
.elementor-widget-text-editor.elementor-drop-cap-view-default .elementor-drop-cap {
    color: var(--e-global-color-primary);
    border-color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-38e7349 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-95411fb {
    --display: flex;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --padding-top: 30px;
    --padding-bottom: 30px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-95411fb:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-95411fb>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-12e8165.elementor-element {
    --flex-grow: 1;
    --flex-shrink: 0;
}

.elementor-190 .elementor-element.elementor-element-12e8165 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 30px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-85ade00 {
    --display: grid;
    --e-con-grid-template-columns: repeat(4, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-widget-icon-box.elementor-view-stacked .elementor-icon {
    background-color: var(--e-global-color-primary);
}

.elementor-widget-icon-box.elementor-view-framed .elementor-icon,
.elementor-widget-icon-box.elementor-view-default .elementor-icon {
    fill: var(--e-global-color-primary);
    color: var(--e-global-color-primary);
    border-color: var(--e-global-color-primary);
}

.elementor-widget-icon-box .elementor-icon-box-title,
.elementor-widget-icon-box .elementor-icon-box-title a {
    font-family: var(--e-global-typography-primary-font-family), Kanit;
    font-weight: var(--e-global-typography-primary-font-weight);
}

.elementor-widget-icon-box .elementor-icon-box-title {
    color: var(--e-global-color-primary);
}

.elementor-widget-icon-box:has(:hover) .elementor-icon-box-title,
.elementor-widget-icon-box:has(:focus) .elementor-icon-box-title {
    color: var(--e-global-color-primary);
}

.elementor-widget-icon-box .elementor-icon-box-description {
    font-family: var(--e-global-typography-text-font-family), Kanit;
    font-weight: var(--e-global-typography-text-font-weight);
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-46357da .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-46357da {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-46357da .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-46357da .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-46357da .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-46357da .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-46357da .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-67d8e74 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-67d8e74 {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-67d8e74 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-67d8e74 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-67d8e74 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-67d8e74 .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-67d8e74 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-5fa38cf .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-5fa38cf {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-5fa38cf .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-5fa38cf .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-5fa38cf .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-5fa38cf .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-5fa38cf .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-145d7c5 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-145d7c5 {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-145d7c5 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-145d7c5 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-145d7c5 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-145d7c5 .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-145d7c5 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-92595a2 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --padding-top: 80px;
    --padding-bottom: 80px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-92595a2:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-92595a2>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("https://www.finixsports.com/wp-content/uploads/2024/07/White_dot_BG-scaled.jpg");
    background-position: center center;
    background-size: cover;
}

.elementor-190 .elementor-element.elementor-element-ce09238 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-ce09238 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-65cba27 {
    --spacer-size: 20px;
}

.elementor-190 .elementor-element.elementor-element-1c1827c {
    --display: grid;
    --e-con-grid-template-columns: repeat(7, 1fr);
    --e-con-grid-template-rows: repeat(2, 1fr);
    --gap: 20px 10px;
    --row-gap: 20px;
    --column-gap: 10px;
    --grid-auto-flow: row;
    --justify-items: center;
    --align-items: center;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-15719dc .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-15719dc {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-15719dc .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-15719dc .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-15719dc .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-15719dc .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-15719dc .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-widget-icon.elementor-view-stacked .elementor-icon {
    background-color: var(--e-global-color-primary);
}

.elementor-widget-icon.elementor-view-framed .elementor-icon,
.elementor-widget-icon.elementor-view-default .elementor-icon {
    color: var(--e-global-color-primary);
    border-color: var(--e-global-color-primary);
}

.elementor-widget-icon.elementor-view-framed .elementor-icon,
.elementor-widget-icon.elementor-view-default .elementor-icon svg {
    fill: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-a1b97b5 .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-a1b97b5 .elementor-icon {
    font-size: 25px;
}

.elementor-190 .elementor-element.elementor-element-a1b97b5 .elementor-icon svg {
    height: 25px;
}

.elementor-190 .elementor-element.elementor-element-238b392 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-238b392 {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-238b392 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-238b392 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-238b392 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-238b392 .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-238b392 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-779e3fe .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-779e3fe .elementor-icon {
    font-size: 25px;
}

.elementor-190 .elementor-element.elementor-element-779e3fe .elementor-icon svg {
    height: 25px;
}

.elementor-190 .elementor-element.elementor-element-2e2666f .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-2e2666f {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-2e2666f .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-2e2666f .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-2e2666f .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-2e2666f .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-2e2666f .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-92a03d9 .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-92a03d9 .elementor-icon {
    font-size: 25px;
}

.elementor-190 .elementor-element.elementor-element-92a03d9 .elementor-icon svg {
    height: 25px;
}

.elementor-190 .elementor-element.elementor-element-64b0c1b .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-64b0c1b {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-64b0c1b .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-64b0c1b .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-64b0c1b .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-64b0c1b .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-64b0c1b .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-e54f5e2 .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-e54f5e2 .elementor-icon {
    font-size: 25px;
}

.elementor-190 .elementor-element.elementor-element-e54f5e2 .elementor-icon svg {
    height: 25px;
}

.elementor-190 .elementor-element.elementor-element-1465bd2 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-1465bd2 {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-1465bd2 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-1465bd2 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-1465bd2 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-1465bd2 .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-1465bd2 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-e3e7302 .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-e3e7302 .elementor-icon {
    font-size: 25px;
}

.elementor-190 .elementor-element.elementor-element-e3e7302 .elementor-icon svg {
    height: 25px;
}

.elementor-190 .elementor-element.elementor-element-8a6664f .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-8a6664f {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-8a6664f .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-8a6664f .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-8a6664f .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-8a6664f .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-8a6664f .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-6d2eda0 .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-6d2eda0 .elementor-icon {
    font-size: 25px;
}

.elementor-190 .elementor-element.elementor-element-6d2eda0 .elementor-icon svg {
    height: 25px;
}

.elementor-190 .elementor-element.elementor-element-4a88ed5 .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-4a88ed5.elementor-view-stacked .elementor-icon {
    background-color: #FF480500;
}

.elementor-190 .elementor-element.elementor-element-4a88ed5.elementor-view-framed .elementor-icon,
.elementor-190 .elementor-element.elementor-element-4a88ed5.elementor-view-default .elementor-icon {
    color: #FF480500;
    border-color: #FF480500;
}

.elementor-190 .elementor-element.elementor-element-4a88ed5.elementor-view-framed .elementor-icon,
.elementor-190 .elementor-element.elementor-element-4a88ed5.elementor-view-default .elementor-icon svg {
    fill: #FF480500;
}

.elementor-190 .elementor-element.elementor-element-4a88ed5 .elementor-icon {
    font-size: 25px;
}

.elementor-190 .elementor-element.elementor-element-4a88ed5 .elementor-icon svg {
    height: 25px;
}

.elementor-190 .elementor-element.elementor-element-c7ce87c .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-c7ce87c {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-c7ce87c .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-c7ce87c .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-c7ce87c .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-c7ce87c .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-c7ce87c .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-61c7bdd .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-61c7bdd .elementor-icon {
    font-size: 25px;
}

.elementor-190 .elementor-element.elementor-element-61c7bdd .elementor-icon svg {
    height: 25px;
}

.elementor-190 .elementor-element.elementor-element-1a772d7 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-1a772d7 {
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-1a772d7 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-1a772d7 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-1a772d7 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
}

.elementor-190 .elementor-element.elementor-element-1a772d7 .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-1a772d7 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-aee4c6c {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --padding-top: 30px;
    --padding-bottom: 30px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-aee4c6c:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-aee4c6c>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-89331b8 {
    --display: grid;
    --e-con-grid-template-columns: repeat(4, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-widget-image .widget-image-caption {
    color: var(--e-global-color-text);
    font-family: var(--e-global-typography-text-font-family), Kanit;
    font-weight: var(--e-global-typography-text-font-weight);
}

.elementor-190 .elementor-element.elementor-element-23f60c0 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-4f4cf7c img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-bcbbc7a img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-26afa1a img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-widget-button .elementor-button {
    background-color: var(--e-global-color-accent);
    font-family: var(--e-global-typography-accent-font-family), Kanit;
    font-weight: var(--e-global-typography-accent-font-weight);
}

.elementor-190 .elementor-element.elementor-element-39ea3f4 .elementor-button {
    font-family: "Kanit", Kanit;
    font-size: 18px;
    font-weight: 500;
    padding: 20px 30px 20px 30px;
}

.elementor-190 .elementor-element.elementor-element-184b697 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --padding-top: 80px;
    --padding-bottom: 80px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-184b697:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-184b697>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("https://www.finixsports.com/wp-content/uploads/2024/07/White_dot_BG-scaled.jpg");
    background-position: center center;
    background-size: cover;
}

.elementor-190 .elementor-element.elementor-element-93cc379 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-93cc379 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-0427ebb {
    --display: grid;
    --e-con-grid-template-columns: repeat(4, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-190 .elementor-element.elementor-element-64fd267 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-64fd267:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-64fd267>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-3801b6f img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-83846b1 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-05924ad {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-b899b46 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-a85e7e2 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-a85e7e2:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-a85e7e2>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-45c543e img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-3227bdb .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-75db980 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-62a0084 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-2168791 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-2168791:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-2168791>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-209fe19 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-d34a31a .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-2588a3a {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-8c2b46f {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-c41a398 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-c41a398:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-c41a398>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-6b8bdc4 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-972eb07 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-12d941d {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-959b5e2 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-9972716 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --overlay-opacity: 1;
    --padding-top: 80px;
    --padding-bottom: 80px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-9972716:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-9972716>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("https://www.finixsports.com/wp-content/uploads/2024/07/White_dot_BG-scaled.jpg");
    background-position: center center;
    background-size: contain;
}

.elementor-190 .elementor-element.elementor-element-9972716::before,
.elementor-190 .elementor-element.elementor-element-9972716>.elementor-background-video-container::before,
.elementor-190 .elementor-element.elementor-element-9972716>.e-con-inner>.elementor-background-video-container::before,
.elementor-190 .elementor-element.elementor-element-9972716>.elementor-background-slideshow::before,
.elementor-190 .elementor-element.elementor-element-9972716>.e-con-inner>.elementor-background-slideshow::before,
.elementor-190 .elementor-element.elementor-element-9972716>.elementor-motion-effects-container>.elementor-motion-effects-layer::before {
    background-color: transparent;
    --background-overlay: '';
    background-image: linear-gradient(180deg, var(--e-global-color-15896d6) 0%, #FFFFFF00 100%);
}

.elementor-190 .elementor-element.elementor-element-164be15 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-weight: 600;
    font-style: normal;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-ed95a22 {
    --display: grid;
    --e-con-grid-template-columns: repeat(2, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-190 .elementor-element.elementor-element-f5e9a6f img {
    width: 650px;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-6e85cf1 img {
    width: 650px;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-0425053 .elementor-button {
    background-color: transparent;
    font-family: "Kanit", Kanit;
    font-size: 18px;
    font-weight: 500;
    background-image: linear-gradient(180deg, var(--e-global-color-80a9e88) 0%, var(--e-global-color-80a9e88) 100%);
    padding: 20px 30px 20px 30px;
}

.elementor-190 .elementor-element.elementor-element-21841a9 {
    --display: grid;
    --e-con-grid-template-columns: repeat(3, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-190 .elementor-element.elementor-element-7ccb16b img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-ef36d96 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-741b39c img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-9868060 .elementor-button {
    font-family: "Kanit", Kanit;
    font-size: 18px;
    font-weight: 500;
    padding: 20px 30px 20px 30px;
}

.elementor-190 .elementor-element.elementor-element-6dd6d06 {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-34e58a5 {
    --display: flex;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-62bcccd img {
    width: 1920px;
    filter: brightness(110%) contrast(110%) saturate(100%) blur(0px) hue-rotate(0deg);
}

.elementor-190 .elementor-element.elementor-element-3f40cbb img {
    width: 1920px;
    max-width: 1%;
    height: 0%;
    object-fit: contain;
    object-position: top center;
    filter: brightness(110%) contrast(110%) saturate(100%) blur(0px) hue-rotate(0deg);
}

.elementor-190 .elementor-element.elementor-element-c5f4706 {
    --display: flex;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --padding-top: 80px;
    --padding-bottom: 40px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-c5f4706:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-c5f4706>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-917eb52 {
    --display: flex;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --justify-content: center;
    --align-items: center;
}

.elementor-190 .elementor-element.elementor-element-423b751>.elementor-widget-container {
    margin: -65px 0px -65px 0px;
}

.elementor-190 .elementor-element.elementor-element-423b751 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-423b751 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 46px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-113d881 {
    --display: flex;
    --flex-direction: row;
    --container-widget-width: initial;
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-746e032 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-746e032 {
    --icon-box-icon-margin: 0px;
}

.elementor-190 .elementor-element.elementor-element-746e032 .elementor-icon-box-title {
    margin-bottom: 10px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-746e032 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-746e032 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-746e032 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 600;
    line-height: 25px;
}

.elementor-190 .elementor-element.elementor-element-746e032 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-70ecf62 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-70ecf62 {
    --icon-box-icon-margin: 0px;
}

.elementor-190 .elementor-element.elementor-element-70ecf62 .elementor-icon-box-title {
    margin-bottom: 10px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-70ecf62 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-70ecf62 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-70ecf62 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 600;
    line-height: 25px;
}

.elementor-190 .elementor-element.elementor-element-70ecf62 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-2c11845 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-2c11845 {
    --icon-box-icon-margin: 0px;
}

.elementor-190 .elementor-element.elementor-element-2c11845 .elementor-icon-box-title {
    margin-bottom: 10px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-2c11845 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-2c11845 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-2c11845 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 600;
    line-height: 25px;
}

.elementor-190 .elementor-element.elementor-element-2c11845 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-b2e293d .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-b2e293d {
    --icon-box-icon-margin: 0px;
}

.elementor-190 .elementor-element.elementor-element-b2e293d .elementor-icon-box-title {
    margin-bottom: 10px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-b2e293d .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-b2e293d .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-b2e293d .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 600;
    line-height: 25px;
}

.elementor-190 .elementor-element.elementor-element-b2e293d .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-a3ce007 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-a3ce007 {
    --icon-box-icon-margin: 0px;
}

.elementor-190 .elementor-element.elementor-element-a3ce007 .elementor-icon-box-title {
    margin-bottom: 10px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-a3ce007 .elementor-icon {
    font-size: 100px;
}

.elementor-190 .elementor-element.elementor-element-a3ce007 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-a3ce007 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 600;
    line-height: 25px;
}

.elementor-190 .elementor-element.elementor-element-a3ce007 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-84f5b9c {
    --display: flex;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-84f5b9c:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-84f5b9c>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-5f21e52 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --padding-top: 40px;
    --padding-bottom: 40px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-febac74 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-febac74 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-836bd9a {
    --spacer-size: 20px;
}

.elementor-190 .elementor-element.elementor-element-eb239b4 {
    --e-image-carousel-slides-to-show: 1;
}

.elementor-190 .elementor-element.elementor-element-eb239b4 .elementor-swiper-button.elementor-swiper-button-prev,
.elementor-190 .elementor-element.elementor-element-eb239b4 .elementor-swiper-button.elementor-swiper-button-next {
    font-size: 40px;
}

.elementor-190 .elementor-element.elementor-element-eb239b4 .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    background: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-eb239b4 .swiper-pagination-bullet:not(.swiper-pagination-bullet-active) {
    background: #F8680075;
    opacity: 1;
}

.elementor-190 .elementor-element.elementor-element-eb239b4 .elementor-image-carousel-wrapper .elementor-image-carousel .swiper-slide-image {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-113fada {
    --spacer-size: 20px;
}

.elementor-190 .elementor-element.elementor-element-e2cd8cd .elementor-button {
    background-color: transparent;
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
    background-image: linear-gradient(180deg, #36C213 0%, #28A00A 100%);
    padding: 20px 30px 20px 30px;
}

.elementor-190 .elementor-element.elementor-element-e2cd8cd .elementor-button:hover,
.elementor-190 .elementor-element.elementor-element-e2cd8cd .elementor-button:focus {
    background-color: #4BDD26;
}

.elementor-190 .elementor-element.elementor-element-e2cd8cd .elementor-button-content-wrapper {
    flex-direction: row;
}

.elementor-190 .elementor-element.elementor-element-6c24960 img {
    width: 100%;
}

.elementor-190 .elementor-element.elementor-element-2e856bb {
    --display: flex;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-2e856bb:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-2e856bb>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-cd94b0c {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --padding-top: 40px;
    --padding-bottom: 40px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-0700afc {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-0700afc .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-e5fedc5 {
    --spacer-size: 20px;
}

.elementor-widget-gallery .elementor-gallery-item__title {
    font-family: var(--e-global-typography-primary-font-family), Kanit;
    font-weight: var(--e-global-typography-primary-font-weight);
}

.elementor-widget-gallery .elementor-gallery-item__description {
    font-family: var(--e-global-typography-text-font-family), Kanit;
    font-weight: var(--e-global-typography-text-font-weight);
}

.elementor-widget-gallery {
    --galleries-title-color-normal: var(--e-global-color-primary);
    --galleries-title-color-hover: var(--e-global-color-secondary);
    --galleries-pointer-bg-color-hover: var(--e-global-color-accent);
    --gallery-title-color-active: var(--e-global-color-secondary);
    --galleries-pointer-bg-color-active: var(--e-global-color-accent);
}

.elementor-widget-gallery .elementor-gallery-title {
    font-family: var(--e-global-typography-primary-font-family), Kanit;
    font-weight: var(--e-global-typography-primary-font-weight);
}

.elementor-190 .elementor-element.elementor-element-08c90bc .e-gallery-item:hover .elementor-gallery-item__overlay,
.elementor-190 .elementor-element.elementor-element-08c90bc .e-gallery-item:focus .elementor-gallery-item__overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.elementor-190 .elementor-element.elementor-element-08c90bc {
    --image-border-radius: 10px;
    --image-transition-duration: 800ms;
    --overlay-transition-duration: 400ms;
    --content-text-align: center;
    --content-padding: 20px;
    --content-transition-duration: 800ms;
    --content-transition-delay: 800ms;
}

.elementor-190 .elementor-element.elementor-element-d088340 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --padding-top: 80px;
    --padding-bottom: 80px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-d088340:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-d088340>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-4aa503f {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-4aa503f .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-9ba9d00 {
    --spacer-size: 20px;
}

.elementor-190 .elementor-element.elementor-element-e9cc81b {
    --display: flex;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --gap: 5px 5px;
    --row-gap: 5px;
    --column-gap: 5px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-9cc3f85 {
    width: var(--container-widget-width, 20%);
    max-width: 20%;
    --container-widget-width: 20%;
    --container-widget-flex-grow: 0;
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-9cc3f85 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-9cc3f85 .elementor-icon {
    font-size: 120px;
}

.elementor-190 .elementor-element.elementor-element-9cc3f85 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-9cc3f85 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 400;
}

.elementor-190 .elementor-element.elementor-element-9cc3f85 .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-9cc3f85 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-b7e7cac .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-0123dce {
    width: var(--container-widget-width, 20%);
    max-width: 20%;
    --container-widget-width: 20%;
    --container-widget-flex-grow: 0;
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-0123dce .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-0123dce .elementor-icon {
    font-size: 120px;
}

.elementor-190 .elementor-element.elementor-element-0123dce .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-0123dce .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 400;
}

.elementor-190 .elementor-element.elementor-element-0123dce .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-0123dce .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-5a86bc4 .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-55bc7b9 {
    width: var(--container-widget-width, 20%);
    max-width: 20%;
    --container-widget-width: 20%;
    --container-widget-flex-grow: 0;
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-55bc7b9 .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-55bc7b9 .elementor-icon {
    font-size: 120px;
}

.elementor-190 .elementor-element.elementor-element-55bc7b9 .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-55bc7b9 .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 400;
}

.elementor-190 .elementor-element.elementor-element-55bc7b9 .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-55bc7b9 .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-26e6208 .elementor-icon-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-0815c4b {
    width: var(--container-widget-width, 20%);
    max-width: 20%;
    --container-widget-width: 20%;
    --container-widget-flex-grow: 0;
    --icon-box-icon-margin: 5px;
}

.elementor-190 .elementor-element.elementor-element-0815c4b .elementor-icon-box-wrapper {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-0815c4b .elementor-icon {
    font-size: 120px;
}

.elementor-190 .elementor-element.elementor-element-0815c4b .elementor-icon-box-title,
.elementor-190 .elementor-element.elementor-element-0815c4b .elementor-icon-box-title a {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 400;
}

.elementor-190 .elementor-element.elementor-element-0815c4b .elementor-icon-box-title {
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-0815c4b .elementor-icon-box-description {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-ffdf2a1 {
    --spacer-size: 20px;
}

.elementor-190 .elementor-element.elementor-element-19328e0 .elementor-button {
    background-color: transparent;
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
    background-image: linear-gradient(180deg, #36C213 0%, #28A00A 100%);
    padding: 20px 30px 20px 30px;
}

.elementor-190 .elementor-element.elementor-element-19328e0 .elementor-button:hover,
.elementor-190 .elementor-element.elementor-element-19328e0 .elementor-button:focus {
    background-color: #4BDD26;
}

.elementor-190 .elementor-element.elementor-element-19328e0 .elementor-button-content-wrapper {
    flex-direction: row;
}

.elementor-190 .elementor-element.elementor-element-ab96af9 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --padding-top: 80px;
    --padding-bottom: 80px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-ab96af9:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-ab96af9>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("https://www.finixsports.com/wp-content/uploads/2024/07/White_dot_BG-scaled.jpg");
    background-position: center center;
    background-size: cover;
}

.elementor-190 .elementor-element.elementor-element-e800281 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-e800281 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-4ccc361 {
    --display: grid;
    --e-con-grid-template-columns: repeat(3, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-7a4fa7b {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    --padding-top: 30px;
    --padding-bottom: 30px;
    --padding-left: 30px;
    --padding-right: 30px;
}

.elementor-190 .elementor-element.elementor-element-7a4fa7b:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-7a4fa7b>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-7fd17a9 {
    --display: flex;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --gap: 10px 10px;
    --row-gap: 10px;
    --column-gap: 10px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-661014c img {
    width: 80px;
}

.elementor-190 .elementor-element.elementor-element-3960704 {
    --display: flex;
    --gap: 5px 5px;
    --row-gap: 5px;
    --column-gap: 5px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-2dc610e .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 600;
}

.elementor-190 .elementor-element.elementor-element-75c85ec {
    --e-rating-icon-font-size: 15px;
    --e-rating-icon-marked-color: #FF931E;
    --e-rating-icon-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-e15cc97 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-12732b0 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    --padding-top: 30px;
    --padding-bottom: 30px;
    --padding-left: 30px;
    --padding-right: 30px;
}

.elementor-190 .elementor-element.elementor-element-12732b0:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-12732b0>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-766084a {
    --display: flex;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --gap: 10px 10px;
    --row-gap: 10px;
    --column-gap: 10px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-d2568cd img {
    width: 80px;
}

.elementor-190 .elementor-element.elementor-element-06d4114 {
    --display: flex;
    --gap: 5px 5px;
    --row-gap: 5px;
    --column-gap: 5px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-020917a .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 600;
}

.elementor-190 .elementor-element.elementor-element-567feca {
    --e-rating-icon-font-size: 15px;
    --e-rating-icon-marked-color: #FF931E;
    --e-rating-icon-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-f5a1ed4 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-db86b4f {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    --padding-top: 30px;
    --padding-bottom: 30px;
    --padding-left: 30px;
    --padding-right: 30px;
}

.elementor-190 .elementor-element.elementor-element-db86b4f:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-db86b4f>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-15896d6);
}

.elementor-190 .elementor-element.elementor-element-b1a2a53 {
    --display: flex;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --gap: 10px 10px;
    --row-gap: 10px;
    --column-gap: 10px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-cf89434 img {
    width: 80px;
}

.elementor-190 .elementor-element.elementor-element-a1a9e9b {
    --display: flex;
    --gap: 5px 5px;
    --row-gap: 5px;
    --column-gap: 5px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-374b44b .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 600;
}

.elementor-190 .elementor-element.elementor-element-02cafb2 {
    --e-rating-icon-font-size: 15px;
    --e-rating-icon-marked-color: #FF931E;
    --e-rating-icon-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-6543e8a {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-1435d8e {
    --e-image-carousel-slides-to-show: 3;
}

.elementor-190 .elementor-element.elementor-element-1435d8e .elementor-swiper-button.elementor-swiper-button-prev,
.elementor-190 .elementor-element.elementor-element-1435d8e .elementor-swiper-button.elementor-swiper-button-next {
    font-size: 45px;
}

.elementor-190 .elementor-element.elementor-element-1435d8e .swiper-wrapper {
    display: flex;
    align-items: center;
}

.elementor-190 .elementor-element.elementor-element-1435d8e .elementor-image-carousel-wrapper .elementor-image-carousel .swiper-slide-image {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-fe5e43d .elementor-button {
    background-color: transparent;
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
    background-image: linear-gradient(180deg, #36C213 0%, #28A00A 100%);
    padding: 20px 30px 20px 30px;
}

.elementor-190 .elementor-element.elementor-element-fe5e43d .elementor-button:hover,
.elementor-190 .elementor-element.elementor-element-fe5e43d .elementor-button:focus {
    background-color: #4BDD26;
}

.elementor-190 .elementor-element.elementor-element-fe5e43d .elementor-button-content-wrapper {
    flex-direction: row;
}

.elementor-190 .elementor-element.elementor-element-a812976 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --justify-content: space-between;
    --align-items: center;
    --overlay-opacity: 1;
    --padding-top: 80px;
    --padding-bottom: 80px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-a812976:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-a812976>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("https://www.finixsports.com/wp-content/uploads/2024/07/White_dot_BG-scaled.jpg");
    background-position: center center;
    background-size: contain;
}

.elementor-190 .elementor-element.elementor-element-a812976::before,
.elementor-190 .elementor-element.elementor-element-a812976>.elementor-background-video-container::before,
.elementor-190 .elementor-element.elementor-element-a812976>.e-con-inner>.elementor-background-video-container::before,
.elementor-190 .elementor-element.elementor-element-a812976>.elementor-background-slideshow::before,
.elementor-190 .elementor-element.elementor-element-a812976>.e-con-inner>.elementor-background-slideshow::before,
.elementor-190 .elementor-element.elementor-element-a812976>.elementor-motion-effects-container>.elementor-motion-effects-layer::before {
    background-color: transparent;
    --background-overlay: '';
    background-image: linear-gradient(180deg, var(--e-global-color-15896d6) 0%, #FFFFFF00 100%);
}

.elementor-190 .elementor-element.elementor-element-33adc8f .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-weight: 600;
    font-style: normal;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-35e67ff {
    --display: grid;
    --e-con-grid-template-columns: repeat(2, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-190 .elementor-element.elementor-element-3841f13 img {
    width: 650px;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-68a1b75 img {
    width: 650px;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-2aa5a0d .elementor-button {
    background-color: transparent;
    font-family: "Kanit", Kanit;
    font-size: 16px;
    font-weight: 500;
    background-image: linear-gradient(180deg, #36C213 0%, #28A00A 100%);
    padding: 20px 30px 20px 30px;
}

.elementor-190 .elementor-element.elementor-element-2aa5a0d .elementor-button:hover,
.elementor-190 .elementor-element.elementor-element-2aa5a0d .elementor-button:focus {
    background-color: #4BDD26;
}

.elementor-190 .elementor-element.elementor-element-2aa5a0d .elementor-button-content-wrapper {
    flex-direction: row;
}

.elementor-190 .elementor-element.elementor-element-99d025f {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-1cf76b2 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --padding-top: 80px;
    --padding-bottom: 160px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-da49104 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-da49104 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-9e98644 {
    --display: grid;
    --e-con-grid-template-columns: repeat(4, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-190 .elementor-element.elementor-element-6b9438a {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-6b9438a:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-6b9438a>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-5f5caf5 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-59562ed .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-09188a1 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-6d698e7 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-59171c7 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-59171c7:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-59171c7>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-84fbc96 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-83e19ab .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-90b31f2 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-3d62c65 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-1907776 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-1907776:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-1907776>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-a657ff0 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-43f9b7f .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-d5debe6 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-e4941ee {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-ec2f25b {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-ec2f25b:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-ec2f25b>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-142a943 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-a88308a .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-8aaab46 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-2044b60 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-0f5a0c4 {
    --spacer-size: 50px;
}

.elementor-190 .elementor-element.elementor-element-0f8469b {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-0f8469b .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-54e9f3c {
    --display: grid;
    --e-con-grid-template-columns: repeat(4, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-190 .elementor-element.elementor-element-4a17cbf {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-4a17cbf:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-4a17cbf>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-9c39650 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-d6d17c0 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-dfae292 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-7d8b10e {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-0f642d5 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-0f642d5:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-0f642d5>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-4d87b47 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-6d556a5 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-8d04555 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-5e688f7 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-99b4d89 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-99b4d89:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-99b4d89>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-d8af0c3 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-1c2442b .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-16371ba {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-398c3a6 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-3d71242 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-3d71242:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-3d71242>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-1017402 img {
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-0a847a4 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-text);
}

.elementor-190 .elementor-element.elementor-element-fab467c {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-76ffdb0 {
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-cd26162 {
    --display: flex;
    --gap: 0px 0px;
    --row-gap: 0px;
    --column-gap: 0px;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-f891010 {
    --display: flex;
    --flex-direction: column;
    --container-widget-width: 100%;
    --container-widget-height: initial;
    --container-widget-flex-grow: 0;
    --container-widget-align-self: initial;
    --flex-wrap-mobile: wrap;
    --padding-top: 80px;
    --padding-bottom: 160px;
    --padding-left: 10px;
    --padding-right: 10px;
}

.elementor-190 .elementor-element.elementor-element-2d91b24 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-2d91b24 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-7cb83fd {
    --display: grid;
    --e-con-grid-template-columns: repeat(3, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-190 .elementor-element.elementor-element-3026a84 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-3026a84:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-3026a84>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-ca203b0 img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-170ab5d {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-170ab5d .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-e2bcb67 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-132ddd8 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-132ddd8:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-132ddd8>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-43e0883 img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-b7381fc {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-b7381fc .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-6a880f3 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-3df5cc3 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-3df5cc3:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-3df5cc3>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-044010e img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-c75837f {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-c75837f .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-f9ae0a7 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-0a1dd04 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-0a1dd04:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-0a1dd04>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-e7951a7 img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-e20e061 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-e20e061 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-177c0c2 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-643807e {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-643807e:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-643807e>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-e7e2760 img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-9a32303 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-9a32303 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-dd693b4 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-6d058dc {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-6d058dc:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-6d058dc>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-f1c99f9 img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-77f593d {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-77f593d .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-06e3db0 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-a24ace0 {
    --spacer-size: 50px;
}

.elementor-190 .elementor-element.elementor-element-d5fd6bd {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-d5fd6bd .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 36px;
    font-weight: 600;
    font-style: normal;
    line-height: 40px;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-eea2b0a {
    --display: grid;
    --e-con-grid-template-columns: repeat(4, 1fr);
    --e-con-grid-template-rows: repeat(1, 1fr);
    --grid-auto-flow: row;
}

.elementor-190 .elementor-element.elementor-element-8ad3bfa {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-8ad3bfa:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-8ad3bfa>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-e1c8bd0 img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-a6d2459 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-a6d2459 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-4c12ac1 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-5b77f36 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-5b77f36:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-5b77f36>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-3724c64 img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-f936e72 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-f936e72 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-6890bb4 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-30a9b14 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-30a9b14:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-30a9b14>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-c9dd0f2 img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-2fc755a {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-2fc755a .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-4dc21f5 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-cc1b635 {
    --display: flex;
    --border-radius: 20px 20px 20px 20px;
    --padding-top: 20px;
    --padding-bottom: 20px;
    --padding-left: 20px;
    --padding-right: 20px;
}

.elementor-190 .elementor-element.elementor-element-cc1b635:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-cc1b635>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-color: var(--e-global-color-6a05be6);
}

.elementor-190 .elementor-element.elementor-element-68e34bd img {
    height: 250px;
    object-fit: contain;
    object-position: center center;
    border-radius: 10px 10px 10px 10px;
}

.elementor-190 .elementor-element.elementor-element-bcf5863 {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-bcf5863 .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 20px;
    font-weight: 600;
    color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-7b3e2d5 {
    text-align: center;
    font-family: "Kanit", Kanit;
    font-weight: 300;
}

.elementor-190 .elementor-element.elementor-element-6ee09f8 {
    --n-tabs-heading-wrap: nowrap;
    --n-tabs-heading-overflow-x: scroll;
    --n-tabs-title-white-space: nowrap;
    --n-tabs-title-color: var(--e-global-color-text);
    --n-tabs-title-color-active: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-6ee09f8>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading>.e-n-tab-title[aria-selected='false']:not( :hover) {
    background: #00000000;
}

.elementor-190 .elementor-element.elementor-element-6ee09f8.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs[data-touch-mode='false']>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:hover {
    background: #FFFFFF00;
    border-style: solid;
    border-width: 0px 0px 2px 0px;
    border-color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-6ee09f8.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading>.e-n-tab-title[aria-selected="true"],
.elementor-190 .elementor-element.elementor-element-6ee09f8.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs[data-touch-mode='true']>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:hover {
    background: #FFFFFF00;
    border-style: solid;
    border-width: 0px 0px 2px 0px;
    border-color: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-6ee09f8.elementor-widget-n-tabs>.elementor-widget-container>.e-n-tabs>.e-n-tabs-heading>.e-n-tab-title[aria-selected="false"]:not( :hover) {
    border-style: solid;
    border-width: 0px 0px 2px 0px;
    border-color: #FFFFFF00;
}

.elementor-190 .elementor-element.elementor-element-6ee09f8 [data-touch-mode="false"] .e-n-tab-title[aria-selected="false"]:hover {
    --n-tabs-title-color-hover: var(--e-global-color-primary);
}

.elementor-190 .elementor-element.elementor-element-44101a0 {
    --display: flex;
    --min-height: 300px;
    --flex-direction: row;
    --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    --container-widget-height: 100%;
    --container-widget-flex-grow: 1;
    --container-widget-align-self: stretch;
    --flex-wrap-mobile: wrap;
    --align-items: center;
    --padding-top: 0px;
    --padding-bottom: 0px;
    --padding-left: 0px;
    --padding-right: 0px;
}

.elementor-190 .elementor-element.elementor-element-44101a0:not(.elementor-motion-effects-element-type-background),
.elementor-190 .elementor-element.elementor-element-44101a0>.elementor-motion-effects-container>.elementor-motion-effects-layer {
    background-image: url("https://www.finixsports.com/wp-content/uploads/2024/07/Banner_BG_1-scaled.jpg");
    background-position: center center;
}

.elementor-190 .elementor-element.elementor-element-d70e044>.elementor-widget-container {
    margin: -150px 0px 0px 0px;
}

.elementor-190 .elementor-element.elementor-element-d70e044.elementor-element {
    --align-self: flex-end;
}

.elementor-190 .elementor-element.elementor-element-d70e044 img {
    width: 450px;
}

.elementor-190 .elementor-element.elementor-element-92e456b.elementor-element {
    --flex-grow: 1;
    --flex-shrink: 0;
}

.elementor-190 .elementor-element.elementor-element-92e456b {
    text-align: center;
}

.elementor-190 .elementor-element.elementor-element-92e456b .elementor-heading-title {
    font-family: "Kanit", Kanit;
    font-size: 48px;
    font-weight: 600;
    font-style: normal;
    line-height: 65px;
    color: var(--e-global-color-5677475);
}

@media(max-width:1150px) {

    .elementor-190 .elementor-element.elementor-element-c2a1f8f::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-background-video-container::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-video-container::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-background-slideshow::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-slideshow::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-motion-effects-container>.elementor-motion-effects-layer::before {
        background-size: 350px auto;
    }

    .elementor-190 .elementor-element.elementor-element-95411fb {
        --flex-wrap: wrap;
    }

    .elementor-190 .elementor-element.elementor-element-12e8165 {
        text-align: center;
    }

    .elementor-190 .elementor-element.elementor-element-85ade00 {
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-1c1827c {
        --e-con-grid-template-columns: repeat(5, 1fr);
        --e-con-grid-template-rows: repeat(4, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-aee4c6c {
        --flex-wrap: wrap;
    }

    .elementor-190 .elementor-element.elementor-element-89331b8 {
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-39ea3f4 .elementor-button {
        font-size: 16px;
        padding: 18px 30px 18px 30px;
    }

    .elementor-190 .elementor-element.elementor-element-0427ebb {
        --e-con-grid-template-columns: repeat(2, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-9972716 {
        --flex-wrap: wrap;
    }

    .elementor-190 .elementor-element.elementor-element-ed95a22 {
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-0425053 .elementor-button {
        font-size: 16px;
        padding: 18px 30px 18px 30px;
    }

    .elementor-190 .elementor-element.elementor-element-21841a9 {
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-9868060 .elementor-button {
        font-size: 16px;
        padding: 18px 30px 18px 30px;
    }

    .elementor-190 .elementor-element.elementor-element-62bcccd img {
        height: 100%;
    }

    .elementor-190 .elementor-element.elementor-element-3f40cbb img {
        height: 100%;
    }

    .elementor-190 .elementor-element.elementor-element-c5f4706 {
        --flex-wrap: wrap;
    }

    .elementor-190 .elementor-element.elementor-element-917eb52 {
        --justify-content: flex-start;
        --align-items: flex-start;
        --container-widget-width: calc((1 - var(--container-widget-flex-grow)) * 100%);
    }

    .elementor-190 .elementor-element.elementor-element-113d881 {
        --justify-content: center;
    }

    .elementor-190 .elementor-element.elementor-element-746e032 .elementor-icon {
        font-size: 80px;
    }

    .elementor-190 .elementor-element.elementor-element-70ecf62 .elementor-icon {
        font-size: 80px;
    }

    .elementor-190 .elementor-element.elementor-element-2c11845 .elementor-icon {
        font-size: 80px;
    }

    .elementor-190 .elementor-element.elementor-element-b2e293d .elementor-icon {
        font-size: 80px;
    }

    .elementor-190 .elementor-element.elementor-element-a3ce007 .elementor-icon {
        font-size: 80px;
    }

    .elementor-190 .elementor-element.elementor-element-e2cd8cd .elementor-button {
        font-size: 16px;
        padding: 18px 30px 18px 30px;
    }

    .elementor-190 .elementor-element.elementor-element-e9cc81b {
        --gap: 20px 20px;
        --row-gap: 20px;
        --column-gap: 20px;
        --flex-wrap: wrap;
    }

    .elementor-190 .elementor-element.elementor-element-9cc3f85 {
        width: var(--container-widget-width, 300px);
        max-width: 300px;
        --container-widget-width: 300px;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-0123dce {
        width: var(--container-widget-width, 300px);
        max-width: 300px;
        --container-widget-width: 300px;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-55bc7b9 {
        width: var(--container-widget-width, 300px);
        max-width: 300px;
        --container-widget-width: 300px;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-0815c4b {
        width: var(--container-widget-width, 300px);
        max-width: 300px;
        --container-widget-width: 300px;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-19328e0 .elementor-button {
        font-size: 16px;
        padding: 18px 30px 18px 30px;
    }

    .elementor-190 .elementor-element.elementor-element-4ccc361 {
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-1435d8e {
        --e-image-carousel-slides-to-show: 3;
    }

    .elementor-190 .elementor-element.elementor-element-fe5e43d .elementor-button {
        font-size: 16px;
        padding: 18px 30px 18px 30px;
    }

    .elementor-190 .elementor-element.elementor-element-a812976 {
        --flex-wrap: wrap;
    }

    .elementor-190 .elementor-element.elementor-element-35e67ff {
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-2aa5a0d .elementor-button {
        font-size: 16px;
        padding: 18px 30px 18px 30px;
    }

    .elementor-190 .elementor-element.elementor-element-9e98644 {
        --e-con-grid-template-columns: repeat(2, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-54e9f3c {
        --e-con-grid-template-columns: repeat(2, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-7cb83fd {
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-eea2b0a {
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-44101a0 {
        --min-height: 150px;
    }

    .elementor-190 .elementor-element.elementor-element-d70e044 img {
        width: 213px;
    }

    .elementor-190 .elementor-element.elementor-element-92e456b .elementor-heading-title {
        font-size: 35px;
        line-height: 1.3em;
    }
}

@media(max-width:767px) {

    .elementor-190 .elementor-element.elementor-element-c2a1f8f::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-background-video-container::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-video-container::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-background-slideshow::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.e-con-inner>.elementor-background-slideshow::before,
    .elementor-190 .elementor-element.elementor-element-c2a1f8f>.elementor-motion-effects-container>.elementor-motion-effects-layer::before {
        background-size: 250px auto;
    }

    .elementor-190 .elementor-element.elementor-element-12e8165 .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-85ade00 {
        --e-con-grid-template-columns: repeat(2, 1fr);
        --e-con-grid-template-rows: repeat(2, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-ce09238 .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-1c1827c {
        --e-con-grid-template-columns: repeat(3, 1fr);
        --grid-auto-flow: row;
        --padding-top: 20px;
        --padding-bottom: 20px;
        --padding-left: 20px;
        --padding-right: 20px;
    }

    .elementor-190 .elementor-element.elementor-element-89331b8 {
        --e-con-grid-template-columns: repeat(2, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-93cc379 .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-0427ebb {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-164be15 .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-ed95a22 {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-21841a9 {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-62bcccd img {
        height: 150px;
        object-fit: cover;
    }

    .elementor-190 .elementor-element.elementor-element-3f40cbb img {
        max-width: 100%;
        object-fit: cover;
    }

    .elementor-190 .elementor-element.elementor-element-917eb52 {
        --justify-content: center;
        --padding-top: 0px;
        --padding-bottom: 0px;
        --padding-left: 0px;
        --padding-right: 0px;
    }

    .elementor-190 .elementor-element.elementor-element-113d881 {
        --gap: 20px 0px;
        --row-gap: 20px;
        --column-gap: 0px;
    }

    .elementor-190 .elementor-element.elementor-element-746e032 {
        width: var(--container-widget-width, 50%);
        max-width: 50%;
        --container-widget-width: 50%;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-70ecf62 {
        width: var(--container-widget-width, 50%);
        max-width: 50%;
        --container-widget-width: 50%;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-2c11845 {
        width: var(--container-widget-width, 50%);
        max-width: 50%;
        --container-widget-width: 50%;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-b2e293d {
        width: var(--container-widget-width, 50%);
        max-width: 50%;
        --container-widget-width: 50%;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-a3ce007 {
        width: var(--container-widget-width, 50%);
        max-width: 50%;
        --container-widget-width: 50%;
        --container-widget-flex-grow: 0;
    }

    .elementor-190 .elementor-element.elementor-element-febac74 .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-0700afc .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-4aa503f .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-e9cc81b {
        --justify-content: center;
        --gap: 20px 20px;
        --row-gap: 20px;
        --column-gap: 20px;
        --padding-top: 20px;
        --padding-bottom: 20px;
        --padding-left: 20px;
        --padding-right: 20px;
    }

    .elementor-190 .elementor-element.elementor-element-9cc3f85 .elementor-icon {
        font-size: 90px;
    }

    .elementor-190 .elementor-element.elementor-element-b7e7cac .elementor-icon i,
    .elementor-190 .elementor-element.elementor-element-b7e7cac .elementor-icon svg {
        transform: rotate(90deg);
    }

    .elementor-190 .elementor-element.elementor-element-0123dce .elementor-icon {
        font-size: 90px;
    }

    .elementor-190 .elementor-element.elementor-element-5a86bc4 .elementor-icon i,
    .elementor-190 .elementor-element.elementor-element-5a86bc4 .elementor-icon svg {
        transform: rotate(90deg);
    }

    .elementor-190 .elementor-element.elementor-element-55bc7b9 .elementor-icon {
        font-size: 90px;
    }

    .elementor-190 .elementor-element.elementor-element-26e6208 .elementor-icon i,
    .elementor-190 .elementor-element.elementor-element-26e6208 .elementor-icon svg {
        transform: rotate(90deg);
    }

    .elementor-190 .elementor-element.elementor-element-0815c4b .elementor-icon {
        font-size: 90px;
    }

    .elementor-190 .elementor-element.elementor-element-e800281 .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-4ccc361 {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-7fd17a9 {
        --flex-wrap: nowrap;
    }

    .elementor-190 .elementor-element.elementor-element-e15cc97 {
        text-align: left;
    }

    .elementor-190 .elementor-element.elementor-element-766084a {
        --flex-wrap: nowrap;
    }

    .elementor-190 .elementor-element.elementor-element-f5a1ed4 {
        text-align: left;
    }

    .elementor-190 .elementor-element.elementor-element-b1a2a53 {
        --flex-wrap: nowrap;
    }

    .elementor-190 .elementor-element.elementor-element-6543e8a {
        text-align: left;
    }

    .elementor-190 .elementor-element.elementor-element-1435d8e {
        --e-image-carousel-slides-to-show: 1;
    }

    .elementor-190 .elementor-element.elementor-element-1435d8e>.elementor-widget-container {
        margin: 0px 0px 0px 0px;
    }

    .elementor-190 .elementor-element.elementor-element-33adc8f .elementor-heading-title {
        font-size: 26px;
    }

    .elementor-190 .elementor-element.elementor-element-35e67ff {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-9e98644 {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-54e9f3c {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-7cb83fd {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-eea2b0a {
        --e-con-grid-template-columns: repeat(1, 1fr);
        --grid-auto-flow: row;
    }

    .elementor-190 .elementor-element.elementor-element-44101a0 {
        --padding-top: 80px;
        --padding-bottom: 0px;
        --padding-left: 10px;
        --padding-right: 10px;
    }

    .elementor-190 .elementor-element.elementor-element-d70e044>.elementor-widget-container {
        margin: 0px 0px 0px 0px;
    }

    .elementor-190 .elementor-element.elementor-element-d70e044.elementor-element {
        --align-self: center;
        --order: 99999
            /* order end hack */
        ;
    }

    .elementor-190 .elementor-element.elementor-element-d70e044 {
        text-align: center;
    }

    .elementor-190 .elementor-element.elementor-element-d70e044 img {
        width: 360px;
    }

    .elementor-190 .elementor-element.elementor-element-92e456b .elementor-heading-title {
        font-size: 25px;
    }
}

@media(min-width:768px) {
    .elementor-190 .elementor-element.elementor-element-85ade00 {
        --width: 80%;
    }

    .elementor-190 .elementor-element.elementor-element-113d881 {
        --width: 80%;
    }
}

@media(max-width:1150px) and (min-width:768px) {
    .elementor-190 .elementor-element.elementor-element-85ade00 {
        --width: 100%;
    }

    .elementor-190 .elementor-element.elementor-element-113d881 {
        --width: 100%;
    }
}

/* Start custom CSS for heading, class: .elementor-element-e5f07f8 */
.elementor-190 .elementor-element.elementor-element-e5f07f8 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
    font-size: 36px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-12e8165 */
.elementor-190 .elementor-element.elementor-element-12e8165 span {
    color: #FF4805;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-ce09238 */
.elementor-190 .elementor-element.elementor-element-ce09238 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon, class: .elementor-element-4a88ed5 */
.elementor-190 .elementor-element.elementor-element-4a88ed5 {
    opacity: 0;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-93cc379 */
.elementor-190 .elementor-element.elementor-element-93cc379 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-423b751 */
.elementor-190 .elementor-element.elementor-element-423b751 .color {
    color: #FF4805;
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
}

.elementor-190 .elementor-element.elementor-element-423b751 {
    white-space: pre;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-746e032 */
.elementor-190 .elementor-element.elementor-element-746e032 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-70ecf62 */
.elementor-190 .elementor-element.elementor-element-70ecf62 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-2c11845 */
.elementor-190 .elementor-element.elementor-element-2c11845 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-b2e293d */
.elementor-190 .elementor-element.elementor-element-b2e293d svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-a3ce007 */
.elementor-190 .elementor-element.elementor-element-a3ce007 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-febac74 */
.elementor-190 .elementor-element.elementor-element-febac74 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-e2cd8cd */
.elementor-190 .elementor-element.elementor-element-e2cd8cd svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0700afc */
.elementor-190 .elementor-element.elementor-element-0700afc .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-4aa503f */
.elementor-190 .elementor-element.elementor-element-4aa503f .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-9cc3f85 */
.elementor-190 .elementor-element.elementor-element-9cc3f85 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0123dce */
.elementor-190 .elementor-element.elementor-element-0123dce .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-55bc7b9 */
.elementor-190 .elementor-element.elementor-element-55bc7b9 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0815c4b */
.elementor-190 .elementor-element.elementor-element-0815c4b .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-19328e0 */
.elementor-190 .elementor-element.elementor-element-19328e0 svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e800281 */
.elementor-190 .elementor-element.elementor-element-e800281 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-fe5e43d */
.elementor-190 .elementor-element.elementor-element-fe5e43d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-2aa5a0d */
.elementor-190 .elementor-element.elementor-element-2aa5a0d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-da49104 */
.elementor-190 .elementor-element.elementor-element-da49104 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0f8469b */
.elementor-190 .elementor-element.elementor-element-0f8469b .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-2d91b24 */
.elementor-190 .elementor-element.elementor-element-2d91b24 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-d5fd6bd */
.elementor-190 .elementor-element.elementor-element-d5fd6bd .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e5f07f8 */
.elementor-190 .elementor-element.elementor-element-e5f07f8 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
    font-size: 36px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-12e8165 */
.elementor-190 .elementor-element.elementor-element-12e8165 span {
    color: #FF4805;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-ce09238 */
.elementor-190 .elementor-element.elementor-element-ce09238 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon, class: .elementor-element-4a88ed5 */
.elementor-190 .elementor-element.elementor-element-4a88ed5 {
    opacity: 0;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-93cc379 */
.elementor-190 .elementor-element.elementor-element-93cc379 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-423b751 */
.elementor-190 .elementor-element.elementor-element-423b751 .color {
    color: #FF4805;
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
}

.elementor-190 .elementor-element.elementor-element-423b751 {
    white-space: pre;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-746e032 */
.elementor-190 .elementor-element.elementor-element-746e032 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-70ecf62 */
.elementor-190 .elementor-element.elementor-element-70ecf62 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-2c11845 */
.elementor-190 .elementor-element.elementor-element-2c11845 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-b2e293d */
.elementor-190 .elementor-element.elementor-element-b2e293d svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-a3ce007 */
.elementor-190 .elementor-element.elementor-element-a3ce007 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-febac74 */
.elementor-190 .elementor-element.elementor-element-febac74 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-e2cd8cd */
.elementor-190 .elementor-element.elementor-element-e2cd8cd svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0700afc */
.elementor-190 .elementor-element.elementor-element-0700afc .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-4aa503f */
.elementor-190 .elementor-element.elementor-element-4aa503f .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-9cc3f85 */
.elementor-190 .elementor-element.elementor-element-9cc3f85 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0123dce */
.elementor-190 .elementor-element.elementor-element-0123dce .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-55bc7b9 */
.elementor-190 .elementor-element.elementor-element-55bc7b9 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0815c4b */
.elementor-190 .elementor-element.elementor-element-0815c4b .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-19328e0 */
.elementor-190 .elementor-element.elementor-element-19328e0 svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e800281 */
.elementor-190 .elementor-element.elementor-element-e800281 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-fe5e43d */
.elementor-190 .elementor-element.elementor-element-fe5e43d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-2aa5a0d */
.elementor-190 .elementor-element.elementor-element-2aa5a0d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-da49104 */
.elementor-190 .elementor-element.elementor-element-da49104 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0f8469b */
.elementor-190 .elementor-element.elementor-element-0f8469b .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-2d91b24 */
.elementor-190 .elementor-element.elementor-element-2d91b24 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-d5fd6bd */
.elementor-190 .elementor-element.elementor-element-d5fd6bd .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e5f07f8 */
.elementor-190 .elementor-element.elementor-element-e5f07f8 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
    font-size: 36px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-12e8165 */
.elementor-190 .elementor-element.elementor-element-12e8165 span {
    color: #FF4805;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-ce09238 */
.elementor-190 .elementor-element.elementor-element-ce09238 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon, class: .elementor-element-4a88ed5 */
.elementor-190 .elementor-element.elementor-element-4a88ed5 {
    opacity: 0;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-93cc379 */
.elementor-190 .elementor-element.elementor-element-93cc379 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-423b751 */
.elementor-190 .elementor-element.elementor-element-423b751 .color {
    color: #FF4805;
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
}

.elementor-190 .elementor-element.elementor-element-423b751 {
    white-space: pre;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-746e032 */
.elementor-190 .elementor-element.elementor-element-746e032 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-70ecf62 */
.elementor-190 .elementor-element.elementor-element-70ecf62 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-2c11845 */
.elementor-190 .elementor-element.elementor-element-2c11845 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-b2e293d */
.elementor-190 .elementor-element.elementor-element-b2e293d svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-a3ce007 */
.elementor-190 .elementor-element.elementor-element-a3ce007 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-febac74 */
.elementor-190 .elementor-element.elementor-element-febac74 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-e2cd8cd */
.elementor-190 .elementor-element.elementor-element-e2cd8cd svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0700afc */
.elementor-190 .elementor-element.elementor-element-0700afc .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-4aa503f */
.elementor-190 .elementor-element.elementor-element-4aa503f .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-9cc3f85 */
.elementor-190 .elementor-element.elementor-element-9cc3f85 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0123dce */
.elementor-190 .elementor-element.elementor-element-0123dce .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-55bc7b9 */
.elementor-190 .elementor-element.elementor-element-55bc7b9 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0815c4b */
.elementor-190 .elementor-element.elementor-element-0815c4b .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-19328e0 */
.elementor-190 .elementor-element.elementor-element-19328e0 svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e800281 */
.elementor-190 .elementor-element.elementor-element-e800281 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-fe5e43d */
.elementor-190 .elementor-element.elementor-element-fe5e43d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-2aa5a0d */
.elementor-190 .elementor-element.elementor-element-2aa5a0d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-da49104 */
.elementor-190 .elementor-element.elementor-element-da49104 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0f8469b */
.elementor-190 .elementor-element.elementor-element-0f8469b .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-2d91b24 */
.elementor-190 .elementor-element.elementor-element-2d91b24 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-d5fd6bd */
.elementor-190 .elementor-element.elementor-element-d5fd6bd .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e5f07f8 */
.elementor-190 .elementor-element.elementor-element-e5f07f8 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
    font-size: 36px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-12e8165 */
.elementor-190 .elementor-element.elementor-element-12e8165 span {
    color: #FF4805;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-ce09238 */
.elementor-190 .elementor-element.elementor-element-ce09238 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon, class: .elementor-element-4a88ed5 */
.elementor-190 .elementor-element.elementor-element-4a88ed5 {
    opacity: 0;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-93cc379 */
.elementor-190 .elementor-element.elementor-element-93cc379 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-423b751 */
.elementor-190 .elementor-element.elementor-element-423b751 .color {
    color: #FF4805;
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
}

.elementor-190 .elementor-element.elementor-element-423b751 {
    white-space: pre;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-746e032 */
.elementor-190 .elementor-element.elementor-element-746e032 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-70ecf62 */
.elementor-190 .elementor-element.elementor-element-70ecf62 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-2c11845 */
.elementor-190 .elementor-element.elementor-element-2c11845 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-b2e293d */
.elementor-190 .elementor-element.elementor-element-b2e293d svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-a3ce007 */
.elementor-190 .elementor-element.elementor-element-a3ce007 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-febac74 */
.elementor-190 .elementor-element.elementor-element-febac74 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-e2cd8cd */
.elementor-190 .elementor-element.elementor-element-e2cd8cd svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0700afc */
.elementor-190 .elementor-element.elementor-element-0700afc .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-4aa503f */
.elementor-190 .elementor-element.elementor-element-4aa503f .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-9cc3f85 */
.elementor-190 .elementor-element.elementor-element-9cc3f85 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0123dce */
.elementor-190 .elementor-element.elementor-element-0123dce .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-55bc7b9 */
.elementor-190 .elementor-element.elementor-element-55bc7b9 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0815c4b */
.elementor-190 .elementor-element.elementor-element-0815c4b .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-19328e0 */
.elementor-190 .elementor-element.elementor-element-19328e0 svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e800281 */
.elementor-190 .elementor-element.elementor-element-e800281 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-fe5e43d */
.elementor-190 .elementor-element.elementor-element-fe5e43d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-2aa5a0d */
.elementor-190 .elementor-element.elementor-element-2aa5a0d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-da49104 */
.elementor-190 .elementor-element.elementor-element-da49104 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0f8469b */
.elementor-190 .elementor-element.elementor-element-0f8469b .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-2d91b24 */
.elementor-190 .elementor-element.elementor-element-2d91b24 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-d5fd6bd */
.elementor-190 .elementor-element.elementor-element-d5fd6bd .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e5f07f8 */
.elementor-190 .elementor-element.elementor-element-e5f07f8 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
    font-size: 36px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-12e8165 */
.elementor-190 .elementor-element.elementor-element-12e8165 span {
    color: #FF4805;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-ce09238 */
.elementor-190 .elementor-element.elementor-element-ce09238 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon, class: .elementor-element-4a88ed5 */
.elementor-190 .elementor-element.elementor-element-4a88ed5 {
    opacity: 0;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-93cc379 */
.elementor-190 .elementor-element.elementor-element-93cc379 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-423b751 */
.elementor-190 .elementor-element.elementor-element-423b751 .color {
    color: #FF4805;
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
}

.elementor-190 .elementor-element.elementor-element-423b751 {
    white-space: pre;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-746e032 */
.elementor-190 .elementor-element.elementor-element-746e032 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-70ecf62 */
.elementor-190 .elementor-element.elementor-element-70ecf62 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-2c11845 */
.elementor-190 .elementor-element.elementor-element-2c11845 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-b2e293d */
.elementor-190 .elementor-element.elementor-element-b2e293d svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-a3ce007 */
.elementor-190 .elementor-element.elementor-element-a3ce007 svg {
    box-shadow: 0px 5px 15px -5px rgba(0, 0, 0, 0.15);
    border-radius: 100px;
    padding: 0px;
    background-color: #F1EFEF;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-febac74 */
.elementor-190 .elementor-element.elementor-element-febac74 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-e2cd8cd */
.elementor-190 .elementor-element.elementor-element-e2cd8cd svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0700afc */
.elementor-190 .elementor-element.elementor-element-0700afc .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-4aa503f */
.elementor-190 .elementor-element.elementor-element-4aa503f .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-9cc3f85 */
.elementor-190 .elementor-element.elementor-element-9cc3f85 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0123dce */
.elementor-190 .elementor-element.elementor-element-0123dce .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-55bc7b9 */
.elementor-190 .elementor-element.elementor-element-55bc7b9 .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for icon-box, class: .elementor-element-0815c4b */
.elementor-190 .elementor-element.elementor-element-0815c4b .color {
    color: #FF4805;
    font-weight: 500;
    font-size: 20px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-19328e0 */
.elementor-190 .elementor-element.elementor-element-19328e0 svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-e800281 */
.elementor-190 .elementor-element.elementor-element-e800281 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-fe5e43d */
.elementor-190 .elementor-element.elementor-element-fe5e43d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for button, class: .elementor-element-2aa5a0d */
.elementor-190 .elementor-element.elementor-element-2aa5a0d svg {
    width: 40px;
    padding: 0px;
    margin: -12px 0px;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-da49104 */
.elementor-190 .elementor-element.elementor-element-da49104 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-0f8469b */
.elementor-190 .elementor-element.elementor-element-0f8469b .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-2d91b24 */
.elementor-190 .elementor-element.elementor-element-2d91b24 .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */
/* Start custom CSS for heading, class: .elementor-element-d5fd6bd */
.elementor-190 .elementor-element.elementor-element-d5fd6bd .color {
    color: #FF4805;
    font-style: normal;
    font-weight: 600;
}

/* End custom CSS */