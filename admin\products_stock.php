<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ดึงข้อมูลสินค้าและสต็อก
$products = [];
try {
    $stmt = $pdo->query("
        SELECT p.*, pc.name as category_name,
               COALESCE(p.stock_quantity, 0) as stock_quantity,
               COALESCE(p.min_stock_level, 5) as min_stock_level
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        ORDER BY p.name
    ");
    $products = $stmt->fetchAll();
} catch (Exception $e) {
    // ข้อมูลจำลอง
    $products = [
        [
            'id' => 1,
            'name' => 'เสื้อกีฬา Syntex โปโล',
            'category_name' => 'เสื้อกีฬา',
            'price' => 210.00,
            'stock_quantity' => 25,
            'min_stock_level' => 5,
            'status' => 'active'
        ],
        [
            'id' => 2,
            'name' => 'เสื้อกีฬา Dri-FIT',
            'category_name' => 'เสื้อกีฬา',
            'price' => 250.00,
            'stock_quantity' => 3,
            'min_stock_level' => 5,
            'status' => 'active'
        ],
        [
            'id' => 3,
            'name' => 'กางเกงกีฬาขาสั้น',
            'category_name' => 'กางเกงกีฬา',
            'price' => 180.00,
            'stock_quantity' => 15,
            'min_stock_level' => 10,
            'status' => 'active'
        ],
        [
            'id' => 4,
            'name' => 'ชุดทีมฟุตบอล',
            'category_name' => 'ชุดทีม',
            'price' => 450.00,
            'stock_quantity' => 0,
            'min_stock_level' => 3,
            'status' => 'active'
        ]
    ];
}

// จัดการการอัปเดตสต็อก
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_stock') {
        $product_id = (int)($_POST['product_id'] ?? 0);
        $stock_quantity = (int)($_POST['stock_quantity'] ?? 0);
        $min_stock_level = (int)($_POST['min_stock_level'] ?? 0);
        $note = trim($_POST['note'] ?? '');
        
        if ($product_id > 0) {
            try {
                $stmt = $pdo->prepare("
                    UPDATE products 
                    SET stock_quantity = ?, min_stock_level = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$stock_quantity, $min_stock_level, $product_id]);
                
                // บันทึกประวัติการเปลี่ยนแปลงสต็อก
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO stock_movements (product_id, movement_type, quantity, note, created_by, created_at)
                        VALUES (?, 'adjustment', ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$product_id, $stock_quantity, $note, $_SESSION['admin_id']]);
                } catch (Exception $e) {
                    // ถ้าไม่มีตาราง stock_movements ก็ข้าม
                }
                
                $success = 'อัปเดตสต็อกเรียบร้อยแล้ว';
            } catch (Exception $e) {
                $error = 'ไม่สามารถอัปเดตสต็อกได้: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'add_stock') {
        $product_id = (int)($_POST['product_id'] ?? 0);
        $add_quantity = (int)($_POST['add_quantity'] ?? 0);
        $note = trim($_POST['note'] ?? '');
        
        if ($product_id > 0 && $add_quantity > 0) {
            try {
                $stmt = $pdo->prepare("
                    UPDATE products 
                    SET stock_quantity = stock_quantity + ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$add_quantity, $product_id]);
                
                // บันทึกประวัติการเพิ่มสต็อก
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO stock_movements (product_id, movement_type, quantity, note, created_by, created_at)
                        VALUES (?, 'in', ?, ?, ?, NOW())
                    ");
                    $stmt->execute([$product_id, $add_quantity, $note, $_SESSION['admin_id']]);
                } catch (Exception $e) {
                    // ถ้าไม่มีตาราง stock_movements ก็ข้าม
                }
                
                $success = 'เพิ่มสต็อกเรียบร้อยแล้ว';
            } catch (Exception $e) {
                $error = 'ไม่สามารถเพิ่มสต็อกได้: ' . $e->getMessage();
            }
        }
    }
    
    // รีเฟรชหน้า
    if ($success || $error) {
        header('Location: products_stock.php?msg=' . urlencode($success ?: $error) . '&type=' . ($success ? 'success' : 'error'));
        exit();
    }
}

// แสดงข้อความ
if (isset($_GET['msg'])) {
    $message = $_GET['msg'];
    $message_type = $_GET['type'] ?? 'info';
    if ($message_type === 'success') {
        $success = $message;
    } else {
        $error = $message;
    }
}

function getStockStatus($stock, $min_level) {
    if ($stock == 0) {
        return ['class' => 'danger', 'text' => 'หมด', 'icon' => 'fas fa-times-circle'];
    } elseif ($stock <= $min_level) {
        return ['class' => 'warning', 'text' => 'ใกล้หมด', 'icon' => 'fas fa-exclamation-triangle'];
    } else {
        return ['class' => 'success', 'text' => 'ปกติ', 'icon' => 'fas fa-check-circle'];
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการสต็อกสินค้า - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .stock-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .table th {
            border-top: none;
            border-bottom: 2px solid #e0e0e0;
            font-weight: 600;
            color: #2c3e50;
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php" class="active"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">จัดการสต็อกสินค้า</h4>
                <small class="text-muted">ตรวจสอบและจัดการสต็อกสินค้าในคลัง</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="products.php">จัดการสินค้า</a></li>
                    <li class="breadcrumb-item active">จัดการสต็อก</li>
                </ol>
            </nav>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Stock Summary -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo count($products); ?></h3>
                            <p class="mb-0">สินค้าทั้งหมด</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-danger"><?php echo count(array_filter($products, fn($p) => $p['stock_quantity'] == 0)); ?></h3>
                            <p class="mb-0">สินค้าหมด</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo count(array_filter($products, fn($p) => $p['stock_quantity'] > 0 && $p['stock_quantity'] <= $p['min_stock_level'])); ?></h3>
                            <p class="mb-0">สินค้าใกล้หมด</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo count(array_filter($products, fn($p) => $p['stock_quantity'] > $p['min_stock_level'])); ?></h3>
                            <p class="mb-0">สินค้าปกติ</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stock Table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">รายการสต็อกสินค้า</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>สินค้า</th>
                                    <th>หมวดหมู่</th>
                                    <th>ราคา</th>
                                    <th>สต็อกปัจจุบัน</th>
                                    <th>สต็อกขั้นต่ำ</th>
                                    <th>สถานะ</th>
                                    <th>จัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($products as $product): ?>
                                    <?php $stock_status = getStockStatus($product['stock_quantity'], $product['min_stock_level']); ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                    </td>
                                    <td><?php echo htmlspecialchars($product['category_name'] ?? 'ไม่ระบุ'); ?></td>
                                    <td>฿<?php echo number_format($product['price'], 2); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $stock_status['class']; ?>">
                                            <?php echo $product['stock_quantity']; ?> ชิ้น
                                        </span>
                                    </td>
                                    <td><?php echo $product['min_stock_level']; ?> ชิ้น</td>
                                    <td>
                                        <span class="stock-badge bg-<?php echo $stock_status['class']; ?> text-white">
                                            <i class="<?php echo $stock_status['icon']; ?> me-1"></i>
                                            <?php echo $stock_status['text']; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" data-bs-toggle="modal" 
                                                    data-bs-target="#updateStockModal<?php echo $product['id']; ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-success" data-bs-toggle="modal" 
                                                    data-bs-target="#addStockModal<?php echo $product['id']; ?>">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Update Stock Modal -->
                                <div class="modal fade" id="updateStockModal<?php echo $product['id']; ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">แก้ไขสต็อก: <?php echo htmlspecialchars($product['name']); ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <input type="hidden" name="action" value="update_stock">
                                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                    
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">จำนวนสต็อกปัจจุบัน</label>
                                                                <input type="number" name="stock_quantity" class="form-control" 
                                                                       value="<?php echo $product['stock_quantity']; ?>" min="0" required>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">สต็อกขั้นต่ำ</label>
                                                                <input type="number" name="min_stock_level" class="form-control" 
                                                                       value="<?php echo $product['min_stock_level']; ?>" min="0" required>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">หมายเหตุ</label>
                                                        <textarea name="note" class="form-control" rows="2" placeholder="เหตุผลในการปรับสต็อก"></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                                    <button type="submit" class="btn btn-primary">บันทึก</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add Stock Modal -->
                                <div class="modal fade" id="addStockModal<?php echo $product['id']; ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">เพิ่มสต็อก: <?php echo htmlspecialchars($product['name']); ?></h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <input type="hidden" name="action" value="add_stock">
                                                    <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                                    
                                                    <div class="alert alert-info">
                                                        <strong>สต็อกปัจจุบัน:</strong> <?php echo $product['stock_quantity']; ?> ชิ้น
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">จำนวนที่ต้องการเพิ่ม</label>
                                                        <input type="number" name="add_quantity" class="form-control" min="1" required>
                                                    </div>
                                                    
                                                    <div class="mb-3">
                                                        <label class="form-label">หมายเหตุ</label>
                                                        <textarea name="note" class="form-control" rows="2" placeholder="เหตุผลในการเพิ่มสต็อก เช่น รับสินค้าใหม่"></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                                    <button type="submit" class="btn btn-success">เพิ่มสต็อก</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
