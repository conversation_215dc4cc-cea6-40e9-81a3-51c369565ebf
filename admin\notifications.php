<?php
/**
 * GT-SportDesign Notification System
 * ระบบแจ้งเตือนและการสื่อสาร
 */

session_start();
require_once '../config/database.php';

// Check admin authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$action = $_GET['action'] ?? 'dashboard';
$message = '';
$error = '';

// Handle notification actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDbConnection();
        
        if (isset($_POST['send_notification'])) {
            $type = $_POST['notification_type'];
            $title = $_POST['title'];
            $content = $_POST['content'];
            $target_audience = $_POST['target_audience'];
            
            if ($target_audience === 'all_users') {
                // Send to all active users
                $stmt = $db->query("SELECT id, email, name FROM users WHERE is_active = 1");
                $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($users as $user) {
                    createNotification($db, $user['id'], $type, $title, $content);
                    
                    // Send email if enabled
                    if (isset($_POST['send_email'])) {
                        sendNotificationEmail($user['email'], $user['name'], $title, $content);
                    }
                }
                
                $message = "✅ Notification sent to " . count($users) . " users";
                
            } elseif ($target_audience === 'specific_user') {
                $user_email = $_POST['user_email'];
                
                $stmt = $db->prepare("SELECT id, name FROM users WHERE email = ? AND is_active = 1");
                $stmt->execute([$user_email]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($user) {
                    createNotification($db, $user['id'], $type, $title, $content);
                    
                    if (isset($_POST['send_email'])) {
                        sendNotificationEmail($user_email, $user['name'], $title, $content);
                    }
                    
                    $message = "✅ Notification sent to $user_email";
                } else {
                    $error = "❌ User not found";
                }
                
            } elseif ($target_audience === 'recent_customers') {
                // Send to customers who ordered in last 30 days
                $stmt = $db->query("
                    SELECT DISTINCT customer_email as email, customer_name as name 
                    FROM payment_orders 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    AND customer_email IS NOT NULL
                ");
                $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($customers as $customer) {
                    // Get or create user ID
                    $stmt = $db->prepare("SELECT id FROM users WHERE email = ?");
                    $stmt->execute([$customer['email']]);
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($user) {
                        createNotification($db, $user['id'], $type, $title, $content);
                    }
                    
                    if (isset($_POST['send_email'])) {
                        sendNotificationEmail($customer['email'], $customer['name'], $title, $content);
                    }
                }
                
                $message = "✅ Notification sent to " . count($customers) . " recent customers";
            }
        }
        
        if (isset($_POST['mark_read'])) {
            $notification_id = $_POST['notification_id'];
            $stmt = $db->prepare("UPDATE notifications SET is_read = 1, read_at = NOW() WHERE id = ?");
            $stmt->execute([$notification_id]);
            $message = "✅ Notification marked as read";
        }
        
        if (isset($_POST['delete_notification'])) {
            $notification_id = $_POST['notification_id'];
            $stmt = $db->prepare("DELETE FROM notifications WHERE id = ?");
            $stmt->execute([$notification_id]);
            $message = "✅ Notification deleted";
        }
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Notification functions
function createNotification($db, $user_id, $type, $title, $content) {
    $stmt = $db->prepare("
        INSERT INTO notifications (user_id, type, title, content, is_read, created_at) 
        VALUES (?, ?, ?, ?, 0, NOW())
    ");
    $stmt->execute([$user_id, $type, $title, $content]);
}

function sendNotificationEmail($email, $name, $title, $content) {
    // Simple email implementation
    $subject = "GT-SportDesign: $title";
    $message = "
    <html>
    <head><title>$title</title></head>
    <body>
        <h2>สวัสดี $name</h2>
        <h3>$title</h3>
        <p>$content</p>
        <hr>
        <p><small>GT-SportDesign - ระบบออกแบบเสื้อกีฬา</small></p>
    </body>
    </html>
    ";
    
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: GT-SportDesign <<EMAIL>>" . "\r\n";
    
    // In production, use proper email service
    // mail($email, $subject, $message, $headers);
}

// Get notifications data
try {
    $db = getDbConnection();
    
    // Recent notifications
    $stmt = $db->query("
        SELECT n.*, u.name as user_name, u.email as user_email
        FROM notifications n
        LEFT JOIN users u ON n.user_id = u.id
        ORDER BY n.created_at DESC
        LIMIT 50
    ");
    $recent_notifications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Notification stats
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_notifications,
            COUNT(CASE WHEN is_read = 0 THEN 1 END) as unread_count,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_count
        FROM notifications
    ");
    $notification_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // User stats
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_users,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users
        FROM users
    ");
    $user_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $recent_notifications = [];
    $notification_stats = ['total_notifications' => 0, 'unread_count' => 0, 'today_count' => 0];
    $user_stats = ['total_users' => 0, 'active_users' => 0];
}

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification System - GT-SportDesign</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .notification-header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 20px 0; margin-bottom: 30px; }
        .notification-card { background: white; border-radius: 10px; padding: 25px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .stat-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 10px; padding: 20px; text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; }
        .stat-label { font-size: 0.9rem; opacity: 0.9; }
        .notification-item { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 10px; }
        .notification-unread { border-left: 4px solid #007bff; background-color: #f8f9ff; }
        .notification-read { border-left: 4px solid #6c757d; }
        .btn-send { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); border: none; color: white; }
        .btn-send:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(44, 62, 80, 0.3); color: white; }
    </style>
</head>
<body>
    <div class="notification-header">
        <div class="container">
            <h1><i class="fas fa-bell me-2"></i>Notification System</h1>
            <p class="mb-0">ระบบแจ้งเตือนและการสื่อสาร GT-SportDesign</p>
        </div>
    </div>

    <div class="container">
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($notification_stats['total_notifications']); ?></div>
                    <div class="stat-label">การแจ้งเตือนทั้งหมด</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($notification_stats['unread_count']); ?></div>
                    <div class="stat-label">ยังไม่อ่าน</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($notification_stats['today_count']); ?></div>
                    <div class="stat-label">วันนี้</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($user_stats['active_users']); ?></div>
                    <div class="stat-label">ผู้ใช้ที่ใช้งาน</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Send Notification -->
            <div class="col-md-6">
                <div class="notification-card">
                    <h5><i class="fas fa-paper-plane me-2"></i>ส่งการแจ้งเตือน</h5>
                    
                    <form method="post">
                        <div class="mb-3">
                            <label class="form-label">ประเภทการแจ้งเตือน</label>
                            <select class="form-control" name="notification_type" required>
                                <option value="info">ข้อมูลทั่วไป</option>
                                <option value="promotion">โปรโมชั่น</option>
                                <option value="order_update">อัปเดตคำสั่งซื้อ</option>
                                <option value="system">ระบบ</option>
                                <option value="announcement">ประกาศ</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">หัวข้อ</label>
                            <input type="text" class="form-control" name="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">เนื้อหา</label>
                            <textarea class="form-control" name="content" rows="4" required></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">กลุ่มเป้าหมาย</label>
                            <select class="form-control" name="target_audience" id="targetAudience" required>
                                <option value="all_users">ผู้ใช้ทั้งหมด</option>
                                <option value="specific_user">ผู้ใช้เฉพาะ</option>
                                <option value="recent_customers">ลูกค้าล่าสุด (30 วัน)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3" id="specificUserDiv" style="display: none;">
                            <label class="form-label">อีเมลผู้ใช้</label>
                            <input type="email" class="form-control" name="user_email">
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="send_email" id="sendEmail">
                                <label class="form-check-label" for="sendEmail">
                                    ส่งอีเมลด้วย
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" name="send_notification" class="btn btn-send">
                            <i class="fas fa-paper-plane me-2"></i>ส่งการแจ้งเตือน
                        </button>
                    </form>
                </div>
            </div>

            <!-- Quick Templates -->
            <div class="col-md-6">
                <div class="notification-card">
                    <h5><i class="fas fa-templates me-2"></i>เทมเพลตด่วน</h5>
                    
                    <div class="list-group">
                        <button class="list-group-item list-group-item-action" onclick="useTemplate('promotion', 'โปรโมชั่นพิเศษ!', 'ลดราคาสินค้าทุกชิ้น 20% สำหรับสมาชิกใหม่')">
                            <i class="fas fa-percentage text-success me-2"></i>โปรโมชั่นพิเศษ
                        </button>
                        
                        <button class="list-group-item list-group-item-action" onclick="useTemplate('order_update', 'อัปเดตคำสั่งซื้อ', 'คำสั่งซื้อของคุณได้รับการอัปเดตสถานะแล้ว')">
                            <i class="fas fa-shopping-cart text-primary me-2"></i>อัปเดตคำสั่งซื้อ
                        </button>
                        
                        <button class="list-group-item list-group-item-action" onclick="useTemplate('announcement', 'ประกาศสำคัญ', 'เรามีการอัปเดตระบบใหม่ เพื่อประสบการณ์ที่ดีขึ้น')">
                            <i class="fas fa-bullhorn text-warning me-2"></i>ประกาศสำคัญ
                        </button>
                        
                        <button class="list-group-item list-group-item-action" onclick="useTemplate('info', 'ข้อมูลใหม่', 'เรามีสินค้าใหม่เข้ามาแล้ว มาดูกันเลย!')">
                            <i class="fas fa-info-circle text-info me-2"></i>ข้อมูลทั่วไป
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Notifications -->
        <div class="notification-card">
            <h5><i class="fas fa-history me-2"></i>การแจ้งเตือนล่าสุด</h5>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ประเภท</th>
                            <th>หัวข้อ</th>
                            <th>ผู้รับ</th>
                            <th>สถานะ</th>
                            <th>วันที่</th>
                            <th>การดำเนินการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($recent_notifications as $notification): ?>
                        <tr class="<?php echo $notification['is_read'] ? '' : 'table-primary'; ?>">
                            <td>
                                <span class="badge bg-<?php 
                                    echo $notification['type'] === 'promotion' ? 'success' : 
                                        ($notification['type'] === 'order_update' ? 'primary' : 
                                        ($notification['type'] === 'system' ? 'danger' : 'info')); 
                                ?>">
                                    <?php echo $notification['type']; ?>
                                </span>
                            </td>
                            <td><?php echo htmlspecialchars($notification['title']); ?></td>
                            <td>
                                <?php echo htmlspecialchars($notification['user_name'] ?? 'Unknown'); ?>
                                <br><small class="text-muted"><?php echo htmlspecialchars($notification['user_email'] ?? ''); ?></small>
                            </td>
                            <td>
                                <?php if ($notification['is_read']): ?>
                                <span class="badge bg-success">อ่านแล้ว</span>
                                <?php else: ?>
                                <span class="badge bg-warning">ยังไม่อ่าน</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo date('d/m/Y H:i', strtotime($notification['created_at'])); ?></td>
                            <td>
                                <?php if (!$notification['is_read']): ?>
                                <form method="post" class="d-inline">
                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                    <button type="submit" name="mark_read" class="btn btn-sm btn-success">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <?php endif; ?>
                                
                                <form method="post" class="d-inline">
                                    <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                    <button type="submit" name="delete_notification" class="btn btn-sm btn-danger" 
                                            onclick="return confirm('คุณแน่ใจหรือไม่ว่าต้องการลบการแจ้งเตือนนี้?')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="notification-card">
            <h5><i class="fas fa-link me-2"></i>ลิงก์ด่วน</h5>
            <div class="row">
                <div class="col-md-3">
                    <a href="dashboard.php" class="btn btn-outline-primary w-100">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="messages.php" class="btn btn-outline-info w-100">
                        <i class="fas fa-envelope me-2"></i>ข้อความ
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="newsletter.php" class="btn btn-outline-success w-100">
                        <i class="fas fa-newspaper me-2"></i>Newsletter
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="analytics.php" class="btn btn-outline-warning w-100">
                        <i class="fas fa-chart-line me-2"></i>Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show/hide specific user email field
        document.getElementById('targetAudience').addEventListener('change', function() {
            const specificUserDiv = document.getElementById('specificUserDiv');
            if (this.value === 'specific_user') {
                specificUserDiv.style.display = 'block';
            } else {
                specificUserDiv.style.display = 'none';
            }
        });

        // Template functions
        function useTemplate(type, title, content) {
            document.querySelector('select[name="notification_type"]').value = type;
            document.querySelector('input[name="title"]').value = title;
            document.querySelector('textarea[name="content"]').value = content;
        }
    </script>
</body>
</html>
