<?php
session_start();

// ถ้าเข้าสู่ระบบแล้ว redirect
if (isset($_SESSION['user_id'])) {
    header('Location: customer_dashboard.php');
    exit;
}

require_once 'includes/config.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // ตรวจสอบข้อมูล
    if (empty($name) || empty($email) || empty($password)) {
        $error = 'กรุณากรอกข้อมูลให้ครบถ้วน';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'รูปแบบอีเมลไม่ถูกต้อง';
    } elseif (strlen($password) < 6) {
        $error = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
    } elseif ($password !== $confirm_password) {
        $error = 'รหัสผ่านไม่ตรงกัน';
    } else {
        try {
            if (file_exists('config/database.php')) {
                require_once 'config/database.php';
                
                if (isset($pdo)) {
                    // ตรวจสอบอีเมลซ้ำ
                    $stmt = $pdo->prepare("SELECT id FROM customers WHERE email = ?");
                    $stmt->execute([$email]);
                    
                    if ($stmt->fetch()) {
                        $error = 'อีเมลนี้ถูกใช้งานแล้ว';
                    } else {
                        // สร้างบัญชีใหม่
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        
                        $stmt = $pdo->prepare("INSERT INTO customers (name, email, phone, password, created_at) VALUES (?, ?, ?, ?, NOW())");
                        
                        if ($stmt->execute([$name, $email, $phone, $hashed_password])) {
                            $success = 'สมัครสมาชิกสำเร็จ! กรุณาเข้าสู่ระบบ';
                            
                            // เคลียร์ฟอร์ม
                            $name = $email = $phone = '';
                        } else {
                            $error = 'เกิดข้อผิดพลาดในการสมัครสมาชิก';
                        }
                    }
                }
            } else {
                $error = 'ระบบฐานข้อมูลยังไม่พร้อม กรุณาติดต่อผู้ดูแลระบบ';
            }
        } catch (Exception $e) {
            $error = 'เกิดข้อผิดพลาดในการสมัครสมาชิก';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>สมัครสมาชิก - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>

<body style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); min-height: 100vh;">

<div class="container">
    <div class="row justify-content-center align-items-center min-vh-100 py-5">
        <div class="col-md-6 col-lg-5">
            <div class="register-card bg-white rounded-4 shadow-lg p-4">
                <!-- Logo -->
                <div class="text-center mb-4">
                    <a href="index.php" class="text-decoration-none">
                        <i class="fas fa-tshirt fa-3x text-primary mb-2"></i>
                        <h3 class="text-primary fw-bold">GT Sport Design</h3>
                    </a>
                    <p class="text-muted">สมัครสมาชิก</p>
                </div>

                <!-- Alert Messages -->
                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Register Form -->
                <form method="POST" id="registerForm">
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อ-นามสกุล *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-user"></i>
                            </span>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">อีเมล *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-envelope"></i>
                            </span>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">เบอร์โทรศัพท์</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-phone"></i>
                            </span>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">รหัสผ่าน *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                <i class="fas fa-eye" id="passwordToggle"></i>
                            </button>
                        </div>
                        <div class="form-text">รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">ยืนยันรหัสผ่าน *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye" id="confirmPasswordToggle"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="terms" required>
                        <label class="form-check-label" for="terms">
                            ฉันยอมรับ <a href="#" class="text-decoration-none">เงื่อนไขการใช้งาน</a> และ 
                            <a href="#" class="text-decoration-none">นโยบายความเป็นส่วนตัว</a>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-user-plus me-2"></i>สมัครสมาชิก
                    </button>
                </form>

                <!-- Links -->
                <div class="text-center">
                    <p class="mb-0">
                        มีบัญชีอยู่แล้ว? 
                        <a href="login.php" class="text-decoration-none fw-bold">เข้าสู่ระบบ</a>
                    </p>
                </div>

                <!-- Back to Home -->
                <div class="text-center mt-3">
                    <a href="index.php" class="btn btn-link text-decoration-none">
                        <i class="fas fa-arrow-left me-1"></i>กลับหน้าแรก
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId === 'password' ? 'passwordToggle' : 'confirmPasswordToggle');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Password validation
document.getElementById('registerForm').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (password.length < 6) {
        e.preventDefault();
        alert('รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร');
        return;
    }
    
    if (password !== confirmPassword) {
        e.preventDefault();
        alert('รหัสผ่านไม่ตรงกัน');
        return;
    }
});

// Real-time password confirmation check
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('รหัสผ่านไม่ตรงกัน');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Auto-focus on name input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('name').focus();
});
</script>

<style>
.register-card {
    max-width: 450px;
    margin: 0 auto;
}

.input-group-text {
    background-color: var(--light-gray);
    border-color: #ddd;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    padding: 12px;
    font-weight: 600;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #d44615, #e55a2b);
    transform: translateY(-1px);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(238, 80, 27, 0.25);
}

.text-primary {
    color: var(--primary-color) !important;
}

a {
    color: var(--primary-color);
}

a:hover {
    color: #d44615;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.is-invalid {
    border-color: #dc3545;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}
</style>

</body>
</html>
