<?php
session_start();
require_once './config/database.php';

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['customer_logged_in'])) {
    header("Location: login.php");
    exit;
}

$pdo = getDbConnection();
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// ดึงข้อมูลลูกค้า
try {
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();
} catch (Exception $e) {
    die("เกิดข้อผิดพลาด: " . $e->getMessage());
}

// ดึงข้อมูลสถิติ
try {
    // จำนวนคำสั่งซื้อทั้งหมด
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE customer_id = ?");
    $stmt->execute([$customer_id]);
    $total_orders = $stmt->fetchColumn();

    // จำนวนงานออกแบบ
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM customer_designs WHERE customer_id = ?");
    $stmt->execute([$customer_id]);
    $total_designs = $stmt->fetchColumn();

    // ยอดเงินรวม
    $stmt = $pdo->prepare("SELECT SUM(total_amount) FROM orders WHERE customer_id = ? AND status != 'cancelled'");
    $stmt->execute([$customer_id]);
    $total_spent = $stmt->fetchColumn() ?: 0;

    // คำสั่งซื้อที่รอดำเนินการ
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE customer_id = ? AND status IN ('pending', 'processing')");
    $stmt->execute([$customer_id]);
    $pending_orders = $stmt->fetchColumn();

} catch (Exception $e) {
    $total_orders = $total_designs = $total_spent = $pending_orders = 0;
}

// ดึงคำสั่งซื้อล่าสุด
try {
    $stmt = $pdo->prepare("
        SELECT o.*, p.name as product_name, p.price as product_price
        FROM orders o
        LEFT JOIN products p ON o.product_id = p.id
        WHERE o.customer_id = ?
        ORDER BY o.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$customer_id]);
    $recent_orders = $stmt->fetchAll();
} catch (Exception $e) {
    $recent_orders = [];
}

// ดึงการออกแบบล่าสุด
try {
    $stmt = $pdo->prepare("
        SELECT cd.*, p.name as product_name
        FROM customer_designs cd
        LEFT JOIN products p ON cd.product_id = p.id
        WHERE cd.customer_id = ?
        ORDER BY cd.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$customer_id]);
    $recent_designs = $stmt->fetchAll();
} catch (Exception $e) {
    $recent_designs = [];
}

$welcome = $_GET['welcome'] ?? '';
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แดชบอร์ดลูกค้า - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: white !important;
        }

        .sidebar {
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 15px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar .nav-link {
            color: #6c757d;
            font-weight: 500;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
        }

        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            color: white;
            padding: 40px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .welcome-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 200%;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            height: 100%;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .stats-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stats-label {
            color: #6c757d;
            font-weight: 500;
        }

        .icon-orders {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .icon-designs {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .icon-money {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .icon-pending {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .content-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 15px;
            color: #11be97;
        }

        .order-item {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .order-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .btn-primary {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(17, 190, 151, 0.3);
        }

        .btn-outline-primary {
            color: #11be97;
            border-color: #11be97;
            border-radius: 10px;
            padding: 8px 20px;
            font-weight: 500;
        }

        .btn-outline-primary:hover {
            background: #11be97;
            border-color: #11be97;
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-bottom: 30px;
            }

            .main-content {
                padding: 15px;
            }

            .welcome-card {
                padding: 25px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= htmlspecialchars($customer_name) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="customer_profile.php">
                                <i class="fas fa-user-edit me-2"></i>แก้ไขโปรไฟล์
                            </a></li>
                            <li><a class="dropdown-item" href="customer_settings.php">
                                <i class="fas fa-cog me-2"></i>ตั้งค่า
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3">
                <div class="sidebar">
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="customer_dashboard.php">
                            <i class="fas fa-tachometer-alt me-3"></i>แดชบอร์ด
                        </a>
                        <a class="nav-link" href="customer_orders.php">
                            <i class="fas fa-shopping-cart me-3"></i>คำสั่งซื้อของฉัน
                        </a>
                        <a class="nav-link" href="customer_designs.php">
                            <i class="fas fa-palette me-3"></i>การออกแบบของฉัน
                        </a>
                        <a class="nav-link" href="customer_profile.php">
                            <i class="fas fa-user-edit me-3"></i>ข้อมูลส่วนตัว
                        </a>
                        <a class="nav-link" href="customer_settings.php">
                            <i class="fas fa-cog me-3"></i>ตั้งค่า
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="main-content">
                    <!-- Welcome Message -->
                    <?php if ($welcome): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            ยินดีต้อนรับ! สมัครสมาชิกเรียบร้อยแล้ว
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Welcome Card -->
                    <div class="welcome-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="mb-3">สวัสดี, <?= htmlspecialchars($customer_name) ?>!</h2>
                                <p class="mb-4">ยินดีต้อนรับสู่แดชบอร์ดลูกค้า GT Sport Design คุณสามารถจัดการคำสั่งซื้อ ติดตามการออกแบบ และดูประวัติการใช้งานได้ที่นี่</p>
                                <a href="design.php" class="btn btn-light">
                                    <i class="fas fa-plus me-2"></i>เริ่มออกแบบใหม่
                                </a>
                            </div>
                            <div class="col-md-4 text-end">
                                <i class="fas fa-tshirt" style="font-size: 6rem; opacity: 0.3;"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon icon-orders">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="stats-number"><?= number_format($total_orders) ?></div>
                                <div class="stats-label">คำสั่งซื้อทั้งหมด</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon icon-designs">
                                    <i class="fas fa-palette"></i>
                                </div>
                                <div class="stats-number"><?= number_format($total_designs) ?></div>
                                <div class="stats-label">งานออกแบบ</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon icon-money">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stats-number">฿<?= number_format($total_spent) ?></div>
                                <div class="stats-label">ยอดเงินรวม</div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="stats-card">
                                <div class="stats-icon icon-pending">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stats-number"><?= number_format($pending_orders) ?></div>
                                <div class="stats-label">รอดำเนินการ</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Recent Orders -->
                        <div class="col-lg-6">
                            <div class="content-card">
                                <h3 class="section-title">
                                    <i class="fas fa-shopping-cart"></i>คำสั่งซื้อล่าสุด
                                </h3>
                                <?php if ($recent_orders): ?>
                                    <?php foreach ($recent_orders as $order): ?>
                                        <div class="order-item">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div>
                                                    <h6 class="mb-1">
                                                        #<?= htmlspecialchars($order['order_number'] ?? 'ORD-' . $order['id']) ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted">
                                                        <?= htmlspecialchars($order['product_name'] ?? 'สินค้าที่ลบแล้ว') ?>
                                                    </p>
                                                    <small class="text-muted">
                                                        <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?>
                                                    </small>
                                                </div>
                                                <div class="text-end">
                                                    <div class="mb-2">
                                                        <?php
                                                        $status_class = 'status-pending';
                                                        $status_text = 'รอดำเนินการ';
                                                        switch ($order['status']) {
                                                            case 'processing':
                                                                $status_class = 'status-processing';
                                                                $status_text = 'กำลังผลิต';
                                                                break;
                                                            case 'completed':
                                                                $status_class = 'status-completed';
                                                                $status_text = 'เสร็จสิ้น';
                                                                break;
                                                            case 'cancelled':
                                                                $status_class = 'status-cancelled';
                                                                $status_text = 'ยกเลิก';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="status-badge <?= $status_class ?>">
                                                            <?= $status_text ?>
                                                        </span>
                                                    </div>
                                                    <div class="fw-bold">
                                                        ฿<?= number_format($order['total_amount']) ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="text-center mt-3">
                                        <a href="customer_orders.php" class="btn btn-outline-primary">
                                            ดูทั้งหมด
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-shopping-cart" style="font-size: 3rem; color: #dee2e6;"></i>
                                        <p class="text-muted mt-3">ยังไม่มีคำสั่งซื้อ</p>
                                        <a href="index.php" class="btn btn-primary">
                                            เริ่มสั่งซื้อ
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Recent Designs -->
                        <div class="col-lg-6">
                            <div class="content-card">
                                <h3 class="section-title">
                                    <i class="fas fa-palette"></i>การออกแบบล่าสุด
                                </h3>
                                <?php if ($recent_designs): ?>
                                    <?php foreach ($recent_designs as $design): ?>
                                        <div class="order-item">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div>
                                                    <h6 class="mb-1">
                                                        <?= htmlspecialchars($design['design_name'] ?? 'การออกแบบ #' . $design['id']) ?>
                                                    </h6>
                                                    <p class="mb-1 text-muted">
                                                        <?= htmlspecialchars($design['product_name'] ?? 'สินค้าที่ลบแล้ว') ?>
                                                    </p>
                                                    <small class="text-muted">
                                                        <?= date('d/m/Y H:i', strtotime($design['created_at'])) ?>
                                                    </small>
                                                </div>
                                                <div class="text-end">
                                                    <?php
                                                    $status_class = 'status-pending';
                                                    $status_text = 'รอตรวจสอบ';
                                                    switch ($design['status']) {
                                                        case 'approved':
                                                            $status_class = 'status-completed';
                                                            $status_text = 'อนุมัติ';
                                                            break;
                                                        case 'rejected':
                                                            $status_class = 'status-cancelled';
                                                            $status_text = 'ปฏิเสธ';
                                                            break;
                                                        case 'in_progress':
                                                            $status_class = 'status-processing';
                                                            $status_text = 'กำลังทำ';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="status-badge <?= $status_class ?>">
                                                        <?= $status_text ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    <div class="text-center mt-3">
                                        <a href="customer_designs.php" class="btn btn-outline-primary">
                                            ดูทั้งหมด
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="fas fa-palette" style="font-size: 3rem; color: #dee2e6;"></i>
                                        <p class="text-muted mt-3">ยังไม่มีการออกแบบ</p>
                                        <a href="design.php" class="btn btn-primary">
                                            เริ่มออกแบบ
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
