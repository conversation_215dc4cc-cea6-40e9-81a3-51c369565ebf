<?php
session_start();
require_once '../../includes/config.php';
require_once '../../includes/db.php';


$admin_page_title = "ข้อความ";
include '../includes/header.php';

// Get message ID from URL
$message_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Validate message ID
if ($message_id <= 0) {
    $_SESSION['message'] = 'ID ข้อความไม่ต้อง';
    $_SESSION['message_type'] = 'error';
    header('Location: index.php');
    exit;
}

// In a real application, fetch message from database
// $sql = "SELECT * FROM contacts_messages WHERE id = ?";
// $params = [$message_id];
// $message = db_query_one($sql, $params);

// if (!$message) {
//     $_SESSION['message'] = 'ไม่พบข้อความ';
//     $_SESSION['message_type'] = 'error';
//     header('Location: index.php');
//     exit;
// }

// If message is unread, mark as read
// if ($message['status'] == 'unread') {
//     $sql = "UPDATE contacts_messages SET status = 'read', updated_at = NOW() WHERE id = ?";
//     $params = [$message_id];
//     db_execute($sql, $params);
// }

// Simulated message data
$message = [
    'id' => $message_id,
    'sender_name' => 'ค้า อย่าง',
    'sender_email' => '<EMAIL>',
    'sender_phone' => '************',
    'subject' => 'สอบถามเกี่ยวกับการผลิตเสื้อ',
    'message' => 'สสส/ค่ะ ผม/ค่ะสนใจอยากผลิตเสื้อประมาณ 50 ตัว ต้องการให้โลโก้ทด้านหน้า และชื่อด้านหลัง อยากทราบว่าราคาประมาณเท่าไหร่ และใช้เวลานานแค่ไหน/คะ',
    'created_at' => '2023-05-15 10:30:45',
    'status' => 'read',
    'ip_address' => '*************',
    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
];

// Get message replies (in a real application)
// $sql = "SELECT mr.*, u.username FROM message_replies mr 
//         LEFT JOIN users u ON mr.admin_id = u.id 
//         WHERE mr.message_id = ? 
//         ORDER BY mr.created_at ASC";
// $params = [$message_id];
// $replies = db_query($sql, $params);

// Simulated replies
$replies = [
    [
        'id' => 1,
        'message_id' => $message_id,
        'reply_text' => 'สสส/ค่ะ ขอบคุณที่สนใจ ของเราเสื้อ 50 ตัว พร้อมโลโก้และชื่อด้านหลัง ราคาประมาณ 350-450 บาท ขึ้นอยู่กับเนื้อผ้าและความซับซ้อนของงาน ใช้เวลาประมาณ 10-14 ทำการ ค่ะ หากสนใจสามารถส่งแบบและชำระมัดจำได้ค่ะ',
        'admin_id' => 1,
        'username' => 'admin',
        'created_at' => '2023-05-15 11:15:22'
    ]
];
?>

<div class="admin-content">
    <div class="admin-content-header">
        <h2><?php echo $admin_page_title; ?></h2>
        <div class="action-buttons">
            <a href="index.php" class="btn btn-secondary">รายการ</a>
            <a href="reply.php?id=<?php echo $message_id; ?>" class="btn btn-primary">ตอบ</a>
            <a href="process.php?action=delete&id=<?php echo $message_id; ?>" class="btn btn-danger" onclick="return confirm('แน่ใจไม่จะลบข้อความ?');">ลบข้อความ</a>
        </div>
    </div>
    
    <?php if (isset($_SESSION['message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['message_type']; ?>">
            <?php 
                echo $_SESSION['message']; 
                unset($_SESSION['message']);
                unset($_SESSION['message_type']);
            ?>
        </div>
    <?php endif; ?>

    <div class="admin-card">
        <div class="admin-card-header">
            <h3><?php echo htmlspecialchars($message['subject']); ?></h3>
            <span class="status-badge status-<?php echo $message['status']; ?>">
                <?php 
                    switch($message['status']) {
                        case 'unread': echo 'ไม่ได้อ่าน'; break;
                        case 'read': echo 'อ่านแล้ว'; break;
                        case 'replied': echo 'ตอบแล้ว'; break;
                        default: echo $message['status'];
                    }
                ?>
            </span>
        </div>
        <div class="admin-card-body">
            <div class="message-details">
                <div class="message-meta">
                    <p><strong>จาก:</strong> <?php echo htmlspecialchars($message['sender_name']); ?> (<?php echo htmlspecialchars($message['sender_email']); ?>)</p>
                    <?php if (!empty($message['sender_phone'])): ?>
                        <p><strong>เบอร์โทร:</strong> <?php echo htmlspecialchars($message['sender_phone']); ?></p>
                    <?php endif; ?>
                    <p><strong>เวลา:</strong> <?php echo $message['created_at']; ?></p>
                </div>
                <div class="message-content">
                    <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                </div>
            </div>
            
            <?php if (!empty($replies)): ?>
                <div class="message-replies">
                    <h4>การตอบ</h4>
                    <?php foreach ($replies as $reply): ?>
                        <div class="reply-item">
                            <div class="reply-meta">
                                <p>
                                    <strong>ตอบโดย:</strong> <?php echo htmlspecialchars($reply['username']); ?>
                                    <span class="reply-time"><?php echo $reply['created_at']; ?></span>
                                </p>
                            </div>
                            <div class="reply-content">
                                <?php echo nl2br(htmlspecialchars($reply['reply_text'])); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <div class="quick-reply">
                <h4>ตอบด่วน</h4>
                <form action="process.php" method="post">
                    <input type="hidden" name="action" value="reply">
                    <input type="hidden" name="message_id" value="<?php echo $message_id; ?>">
                    <input type="hidden" name="recipient_email" value="<?php echo htmlspecialchars($message['sender_email']); ?>">
                    
                    <div class="form-group">
                        <label for="reply_text">ข้อความตอบ:</label>
                        <textarea id="reply_text" name="reply_text" class="form-control" rows="5" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>เทมเพลตข้อความ:</label>
                        <div class="template-buttons">
                            <button type="button" class="btn btn-sm btn-outline-secondary template-btn" data-template="สสสค่ะ ขอบคุณที่สนใจ ของเรา">ทักทาย</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary template-btn" data-template="เสื้อ ราคาเริ่มต้น 299 บาท/ตัว ขึ้นอยู่กับจำนวนและความซับซ้อนของงาน">ราคาเสื้อ</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary template-btn" data-template="ใช้เวลาประมาณ 7-14 ทำการ แบบและชำระมัดจำ">ระยะเวลา</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary template-btn" data-template="หากมีข้อสอบถาม สามารถต่อเราได้เบอร์ 02-123-4567 ในเวลาทำการ">ข้อมูลต่อ</button>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">ส่งการตอบ</button>
                </form>
            </div>
            
            <div class="message-technical-info">
                <h4>ข้อมูลทางเทคนิค</h4>
                <p><strong>IP Address:</strong> <?php echo $message['ip_address']; ?></p>
                <p><strong>User Agent:</strong> <?php echo $message['user_agent']; ?></p>
            </div>
        </div>
    </div>
</div>

<style>
.message-details {
    margin-bottom: 30px;
}

.message-meta {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.message-content {
    background-color: #fff;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 5px;
    line-height: 1.6;
}

.message-replies {
    margin-top: 30px;
    margin-bottom: 30px;
}

.reply-item {
    background-color: #f0f7ff;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.reply-meta {
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 10px;
}

.reply-time {
    color: #777;
    font-size: 0.9em;
    margin-left: 10px;
}

.reply-content {
    line-height: 1.6;
}

.quick-reply {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 30px;
}

.template-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.message-technical-info {
    font-size: 0.9em;
    color: #777;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.status-unread {
    background-color: #f0ad4e;
}

.status-read {
    background-color: #5bc0de;
}

.status-replied {
    background-color: #5cb85c;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Template buttons functionality
    const templateButtons = document.querySelectorAll('.template-btn');
    const replyTextarea = document.getElementById('reply_text');
    
    templateButtons.forEach(button => {
        button.addEventListener('click', function() {
            const templateText = this.getAttribute('data-template');
            
            // If textarea is empty, just insert the template
            // Otherwise, add a new line and then the template
            if (replyTextarea.value === '') {
                replyTextarea.value = templateText;
            } else {
                replyTextarea.value += '\n\n' + templateText;
            }
            
            // Focus on the textarea and move cursor to the end
            replyTextarea.focus();
            replyTextarea.scrollTop = replyTextarea.scrollHeight;
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>

