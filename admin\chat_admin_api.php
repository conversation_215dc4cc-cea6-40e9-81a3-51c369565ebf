<?php
session_start();
header('Content-Type: application/json');
require_once '../config/database.php';

// ตรวจสอบการล็อก
if (!isset($_SESSION['admin_id'])) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

try {
    $pdo = getDbConnection();

    $method = $_SERVER['REQUEST_METHOD'];

    if ($method === 'POST') {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        $action = $data['action'] ?? '';
    } else {
        $action = $_GET['action'] ?? '';
    }

    switch ($action) {
        case 'get_conversation_messages':
            getConversationMessages($pdo);
            break;

        case 'mark_as_read':
            markAsRead($pdo, $data);
            break;

        case 'send_message':
            sendAdminMessage($pdo, $data);
            break;

        case 'get_conversation_info':
            getConversationInfo($pdo);
            break;

        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getConversationMessages($pdo) {
    $customer_id = (int)($_GET['customer_id'] ?? 0);

    if (!$customer_id) {
        throw new Exception('Missing customer ID');
    }

    $stmt = $pdo->prepare("
        SELECT cm.*,
               CASE
                   WHEN cm.sender_type = 'admin' THEN au.fullname
                   ELSE c.name
               END as sender_name
        FROM chat_messages cm
        LEFT JOIN admin_users au ON cm.admin_id = au.id AND cm.sender_type = 'admin'
        LEFT JOIN customers c ON cm.customer_id = c.id
        WHERE cm.customer_id = ?
        ORDER BY cm.created_at ASC
    ");
    $stmt->execute([$customer_id]);
    $messages = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'messages' => $messages
    ]);
}

function markAsRead($pdo, $data) {
    $customer_id = (int)($data['customer_id'] ?? 0);

    if (!$customer_id) {
        throw new Exception('Missing customer ID');
    }

    // ปรับข้อความของลูกค้าให้เป็นอ่านแล้ว
    $stmt = $pdo->prepare("
        UPDATE chat_messages
        SET is_read = 1, read_at = NOW()
        WHERE customer_id = ? AND sender_type = 'customer' AND is_read = 0
    ");
    $stmt->execute([$customer_id]);

    echo json_encode([
        'success' => true,
        'message' => 'Messages marked as read'
    ]);
}

function sendAdminMessage($pdo, $data) {
    $customer_id = (int)($data['customer_id'] ?? 0);
    $message = trim($data['message'] ?? '');
    $admin_id = $_SESSION['admin_id'];

    if (!$customer_id || !$message) {
        throw new Exception('Missing required data');
    }

    // บันทึกข้อความ
    $stmt = $pdo->prepare("
        INSERT INTO chat_messages (customer_id, admin_id, sender_type, message, is_read, created_at)
        VALUES (?, ?, 'admin', ?, 1, NOW())
    ");
    $stmt->execute([$customer_id, $admin_id, $message]);

    echo json_encode([
        'success' => true,
        'message' => 'Message sent successfully'
    ]);
}

function getConversationInfo($pdo) {
    $customer_id = (int)($_GET['customer_id'] ?? 0);

    if (!$customer_id) {
        throw new Exception('Missing customer ID');
    }

    $stmt = $pdo->prepare("
        SELECT c.*,
               COUNT(cm.id) as total_messages,
               COUNT(CASE WHEN cm.sender_type = 'customer' THEN 1 END) as customer_messages,
               COUNT(CASE WHEN cm.sender_type = 'admin' THEN 1 END) as admin_messages,
               MAX(cm.created_at) as last_message_time
        FROM customers c
        LEFT JOIN chat_messages cm ON c.id = cm.customer_id
        WHERE c.id = ?
        GROUP BY c.id
    ");
    $stmt->execute([$customer_id]);
    $conversation = $stmt->fetch();

    if (!$conversation) {
        throw new Exception('Customer not found');
    }

    echo json_encode([
        'success' => true,
        'conversation' => $conversation
    ]);
}
?>
