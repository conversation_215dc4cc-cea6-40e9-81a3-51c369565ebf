<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id'])) {
    http_response_code(403);
    exit('Unauthorized');
}

$customer_id = (int)($_GET['id'] ?? 0);

if (!$customer_id) {
    exit('Invalid customer ID');
}

$pdo = getDbConnection();

try {
    // ดึงข้อมูลลูกค้า
    $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();

    if (!$customer) {
        exit('Customer not found');
    }

    // ดึงคำสั่งซื้อของลูกค้า
    $stmt = $pdo->prepare("
        SELECT * FROM orders
        WHERE customer_id = ?
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$customer_id]);
    $orders = $stmt->fetchAll();

    // ดึงการออกแบบของลูกค้า
    $stmt = $pdo->prepare("
        SELECT d.*, p.name as product_name
        FROM designs d
        LEFT JOIN products p ON d.product_id = p.id
        WHERE d.customer_id = ?
        ORDER BY d.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$customer_id]);
    $designs = $stmt->fetchAll();

    // ดึงข้อความแชทของลูกค้า
    $stmt = $pdo->prepare("
        SELECT cm.*, cc.id as conversation_id
        FROM chat_messages cm
        LEFT JOIN chat_conversations cc ON cm.conversation_id = cc.id
        WHERE cc.customer_id = ?
        ORDER BY cm.created_at DESC
        LIMIT 5
    ");
    $stmt->execute([$customer_id]);
    $messages = $stmt->fetchAll();

    // สถิติลูกค้า
    $stmt = $pdo->prepare("
        SELECT
            COUNT(o.id) as total_orders,
            SUM(o.total_amount) as total_spent,
            AVG(o.total_amount) as avg_order_value,
            MAX(o.created_at) as last_order_date,
            MIN(o.created_at) as first_order_date
        FROM orders o
        WHERE o.customer_id = ?
    ");
    $stmt->execute([$customer_id]);
    $stats = $stmt->fetch();

} catch (Exception $e) {
    exit('Error loading customer details: ' . $e->getMessage());
}
?>

<div class="customer-detail">
    <!-- ข้อมูลลูกค้า -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="d-flex align-items-center mb-3">
                <div class="customer-avatar me-3" style="width: 60px; height: 60px; font-size: 24px;">
                    <?= strtoupper(substr($customer['name'], 0, 1)) ?>
                </div>
                <div>
                    <h5 class="mb-1"><?= htmlspecialchars($customer['name']) ?></h5>
                    <span class="badge bg-<?= $customer['status'] === 'active' ? 'success' : 'secondary' ?>">
                        <?= $customer['status'] === 'active' ? 'ใช้งาน' : 'ไม่ใช้งาน' ?>
                    </span>
                </div>
            </div>

            <table class="table table-sm">
                <tr>
                    <td><strong>ID:</strong></td>
                    <td><?= $customer['id'] ?></td>
                </tr>
                <tr>
                    <td><strong>อีเมล:</strong></td>
                    <td><?= htmlspecialchars($customer['email']) ?></td>
                </tr>
                <tr>
                    <td><strong>เบอร์โทร:</strong></td>
                    <td><?= htmlspecialchars($customer['phone'] ?: 'ไม่ระบุ') ?></td>
                </tr>
                <tr>
                    <td><strong>วันที่สมัคร:</strong></td>
                    <td><?= date('d/m/Y H:i:s', strtotime($customer['created_at'])) ?></td>
                </tr>
                <tr>
                    <td><strong>อัพเดตล่าสุด:</strong></td>
                    <td><?= $customer['updated_at'] ? date('d/m/Y H:i:s', strtotime($customer['updated_at'])) : 'ไม่เคย' ?></td>
                </tr>
            </table>
        </div>

        <div class="col-md-6">
            <h6 class="text-muted">สถิติการซื้อ</h6>
            <div class="row">
                <div class="col-6">
                    <div class="text-center p-3 bg-light rounded">
                        <h4 class="text-primary"><?= number_format($stats['total_orders']) ?></h4>
                        <small>คำสั่งซื้อทั้งหมด</small>
                    </div>
                </div>
                <div class="col-6">
                    <div class="text-center p-3 bg-light rounded">
                        <h4 class="text-success">฿<?= number_format($stats['total_spent']) ?></h4>
                        <small>ยอดใช้จ่ายรวม</small>
                    </div>
                </div>
                <div class="col-6 mt-3">
                    <div class="text-center p-3 bg-light rounded">
                        <h4 class="text-info">฿<?= number_format($stats['avg_order_value']) ?></h4>
                        <small>มูลค่าเฉลี่ย/คำสั่งซื้อ</small>
                    </div>
                </div>
                <div class="col-6 mt-3">
                    <div class="text-center p-3 bg-light rounded">
                        <h4 class="text-warning"><?= $stats['last_order_date'] ? date('d/m/Y', strtotime($stats['last_order_date'])) : 'ไม่เคย' ?></h4>
                        <small>คำสั่งซื้อล่าสุด</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ที่อยู่ -->
    <?php if ($customer['address']): ?>
    <div class="mb-4">
        <h6 class="text-muted">ที่อยู่</h6>
        <div class="alert alert-light">
            <?= nl2br(htmlspecialchars($customer['address'])) ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- คำสั่งซื้อล่าสุด -->
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="text-muted mb-0">คำสั่งซื้อล่าสุด</h6>
            <a href="orders.php?customer_id=<?= $customer_id ?>" class="btn btn-sm btn-outline-primary">
                ดูทั้งหมด
            </a>
        </div>

        <?php if ($orders): ?>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>เลขที่คำสั่งซื้อ</th>
                        <th>วันที่</th>
                        <th>จำนวนเงิน</th>
                        <th>สถานะ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                    <tr>
                        <td><?= htmlspecialchars($order['order_number']) ?></td>
                        <td><?= date('d/m/Y', strtotime($order['created_at'])) ?></td>
                        <td>฿<?= number_format($order['total_amount']) ?></td>
                        <td>
                            <span class="badge bg-<?= $order['status'] === 'completed' ? 'success' : 'warning' ?>">
                                <?php
                                $status_text = [
                                    'pending' => 'รอดำเนินการ',
                                    'processing' => 'กำลังดำเนินการ',
                                    'completed' => 'เสร็จสิ้น',
                                    'cancelled' => 'ยกเลิก'
                                ];
                                echo $status_text[$order['status']] ?? $order['status'];
                                ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <p class="text-muted text-center py-3">ยังไม่มีคำสั่งซื้อ</p>
        <?php endif; ?>
    </div>

    <!-- การออกแบบ -->
    <?php if ($designs): ?>
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="text-muted mb-0">การออกแบบ</h6>
            <a href="designs.php?customer_id=<?= $customer_id ?>" class="btn btn-sm btn-outline-primary">
                ดูทั้งหมด
            </a>
        </div>

        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>รหัสการออกแบบ</th>
                        <th>สินค้า</th>
                        <th>วันที่</th>
                        <th>สถานะ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($designs as $design): ?>
                    <tr>
                        <td><?= htmlspecialchars($design['design_number']) ?></td>
                        <td><?= htmlspecialchars($design['product_name'] ?: 'ไม่ระบุ') ?></td>
                        <td><?= date('d/m/Y', strtotime($design['created_at'])) ?></td>
                        <td>
                            <span class="badge bg-<?= $design['status'] === 'approved' ? 'success' : 'warning' ?>">
                                <?php
                                $design_status = [
                                    'pending' => 'รอตรวจสอบ',
                                    'approved' => 'อนุมัติแล้ว',
                                    'rejected' => 'ปฏิเสธ',
                                    'completed' => 'เสร็จสิ้น'
                                ];
                                echo $design_status[$design['status']] ?? $design['status'];
                                ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php endif; ?>

    <!-- ข้อความแชทล่าสุด -->
    <?php if ($messages): ?>
    <div class="mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="text-muted mb-0">ข้อความแชทล่าสุด</h6>
            <a href="chat.php?customer_id=<?= $customer_id ?>" class="btn btn-sm btn-outline-primary">
                เปิดแชท
            </a>
        </div>

        <div class="chat-messages" style="max-height: 200px; overflow-y: auto;">
            <?php foreach ($messages as $message): ?>
            <div class="message-item mb-2 p-2 <?= $message['sender_type'] === 'admin' ? 'bg-light' : 'bg-primary text-white' ?> rounded">
                <div class="d-flex justify-content-between">
                    <strong><?= $message['sender_type'] === 'admin' ? 'Admin' : 'ลูกค้า' ?></strong>
                    <small><?= date('d/m/Y H:i', strtotime($message['created_at'])) ?></small>
                </div>
                <p class="mb-1"><?= nl2br(htmlspecialchars($message['message'])) ?></p>
            </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <p class="text-muted text-center py-3">ยังไม่มีข้อความแชท</p>
    <?php endif; ?>

    <!-- การดำเนินการ -->
    <div class="d-flex gap-2 justify-content-end mt-4">
        <button class="btn btn-warning btn-sm" onclick="editCustomer(<?= $customer['id'] ?>)">
            <i class="fas fa-edit"></i> แก้ไขข้อมูล
        </button>
        <button class="btn btn-info btn-sm" onclick="viewOrders(<?= $customer['id'] ?>)">
            <i class="fas fa-shopping-cart"></i> ดูคำสั่งซื้อ
        </button>
        <button class="btn btn-success btn-sm" onclick="sendMessage(<?= $customer['id'] ?>)">
            <i class="fas fa-comments"></i> ส่งข้อความ
        </button>
        <button class="btn btn-primary btn-sm" onclick="sendEmail(<?= $customer['id'] ?>)">
            <i class="fas fa-envelope"></i> ส่งอีเมล
        </button>
    </div>
</div>

<style>
.customer-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

.message-item {
    font-size: 14px;
}

.chat-messages {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
}
</style>

<script>
function sendEmail(customerId) {
    if (confirm('ต้องการส่งอีเมลแจ้งข่าวสารให้ลูกค้าหรือไม่?')) {
        fetch(`send_customer_email.php?id=${customerId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('ส่งอีเมลเรียบร้อยแล้ว');
                } else {
                    alert('เกิดข้อผิดพลาด: ' + data.message);
                }
            })
            .catch(error => {
                alert('เกิดข้อผิดพลาดในการส่งอีเมล');
            });
    }
}
</script>
