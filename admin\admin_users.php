<?php
session_start();
require_once '../includes/db.php';
require_once '../includes/config.php';

// ตรวจสอบการเข้าสู่ระบบและสิทธิ์
requireSuperAdmin();

$db = getDB();
$message = '';
$messageType = '';

// จัดการการดำเนินการ
if ($_POST) {
    $action = $_POST['action'] ?? '';

    try {
        if ($action === 'add_user') {
            $username = trim($_POST['username'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $fullname = trim($_POST['fullname'] ?? '');
            $password = $_POST['password'] ?? '';
            $role = $_POST['role'] ?? 'staff';
            $phone = trim($_POST['phone'] ?? '');
            $notes = trim($_POST['notes'] ?? '');

            // ตรวจสอบข้อมูล
            if (!$username || !$email || !$fullname || !$password) {
                throw new Exception('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน');
            }

            if (!validateEmail($email)) {
                throw new Exception('รูปแบบอีเมลไม่ถูกต้อง');
            }

            if (strlen($password) < PASSWORD_MIN_LENGTH) {
                throw new Exception('รหัสผ่านต้องมีอย่างน้อย ' . PASSWORD_MIN_LENGTH . ' ตัวอักษร');
            }

            // ตรวจสอบความซ้ำ
            $stmt = $db->prepare("SELECT id FROM admin_users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            if ($stmt->fetch()) {
                throw new Exception('ชื่อผู้ใช้หรืออีเมลนี้ถูกใช้งานแล้ว');
            }

            // เพิ่มผู้ใช้ใหม่
            $hashedPassword = hashPassword($password);
            $stmt = $db->prepare("
                INSERT INTO admin_users (username, email, fullname, password, role, phone, notes, created_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$username, $email, $fullname, $hashedPassword, $role, $phone, $notes, getCurrentAdminId()]);

            $newUserId = $db->lastInsertId();
            logSystemAction('admin', getCurrentAdminId(), 'create_admin_user', "Created admin user: $username (ID: $newUserId)");

            $message = 'เพิ่มผู้ใช้เรียบร้อยแล้ว';
            $messageType = 'success';

        } elseif ($action === 'update_user') {
            $userId = (int)$_POST['user_id'];
            $fullname = trim($_POST['fullname'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $role = $_POST['role'] ?? 'staff';
            $status = $_POST['status'] ?? 'active';
            $phone = trim($_POST['phone'] ?? '');
            $notes = trim($_POST['notes'] ?? '');

            if (!$fullname || !$email) {
                throw new Exception('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน');
            }

            if (!validateEmail($email)) {
                throw new Exception('รูปแบบอีเมลไม่ถูกต้อง');
            }

            // ตรวจสอบความซ้ำ (ยกเว้นตัวเอง)
            $stmt = $db->prepare("SELECT id FROM admin_users WHERE email = ? AND id != ?");
            $stmt->execute([$email, $userId]);
            if ($stmt->fetch()) {
                throw new Exception('อีเมลนี้ถูกใช้งานแล้ว');
            }

            $stmt = $db->prepare("
                UPDATE admin_users
                SET fullname = ?, email = ?, role = ?, status = ?, phone = ?, notes = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$fullname, $email, $role, $status, $phone, $notes, $userId]);

            logSystemAction('admin', getCurrentAdminId(), 'update_admin_user', "Updated admin user ID: $userId");

            $message = 'อัพเดตข้อมูลเรียบร้อยแล้ว';
            $messageType = 'success';

        } elseif ($action === 'reset_password') {
            $userId = (int)$_POST['user_id'];
            $newPassword = $_POST['new_password'] ?? '';

            if (strlen($newPassword) < PASSWORD_MIN_LENGTH) {
                throw new Exception('รหัสผ่านต้องมีอย่างน้อย ' . PASSWORD_MIN_LENGTH . ' ตัวอักษร');
            }

            $hashedPassword = hashPassword($newPassword);
            $stmt = $db->prepare("
                UPDATE admin_users
                SET password = ?, login_attempts = 0, locked_until = NULL, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$hashedPassword, $userId]);

            logSystemAction('admin', getCurrentAdminId(), 'reset_admin_password', "Reset password for admin user ID: $userId");

            $message = 'รีเซ็ตรหัสผ่านเรียบร้อยแล้ว';
            $messageType = 'success';
        }

    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// ดึงรายการผู้ใช้
$search = $_GET['search'] ?? '';
$role_filter = $_GET['role'] ?? '';
$status_filter = $_GET['status'] ?? '';

$query = "
    SELECT au.*, creator.fullname as created_by_name
    FROM admin_users au
    LEFT JOIN admin_users creator ON au.created_by = creator.id
    WHERE 1=1
";
$params = [];

if ($search) {
    $query .= " AND (au.username LIKE ? OR au.fullname LIKE ? OR au.email LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

if ($role_filter) {
    $query .= " AND au.role = ?";
    $params[] = $role_filter;
}

if ($status_filter) {
    $query .= " AND au.status = ?";
    $params[] = $status_filter;
}

$query .= " ORDER BY au.created_at DESC";

$stmt = $db->prepare($query);
$stmt->execute($params);
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// สถิติ
$stmt = $db->prepare("SELECT role, COUNT(*) as count FROM admin_users WHERE status = 'active' GROUP BY role");
$stmt->execute();
$roleStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

$stmt = $db->prepare("SELECT status, COUNT(*) as count FROM admin_users GROUP BY status");
$stmt->execute();
$statusStats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
?>

 <?php include 'includes/header.php'; ?>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เพิ่มผู้ใช้ใหม่</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="add_user">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ชื่อผู้ใช้ *</label>
                                    <input type="text" class="form-control" name="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">อีเมล *</label>
                                    <input type="email" class="form-control" name="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">ชื่อเต็ม *</label>
                                    <input type="text" class="form-control" name="fullname" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">เบอร์โทร</label>
                                    <input type="tel" class="form-control" name="phone">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">รหัสผ่าน *</label>
                                    <input type="password" class="form-control" name="password" required minlength="6">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">บทบาท *</label>
                                    <select class="form-select" name="role" required>
                                        <option value="staff">Staff</option>
                                        <option value="admin">Admin</option>
                                        <option value="super_admin">Super Admin</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    <label class="form-label">หมายเหตุ</label>
                                    <textarea class="form-control" name="notes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">เพิ่มผู้ใช้</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">แก้ไขข้อมูลผู้ใช้</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="update_user">
                    <input type="hidden" name="user_id" id="edit_user_id">
                    <div class="modal-body" id="editUserContent">
                        <!-- Content will be loaded via JavaScript -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">บันทึกการแก้ไข</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รีเซ็ตรหัสผ่าน</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="reset_password">
                    <input type="hidden" name="user_id" id="reset_user_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">รหัสผ่านใหม่</label>
                            <input type="password" class="form-control" name="new_password" required minlength="6">
                            <div class="form-text">รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-warning">รีเซ็ตรหัสผ่าน</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const users = <?= json_encode($users) ?>;

        function editUser(userId) {
            const user = users.find(u => u.id == userId);
            if (!user) return;

            document.getElementById('edit_user_id').value = userId;

            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">ชื่อเต็ม *</label>
                            <input type="text" class="form-control" name="fullname" value="${escapeHtml(user.fullname)}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">อีเมล *</label>
                            <input type="email" class="form-control" name="email" value="${escapeHtml(user.email)}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">บทบาท *</label>
                            <select class="form-select" name="role" required>
                                <option value="staff" ${user.role === 'staff' ? 'selected' : ''}>Staff</option>
                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>Admin</option>
                                <option value="super_admin" ${user.role === 'super_admin' ? 'selected' : ''}>Super Admin</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">สถานะ *</label>
                            <select class="form-select" name="status" required>
                                <option value="active" ${user.status === 'active' ? 'selected' : ''}>ใช้งานอยู่</option>
                                <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>ไม่ใช้งาน</option>
                                <option value="suspended" ${user.status === 'suspended' ? 'selected' : ''}>ถูกระงับ</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">เบอร์โทร</label>
                            <input type="tel" class="form-control" name="phone" value="${escapeHtml(user.phone || '')}">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="mb-3">
                            <label class="form-label">หมายเหตุ</label>
                            <textarea class="form-control" name="notes" rows="3">${escapeHtml(user.notes || '')}</textarea>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('editUserContent').innerHTML = content;
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }

        function resetPassword(userId) {
            document.getElementById('reset_user_id').value = userId;
            new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
 <?php include 'includes/footer.php'; ?>