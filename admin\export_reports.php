<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการเข้าสู่ระบบ Admin
if (!isset($_SESSION['admin_logged_in'])) {
    header("Location: login.php");
    exit;
}

$pdo = getDbConnection();

// รับข้อมูลจาก GET parameters
$export_type = $_GET['type'] ?? 'excel'; // excel หรือ pdf
$report_type = $_GET['report'] ?? 'sales'; // sales, orders, products, customers
$date_from = $_GET['date_from'] ?? date('Y-m-01');
$date_to = $_GET['date_to'] ?? date('Y-m-d');

// ฟังก์ชันดึงข้อมูลยอดขาย
function getSalesReport($pdo, $date_from, $date_to) {
    $stmt = $pdo->prepare("
        SELECT
            DATE(o.created_at) as order_date,
            COUNT(o.id) as total_orders,
            SUM(o.total_amount) as total_sales,
            SUM(o.quantity) as total_quantity,
            AVG(o.total_amount) as avg_order_value
        FROM orders o
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        AND o.status NOT IN ('cancelled')
        GROUP BY DATE(o.created_at)
        ORDER BY order_date DESC
    ");
    $stmt->execute([$date_from, $date_to]);
    return $stmt->fetchAll();
}

// ฟังก์ชันดึงข้อมูลคำสั่งซื้อ
function getOrdersReport($pdo, $date_from, $date_to) {
    $stmt = $pdo->prepare("
        SELECT
            o.id,
            o.order_number,
            c.name as customer_name,
            c.email as customer_email,
            p.name as product_name,
            o.quantity,
            o.total_amount,
            o.status,
            o.created_at
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN products p ON o.product_id = p.id
        WHERE DATE(o.created_at) BETWEEN ? AND ?
        ORDER BY o.created_at DESC
    ");
    $stmt->execute([$date_from, $date_to]);
    return $stmt->fetchAll();
}

// ฟังก์ชันดึงข้อมูลสินค้า
function getProductsReport($pdo, $date_from, $date_to) {
    $stmt = $pdo->prepare("
        SELECT
            p.id,
            p.name,
            pc.name as category_name,
            p.price,
            p.status,
            COALESCE(SUM(o.quantity), 0) as total_sold,
            COALESCE(SUM(o.total_amount), 0) as total_revenue,
            COALESCE(COUNT(o.id), 0) as order_count
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        LEFT JOIN orders o ON p.id = o.product_id
            AND DATE(o.created_at) BETWEEN ? AND ?
            AND o.status NOT IN ('cancelled')
        GROUP BY p.id
        ORDER BY total_revenue DESC
    ");
    $stmt->execute([$date_from, $date_to]);
    return $stmt->fetchAll();
}

// ฟังก์ชันดึงข้อมูลลูกค้า
function getCustomersReport($pdo, $date_from, $date_to) {
    $stmt = $pdo->prepare("
        SELECT
            c.id,
            c.name,
            c.email,
            c.phone,
            c.created_at as register_date,
            COALESCE(COUNT(o.id), 0) as total_orders,
            COALESCE(SUM(o.total_amount), 0) as total_spent,
            c.last_login
        FROM customers c
        LEFT JOIN orders o ON c.id = o.customer_id
            AND DATE(o.created_at) BETWEEN ? AND ?
            AND o.status NOT IN ('cancelled')
        WHERE DATE(c.created_at) BETWEEN ? AND ?
        GROUP BY c.id
        ORDER BY total_spent DESC
    ");
    $stmt->execute([$date_from, $date_to, $date_from, $date_to]);
    return $stmt->fetchAll();
}

// ดึงข้อมูลตามประเภทรายงาน
$data = [];
$headers = [];
$filename = '';

switch ($report_type) {
    case 'sales':
        $data = getSalesReport($pdo, $date_from, $date_to);
        $headers = ['วันที่', 'จำนวนคำสั่งซื้อ', 'ยอดขาย (บาท)', 'จำนวนสินค้า', 'ค่าเฉลี่ยต่อคำสั่งซื้อ'];
        $filename = 'sales_report_' . $date_from . '_to_' . $date_to;
        break;
    case 'orders':
        $data = getOrdersReport($pdo, $date_from, $date_to);
        $headers = ['รหัส', 'เลขที่คำสั่งซื้อ', 'ลูกค้า', 'อีเมล', 'สินค้า', 'จำนวน', 'ยอดรวม', 'สถานะ', 'วันที่สั่งซื้อ'];
        $filename = 'orders_report_' . $date_from . '_to_' . $date_to;
        break;
    case 'products':
        $data = getProductsReport($pdo, $date_from, $date_to);
        $headers = ['รหัส', 'ชื่อสินค้า', 'หมวดหมู่', 'ราคา', 'สถานะ', 'ขายได้', 'รายได้', 'จำนวนคำสั่งซื้อ'];
        $filename = 'products_report_' . $date_from . '_to_' . $date_to;
        break;
    case 'customers':
        $data = getCustomersReport($pdo, $date_from, $date_to);
        $headers = ['รหัส', 'ชื่อ', 'อีเมล', 'เบอร์โทร', 'วันที่สมัคร', 'คำสั่งซื้อ', 'ยอดซื้อรวม', 'เข้าสู่ระบบล่าสุด'];
        $filename = 'customers_report_' . $date_from . '_to_' . $date_to;
        break;
}

if ($export_type === 'excel') {
    // ส่งออกเป็น Excel (CSV format)
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');

    // เพิ่ม BOM สำหรับ UTF-8
    echo "\xEF\xBB\xBF";

    $output = fopen('php://output', 'w');

    // เขียน headers
    fputcsv($output, $headers);

    // เขียนข้อมูล
    foreach ($data as $row) {
        $csv_row = [];
        switch ($report_type) {
            case 'sales':
                $csv_row = [
                    $row['order_date'],
                    $row['total_orders'],
                    number_format($row['total_sales'], 2),
                    $row['total_quantity'],
                    number_format($row['avg_order_value'], 2)
                ];
                break;
            case 'orders':
                $csv_row = [
                    $row['id'],
                    $row['order_number'] ?: 'ORD-' . $row['id'],
                    $row['customer_name'] ?: 'ไม่ระบุ',
                    $row['customer_email'] ?: 'ไม่ระบุ',
                    $row['product_name'] ?: 'ไม่ระบุ',
                    $row['quantity'],
                    number_format($row['total_amount'], 2),
                    $row['status'],
                    $row['created_at']
                ];
                break;
            case 'products':
                $csv_row = [
                    $row['id'],
                    $row['name'],
                    $row['category_name'] ?: 'ไม่ระบุ',
                    number_format($row['price'], 2),
                    $row['status'] == 1 ? 'เปิดขาย' : 'ปิดขาย',
                    $row['total_sold'],
                    number_format($row['total_revenue'], 2),
                    $row['order_count']
                ];
                break;
            case 'customers':
                $csv_row = [
                    $row['id'],
                    $row['name'],
                    $row['email'],
                    $row['phone'] ?: 'ไม่ระบุ',
                    $row['register_date'],
                    $row['total_orders'],
                    number_format($row['total_spent'], 2),
                    $row['last_login'] ?: 'ไม่เคย'
                ];
                break;
        }
        fputcsv($output, $csv_row);
    }

    fclose($output);
    exit;

} elseif ($export_type === 'pdf') {
    // ส่งออกเป็น PDF (ใช้ HTML to PDF)
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title><?= ucfirst($report_type) ?> Report</title>
        <style>
            body {
                font-family: 'DejaVu Sans', sans-serif;
                font-size: 12px;
                margin: 20px;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
            }
            .company-name {
                font-size: 24px;
                font-weight: bold;
                color: #11be97;
            }
            .report-title {
                font-size: 18px;
                margin: 10px 0;
            }
            .date-range {
                font-size: 14px;
                color: #666;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            .text-right {
                text-align: right;
            }
            .text-center {
                text-align: center;
            }
            .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 10px;
                color: #666;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="company-name">GT Sport Design</div>
            <div class="report-title">รายงาน<?=
                $report_type == 'sales' ? 'ยอดขาย' :
                ($report_type == 'orders' ? 'คำสั่งซื้อ' :
                ($report_type == 'products' ? 'สินค้า' : 'ลูกค้า'))
            ?></div>
            <div class="date-range">ช่วงเวลา: <?= $date_from ?> ถึง <?= $date_to ?></div>
        </div>

        <table>
            <thead>
                <tr>
                    <?php foreach ($headers as $header): ?>
                        <th><?= $header ?></th>
                    <?php endforeach; ?>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($data as $row): ?>
                    <tr>
                        <?php
                        switch ($report_type) {
                            case 'sales':
                                echo '<td>' . $row['order_date'] . '</td>';
                                echo '<td class="text-center">' . $row['total_orders'] . '</td>';
                                echo '<td class="text-right">' . number_format($row['total_sales'], 2) . '</td>';
                                echo '<td class="text-center">' . $row['total_quantity'] . '</td>';
                                echo '<td class="text-right">' . number_format($row['avg_order_value'], 2) . '</td>';
                                break;
                            case 'orders':
                                echo '<td>' . $row['id'] . '</td>';
                                echo '<td>' . ($row['order_number'] ?: 'ORD-' . $row['id']) . '</td>';
                                echo '<td>' . ($row['customer_name'] ?: 'ไม่ระบุ') . '</td>';
                                echo '<td>' . ($row['customer_email'] ?: 'ไม่ระบุ') . '</td>';
                                echo '<td>' . ($row['product_name'] ?: 'ไม่ระบุ') . '</td>';
                                echo '<td class="text-center">' . $row['quantity'] . '</td>';
                                echo '<td class="text-right">' . number_format($row['total_amount'], 2) . '</td>';
                                echo '<td>' . $row['status'] . '</td>';
                                echo '<td>' . date('d/m/Y H:i', strtotime($row['created_at'])) . '</td>';
                                break;
                            case 'products':
                                echo '<td>' . $row['id'] . '</td>';
                                echo '<td>' . $row['name'] . '</td>';
                                echo '<td>' . ($row['category_name'] ?: 'ไม่ระบุ') . '</td>';
                                echo '<td class="text-right">' . number_format($row['price'], 2) . '</td>';
                                echo '<td>' . ($row['status'] == 1 ? 'เปิดขาย' : 'ปิดขาย') . '</td>';
                                echo '<td class="text-center">' . $row['total_sold'] . '</td>';
                                echo '<td class="text-right">' . number_format($row['total_revenue'], 2) . '</td>';
                                echo '<td class="text-center">' . $row['order_count'] . '</td>';
                                break;
                            case 'customers':
                                echo '<td>' . $row['id'] . '</td>';
                                echo '<td>' . $row['name'] . '</td>';
                                echo '<td>' . $row['email'] . '</td>';
                                echo '<td>' . ($row['phone'] ?: 'ไม่ระบุ') . '</td>';
                                echo '<td>' . date('d/m/Y', strtotime($row['register_date'])) . '</td>';
                                echo '<td class="text-center">' . $row['total_orders'] . '</td>';
                                echo '<td class="text-right">' . number_format($row['total_spent'], 2) . '</td>';
                                echo '<td>' . ($row['last_login'] ? date('d/m/Y H:i', strtotime($row['last_login'])) : 'ไม่เคย') . '</td>';
                                break;
                        }
                        ?>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="footer">
            สร้างเมื่อ: <?= date('d/m/Y H:i:s') ?> | GT Sport Design Admin System
        </div>
    </body>
    </html>
    <?php
    $html = ob_get_clean();

    // ใช้ DomPDF หรือ TCPDF สำหรับแปลง HTML เป็น PDF
    // สำหรับการทดสอบ เราจะส่ง HTML โดยตรง
    header('Content-Type: text/html; charset=utf-8');
    header('Content-Disposition: inline; filename="' . $filename . '.html"');
    echo $html;
    exit;
}
?>
