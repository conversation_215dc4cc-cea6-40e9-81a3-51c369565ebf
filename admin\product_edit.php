<?php
require_once 'includes/header.php';
require_once '../includes/db.php';
require_once '../includes/config.php';

// ตรวจสอบ ID สินค้า
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "ไม่พบข้อมูลสินค้า";
    header("Location: products.php");
    exit;
}

$product_id = $_GET['id'];

// ดึงข้อมูลสินค้า
try {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();
    
    if (!$product) {
        $_SESSION['error'] = "ไม่พบข้อมูลสินค้า";
        header("Location: products.php");
        exit;
    }
} catch (Exception $e) {
    $_SESSION['error'] = "เกิดข้อผิดพลาด: " . $e->getMessage();
    header("Location: products.php");
    exit;
}

// ดึงข้อมูลหมวดหมู่
$categories = [];
try {
    $stmt = $db->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    $_SESSION['error'] = "ไม่สามารถดึงข้อมูลหมวดหมู่ได้";
}

// ดึงข้อมูลรูปภาพ
$images = [];
try {
    $stmt = $db->prepare("SELECT * FROM product_images WHERE product_id = ? ORDER BY sort_order");
    $stmt->execute([$product_id]);
    $images = $stmt->fetchAll();
} catch (Exception $e) {
    // ไม่มีรูปภาพ
}

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // ข้อมูลสินค้า
        $name = $_POST['name'] ?? '';
        $sku = $_POST['sku'] ?? '';
        $category_id = $_POST['category_id'] ?? null;
        $description = $_POST['description'] ?? '';
        $price = $_POST['price'] ?? 0;
        $sale_price = $_POST['sale_price'] ?? null;
        $stock_quantity = $_POST['stock_quantity'] ?? 0;
        $weight = $_POST['weight'] ?? null;
        $dimensions = $_POST['dimensions'] ?? null;
        $features = $_POST['features'] ?? null;
        $status = $_POST['status'] ?? 'active';
        
        // ตรวจสอบข้อมูล
        if (empty($name)) {
            throw new Exception("กรุณาระบุชื่อสินค้า");
        }
        
        if (!is_numeric($price) || $price < 0) {
            throw new Exception("ราคาไม่ถูกต้อง");
        }
        
        if (!empty($sale_price) && (!is_numeric($sale_price) || $sale_price < 0)) {
            throw new Exception("ราคาโปรโมชั่นไม่ถูกต้อง");
        }
        
        if (!is_numeric($stock_quantity) || $stock_quantity < 0) {
            throw new Exception("จำนวนสินค้าไม่ถูกต้อง");
        }
        
        // อัพเดทข้อมูลสินค้า
        $stmt = $db->prepare("
            UPDATE products SET
                name = ?,
                sku = ?,
                category_id = ?,
                description = ?,
                price = ?,
                sale_price = ?,
                stock_quantity = ?,
                weight = ?,
                dimensions = ?,
                features = ?,
                status = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->execute([
            $name, $sku, $category_id, $description, $price, $sale_price,
            $stock_quantity, $weight, $dimensions, $features, $status,
            $product_id
        ]);
        
        // อัพโหลดรูปภาพเพิ่มเติม
        if (!empty($_FILES['images']['name'][0])) {
            $upload_dir = '../uploads/products/';
            
            // สร้างโฟลเดอร์ถ้ายังไม่มี
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            $max_size = 5 * 1024 * 1024; // 5MB
            
            // หา sort_order ล่าสุด
            $stmt = $db->prepare("SELECT MAX(sort_order) FROM product_images WHERE product_id = ?");
            $stmt->execute([$product_id]);
            $max_sort = $stmt->fetchColumn() ?: 0;
            
            foreach ($_FILES['images']['tmp_name'] as $key => $tmp_name) {
                if ($_FILES['images']['error'][$key] === UPLOAD_ERR_OK) {
                    $file_name = $_FILES['images']['name'][$key];
                    $file_size = $_FILES['images']['size'][$key];
                    $file_type = $_FILES['images']['type'][$key];
                    $file_tmp = $_FILES['images']['tmp_name'][$key];
                    
                    // ตรวจสอบประเภทไฟล์
                    if (!in_array($file_type, $allowed_types)) {
                        continue; // ข้ามไฟล์ที่ไม่รองรับ
                    }
                    
                    // ตรวจสอบขนาดไฟล์
                    if ($file_size > $max_size) {
                        continue; // ข้ามไฟล์ที่ใหญ่เกินไป
                    }
                    
                    // สร้างชื่อไฟล์ใหม่
                    $ext = pathinfo($file_name, PATHINFO_EXTENSION);
                    $new_file_name = 'product_' . $product_id . '_' . uniqid() . '.' . $ext;
                    
                    // อัพโหลดไฟล์
                    if (move_uploaded_file($file_tmp, $upload_dir . $new_file_name)) {
                        // ปรับขนาดรูปภาพ
                        resizeImage($upload_dir . $new_file_name, 800, 800);
                        
                        // เพิ่มข้อมูลรูปภาพลงฐานข้อมูล
                        $sort_order = $max_sort + $key + 1;
                        $stmt = $db->prepare("
                            INSERT INTO product_images (product_id, image, sort_order, created_at)
                            VALUES (?, ?, ?, NOW())
                        ");
                        $stmt->execute([$product_id, $new_file_name, $sort_order]);
                        
                        // สร้าง watermark ถ้าต้องการ
                        if (isset($_POST['add_watermark']) && $_POST['add_watermark'] == 1) {
                            addWatermark($upload_dir . $new_file_name, '../assets/images/watermark.png');
                        }
                    }
                }
            }
        }
        
        $_SESSION['success'] = "อัพเดทข้อมูลสินค้าเรียบร้อยแล้ว";
        header("Location: products.php");
        exit;
        
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

// ฟังก์ชันปรับขนาดรูปภาพ
function resizeImage($file_path, $max_width, $max_height) {
    list($width, $height) = getimagesize($file_path);
    
    if ($width <= $max_width && $height <= $max_height) {
        return; // ไม่ต้องปรับขนาด
    }
    
    $ratio = min($max_width / $width, $max_height / $height);
    $new_width = $width * $ratio;
    $new_height = $height * $ratio;
    
    $ext = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
    
    switch ($ext) {
        case 'jpg':
        case 'jpeg':
            $source = imagecreatefromjpeg($file_path);
            break;
        case 'png':
            $source = imagecreatefrompng($file_path);
            break;
        case 'gif':
            $source = imagecreatefromgif($file_path);
            break;
        case 'webp':
            $source = imagecreatefromwebp($file_path);
            break;
        default:
            return; // ไม่รองรับ
    }
    
    $destination = imagecreatetruecolor($new_width, $new_height);
    
    // รักษาความโปร่งใสสำหรับ PNG
    if ($ext === 'png') {
        imagealphablending($destination, false);
        imagesavealpha($destination, true);
    }
    
    imagecopyresampled($destination, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
    
    switch ($ext) {
        case 'jpg':
        case 'jpeg':
            imagejpeg($destination, $file_path, 90);
            break;
        case 'png':
            imagepng($destination, $file_path, 9);
            break;
        case 'gif':
            imagegif($destination, $file_path);
            break;
        case 'webp':
            imagewebp($destination, $file_path, 90);
            break;
    }
    
    imagedestroy($source);
    imagedestroy($destination);
}

// ฟังก์ชันเพิ่ม watermark
function addWatermark($image_path, $watermark_path) {
    if (!file_exists($watermark_path)) {
        return;
    }
    
    $ext = strtolower(pathinfo($image_path, PATHINFO_EXTENSION));
    
    switch ($ext) {
        case 'jpg':
        case 'jpeg':
            $image = imagecreatefromjpeg($image_path);
            break;
        case 'png':
            $image = imagecreatefrompng($image_path);
            break;
        case 'gif':
            $image = imagecreatefromgif($image_path);
            break;
        case 'webp':
            $image = imagecreatefromwebp($image_path);
            break;
        default:
            return; // ไม่รองรับ
    }
    
    $watermark = imagecreatefrompng($watermark_path);
    
    $image_width = imagesx($image);
    $image_height = imagesy($image);
    $watermark_width = imagesx($watermark);
    $watermark_height = imagesy($watermark);
    
    // ตำแหน่ง watermark (มุมขวาล่าง)
    $dest_x = $image_width - $watermark_width - 10;
    $dest_y = $image_height - $watermark_height - 10;
    
    // ใส่ watermark
    imagecopy($image, $watermark, $dest_x, $dest_y, 0, 0, $watermark_width, $watermark_height);
    
    // บันทึกรูปภาพ
    switch ($ext) {
        case 'jpg':
        case 'jpeg':
            imagejpeg($image, $image_path, 90);
            break;
        case 'png':
            imagepng($image, $image_path, 9);
            break;
        case 'gif':
            imagegif($image, $image_path);
            break;
        case 'webp':
            imagewebp($image, $image_path, 90);
            break;
    }
    
    imagedestroy($image);
    imagedestroy($watermark);
}
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">แก้ไขสินค้า: <?= htmlspecialchars($product['name']) ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="products.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> กลับ
        </a>
    </div>
</div>

<?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger">
        <?= $_SESSION['error'] ?>
        <?php unset($_SESSION['error']); ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <form action="" method="post" enctype="multipart/form-data">
            <div class="row">
                <!-- ข้อมูลพื้นฐาน -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">ข้อมูลพื้นฐาน</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="name" class="form-label">ชื่อสินค้า <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="<?= htmlspecialchars($product['name']) ?>" required>
                            