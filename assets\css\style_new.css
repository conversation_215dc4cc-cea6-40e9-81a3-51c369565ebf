/* GT Sport Design - Enhanced Styles */

:root {
    --primary-color: #ee501b;
    --secondary-color: #ff6b35;
    --dark-color: #2c3e50;
    --light-gray: #f8f9fa;
    --gradient-primary: linear-gradient(135deg, #ee501b, #ff6b35);
    --gradient-dark: linear-gradient(135deg, #2c3e50, #34495e);
    --shadow-light: 0 5px 15px rgba(0,0,0,0.1);
    --shadow-medium: 0 10px 30px rgba(0,0,0,0.15);
    --shadow-heavy: 0 20px 40px rgba(0,0,0,0.2);
    --border-radius: 15px;
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', 'Prompt', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
}

.display-3 {
    font-weight: 700;
}

.lead {
    font-size: 1.1rem;
    font-weight: 400;
}

/* Buttons */
.btn {
    border-radius: 50px;
    padding: 12px 30px;
    font-weight: 500;
    text-transform: none;
    transition: var(--transition);
    border: none;
}

.btn-lg {
    padding: 15px 40px;
    font-size: 1.1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: white;
}

.btn-primary:hover {
    background: var(--gradient-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-outline-light {
    border: 2px solid white;
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Navbar */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    padding: 1rem 0;
}

.navbar-scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: var(--shadow-medium);
}

.navbar-brand {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--dark-color) !important;
    margin: 0 10px;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    transform: translateY(-1px);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

/* Sections */
section {
    position: relative;
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.highlight {
    color: var(--primary-color);
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-dark {
    background: var(--gradient-dark);
}

.shadow-light {
    box-shadow: var(--shadow-light);
}

.shadow-medium {
    box-shadow: var(--shadow-medium);
}

.shadow-heavy {
    box-shadow: var(--shadow-heavy);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-title h2 {
        font-size: 2rem;
    }
    
    .display-3 {
        font-size: 2.5rem;
    }
    
    .btn-lg {
        padding: 12px 25px;
        font-size: 1rem;
    }
    
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 576px) {
    .section-title h2 {
        font-size: 1.8rem;
    }
    
    .display-3 {
        font-size: 2rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .hero-buttons .btn {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .footer,
    .chat-widget,
    .chat-toggle,
    .floating-element {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus States */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(238, 80, 27, 0.25);
    border-color: var(--primary-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}
