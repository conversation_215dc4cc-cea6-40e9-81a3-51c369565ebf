<?php
/**
 * สร้างไฟล์ภาพ placeholder สำหรับเว็บไซต์
 */

// สร้างโฟลเดอร์ assets/images ถ้าไม่มี
if (!is_dir('assets/images')) {
    mkdir('assets/images', 0755, true);
}

if (!is_dir('assets/images/banner')) {
    mkdir('assets/images/banner', 0755, true);
}

echo "<h1>🎨 สร้างไฟล์ภาพ Placeholder</h1>";

// สร้างภาพ Hero
$hero_url = "https://via.placeholder.com/800x600/ee501b/ffffff?text=GT+Sport+Design+Hero";
$hero_content = file_get_contents($hero_url);
if ($hero_content) {
    file_put_contents('assets/images/hero-shirt.png', $hero_content);
    echo "✅ สร้างไฟล์ assets/images/hero-shirt.png<br>";
}

// สร้างภาพ Banner
$banner_url = "https://via.placeholder.com/1200x400/ee501b/ffffff?text=GT+Sport+Design+Banner";
$banner_content = file_get_contents($banner_url);
if ($banner_content) {
    file_put_contents('assets/images/banner/3.jpg', $banner_content);
    echo "✅ สร้างไฟล์ assets/images/banner/3.jpg<br>";
}

// สร้างภาพ Logo
$logo_url = "https://via.placeholder.com/200x80/ee501b/ffffff?text=GT+LOGO";
$logo_content = file_get_contents($logo_url);
if ($logo_content) {
    file_put_contents('assets/images/logo.png', $logo_content);
    echo "✅ สร้างไฟล์ assets/images/logo.png<br>";
}

// สร้างภาพ OG Image
$og_url = "https://via.placeholder.com/1200x630/ee501b/ffffff?text=GT+Sport+Design";
$og_content = file_get_contents($og_url);
if ($og_content) {
    file_put_contents('assets/images/og-image.jpg', $og_content);
    echo "✅ สร้างไฟล์ assets/images/og-image.jpg<br>";
}

// สร้างภาพสินค้า
$products = [
    'company-shirt.jpg' => 'เสื้อบริษัท',
    'activity-shirt.jpg' => 'เสื้อกิจกรรม',
    'school-sport.jpg' => 'เสื้อกีฬาโรงเรียน',
    'sport-shirt.jpg' => 'เสื้อกีฬา',
    'government.jpg' => 'เสื้อราชการ',
    'custom-design.jpg' => 'ดีไซน์พิเศษ'
];

if (!is_dir('uploads/products')) {
    mkdir('uploads/products', 0755, true);
}

foreach ($products as $filename => $title) {
    $product_url = "https://via.placeholder.com/400x300/ee501b/ffffff?text=" . urlencode($title);
    $product_content = file_get_contents($product_url);
    if ($product_content) {
        file_put_contents('uploads/products/' . $filename, $product_content);
        echo "✅ สร้างไฟล์ uploads/products/$filename<br>";
    }
}

echo "<br><h2>🎉 เสร็จสิ้น!</h2>";
echo "<p>สร้างไฟล์ภาพ placeholder ทั้งหมดเรียบร้อยแล้ว</p>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>🔗 ทดสอบเว็บไซต์:</h3>";
echo "<ul>";
echo "<li><a href='https://gtsportdesign.com/' target='_blank'>หน้าแรกใหม่</a></li>";
echo "<li><a href='https://gtsportdesign.com/products.php' target='_blank'>หน้าสินค้า</a></li>";
echo "<li><a href='https://gtsportdesign.com/shirt-design.php' target='_blank'>ออกแบบเสื้อ</a></li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📝 ขั้นตอนต่อไป:</h3>";
echo "<ol>";
echo "<li>ทดสอบหน้าเว็บใหม่</li>";
echo "<li>เปลี่ยนภาพเป็นภาพจริงของสินค้า</li>";
echo "<li>ลบไฟล์ create_images.php นี้</li>";
echo "</ol>";
echo "</div>";
?>

<style>
body {
    font-family: 'Kanit', sans-serif;
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3 {
    color: #ee501b;
}

a {
    color: #ee501b;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
