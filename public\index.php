<?php
require_once '../config/database.php';

// ดึงข้อมูลการตั้งค่าเว็บไซต์
$settings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    // fallback หากยังไม่มีข้อมูล
    $settings = [
        'site_name' => 'GT Sport Design',
        'contact_phone' => '************',
        'contact_line' => '@gtsport',
        'facebook_page' => 'https://www.facebook.com/GTSportDesign.1'
    ];
}

// ดึงสินค้าแนะนำ
$featured_products = [];
try {
    $stmt = $pdo->query("
        SELECT p.*, pc.name as category_name,
               COALESCE(pi.file_id, 0) as has_image
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.image_type = 'main'
        WHERE p.status = 'active'
        ORDER BY p.created_at DESC
        LIMIT 6
    ");
    $featured_products = $stmt->fetchAll();
} catch (Exception $e) {
    // fallback ถ้า db ยังไม่พร้อม
    error_log('Featured products query failed: ' . $e->getMessage());
}

// ดึงรีวิวลูกค้า
$reviews = [];
try {
    $stmt = $pdo->query("
        SELECT r.*, c.name as customer_name, p.name as product_name
        FROM reviews r
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN products p ON r.product_id = p.id
        WHERE r.rating >= 4
        ORDER BY r.created_at DESC
        LIMIT 6
    ");
    $reviews = $stmt->fetchAll();
} catch (Exception $e) {
    // fallback
}

// ดึงผลงานจากแกลเลอรี่
$gallery_items = [];
try {
    $stmt = $pdo->query("
        SELECT g.*, f.file_path
        FROM gallery_images g
        LEFT JOIN file_uploads f ON g.file_id = f.id
        WHERE g.is_active = 1 AND g.category = 'portfolio'
        ORDER BY g.sort_order, g.created_at DESC
        LIMIT 9
    ");
    $gallery_items = $stmt->fetchAll();
} catch (Exception $e) {
    // fallback
}
?>
<!DOCTYPE html>
<html lang="th">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($settings['site_name'] ?? 'GT Sport Design'); ?> | รับผลิตเสื้อทีม
        เสื้อกีฬาออกแบบเอง</title>
    <meta name="description"
        content="รับผลิตเสื้อกีฬา เสื้อทีม ออกแบบตามสั่ง คุณภาพสูง ราคาโรงงาน ส่งทั่วไทย - GT Sport Design">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #ee501b;
            --secondary-color: #ff6b35;
            --dark-color: #303136;
            --light-gray: #f1f0f0;
        }

        body {
            font-family: 'Kanit', sans-serif;
            line-height: 1.6;
        }

        /* Header Styles */
        .top-header {
            background: var(--dark-color);
            color: white;
            padding: 8px 0;
            font-size: 14px;
        }

        .top-header a {
            color: white;
            text-decoration: none;
        }

        .main-navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
        }

        .navbar-brand {
            color: var(--primary-color) !important;
            font-weight: 700;
            font-size: 2rem;
        }

        .navbar-nav .nav-link {
            color: var(--dark-color) !important;
            font-weight: 500;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .contact-icons {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .contact-icons a {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: transform 0.3s ease;
        }

        .contact-icons a:hover {
            transform: scale(1.1);
        }

        .contact-icons .phone {
            background: #25d366;
        }

        .contact-icons .line {
            background: #00c300;
        }

        .contact-icons .facebook {
            background: #1877f2;
        }

        /* Hero Section - คล้าย Finix Sports */
        .hero-section {
            /* background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%); */
            color: white;
            min-height: 600px;
            position: relative;
            overflow: hidden;
        }

        /* .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: url('https://ext.same-assets.com/4182383019/921381138.jpeg') no-repeat center;
            background-size: cover;
            opacity: 0.3;
        } */

        .hero-content {
            position: relative;
            z-index: 2;
            padding: 100px 0;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 30px 0;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .feature-list li i {
            margin-right: 15px;
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-hero {
            background: white;
            color: var(--primary-color);
            border: none;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-hero:hover {
            background: var(--light-gray);
            color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* Features Section */
        .features-section {
            padding: 50px 0;
            background: white;
        }

        .feature-card {
            text-align: center;
            padding: 40px 20px;
            border-radius: 15px;
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--dark-color);
        }

        /* Products Section */
        .products-section {
            padding: 80px 0;
            background: var(--light-gray);
        }

        .section-title {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 15px;
        }

        .section-title .highlight {
            color: #ffffff;;
        }
    
        .product-card{
 
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;

}
     .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .product-image {
    height: 415px;
    background: #eb4e17;
    position: relative;
    overflow: hidden;
}

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.1);
        }

        .product-image .placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #999;
            font-size: 3rem;
        }

        .product-info {
            padding: 50px;
        }

        .product-category {
            color: var(--primary-color);
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .product-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 10px 0;
            color: var(--dark-color);
        }

        .product-price {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        /* Gallery Section */
        .gallery-section {
            padding: 80px 0;
            background: white;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .gallery-item{
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    height: 285px;
    background: #eb4e17;
}

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .gallery-item:hover img {
            transform: scale(1.1);
        }

        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(238, 80, 27, 0.8), rgba(255, 107, 53, 0.8));
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 1;
        }

        /* Reviews Section */
        .reviews-section {
            padding: 80px 0;
            background: var(--light-gray);
        }

        .review-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            height: 100%;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .review-card:hover {
            transform: translateY(-5px);
        }

        .review-stars {
            color: #ffc107;
            margin-bottom: 15px;
        }

        .review-text {
            font-style: italic;
            color: #666;
            margin-bottom: 20px;
        }

        .review-author {
            display: flex;
            align-items: center;
        }

        .review-avatar {
            width: 50px;
            height: 50px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 15px;
        }

        .review-name {
            font-weight: 600;
            color: var(--dark-color);
        }

        /* Footer */
        .footer {
            background: var(--dark-color);
            color: white;
            padding: 50px 0 20px;
        }

        .footer h5 {
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .footer a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: var(--primary-color);
        }

        .footer-bottom {
            border-top: 1px solid #444;
            padding-top: 20px;
            margin-top: 30px;
            text-align: center;
            color: #999;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .contact-icons {
                margin-top: 10px;
            }

            .section-title h2 {
                font-size: 2rem;
            }
        }

        /* Chat Widget */
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .chat-button {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(238, 80, 27, 0.4);
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
        }

        .chat-button:hover {
            transform: scale(1.1);
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 4px 15px rgba(238, 80, 27, 0.4);
            }

            50% {
                box-shadow: 0 4px 25px rgba(238, 80, 27, 0.6);
            }

            100% {
                box-shadow: 0 4px 15px rgba(238, 80, 27, 0.4);
            }
        }
    </style>
     <style>
        body {
            font-family: 'Kanit', sans-serif;
        }

        /* .hero-section {
            background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
            color: white;
            padding: 120px 0 80px;
            min-height: 100vh;
            display: flex;
            align-items: center;
        } */

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            font-weight: 400;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .btn-hero {
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
        }

        .btn-hero:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color:rgb(240, 241, 243);
            margin-bottom: 1rem;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 3rem;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: #eb4e17 !important;
            font-size: 1.5rem;
        }

        .nav-link {
            font-weight: 500;
            color: #2c3e50 !important;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #eb4e17 !important;
        }

        .footer {
            background: #2c3e50;
            color: white;
            padding: 50px 0 20px;
        }

        .footer h5 {
            color: #eb4e17;
            margin-bottom: 20px;
        }

        .footer a {
            color: #bdc3c7;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: #eb4e17;
        }
    </style>

</head>


 

<?php include '../includes/header.php'; ?>

<!-- ✅ HTML -->

 <style>
    * {
      box-sizing: border-box;
    }
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      font-family: 'Sarabun', sans-serif;
    }
    .hero-section {
      position: relative;
      width: 100%;
      height: 100vh;
      overflow: hidden;
    }

    .video-desktop,
    .video-mobile {
      position: absolute;
      top: 50%;
      left: 50%;
      min-width: 100%;
      min-height: 100%;
      transform: translate(-50%, -50%);
      object-fit: cover;
      z-index: -1;
      display: none;
    }

    /* แสดงเฉพาะ Desktop */
    @media (min-width: 769px) {
      .video-desktop {
        display: block;
      }
    }

    /* แสดงเฉพาะ Mobile */
    @media (max-width: 768px) {
      .video-mobile {
        display: block;
      }
    }

    .hero-content {
      position: relative;
      z-index: 1;
      height: 100%;
      width: 100%;
      background: rgba(0, 0, 0, 0.22);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      color: white;
      padding: 2rem;
    }

    .hero-content img {
      width: 180px;
      margin-bottom: 1rem;
    }

    .hero-subtitle {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .hero-btn {
      background-color: #ff6600;
      color: white;
      padding: 0.8rem 1.5rem;
      border-radius: 10px;
      text-decoration: none;
      font-size: 1rem;
      transition: background 0.3s ease;
    }

    .hero-btn:hover {
      background-color: #e65c00;
    }

    @media (max-width: 768px) {
      .hero-content img {
        width: 140px;
      }

      .hero-subtitle {
        font-size: 1.2rem;
      }

      .hero-btn {
        font-size: 0.9rem;
      }
    }
  </style>
 <section class="hero-section"style="
    margin-top: 120px;
">
    <!-- วิดีโอ Desktop -->
    <video autoplay muted loop playsinline class="video-desktop">
      <source src="assets/video/gt-banner.mp4" type="video/mp4">
    </video>

    <!-- วิดีโอ Mobile -->
    <video autoplay muted loop playsinline class="video-mobile">
      <source src="assets/video/gt-mobile.mp4" type="video/mp4">
    </video>

    <!-- เนื้อหาทับวิดีโอ -->
    <div class="hero-content">
      <img src="assets/images/Gt_Logo.png" alt="GT Logo">
      <p class="hero-subtitle">ออกแบบและผลิตเสื้อกีฬาคุณภาพสูง</p>
      <a href="design.php" class="hero-btn">เริ่มออกแบบเลย</a>
    </div>
  </section>

<!-- ✅ CSS -->
<style>
.hero-section {
  position: relative;
  width: 100%;
  height: auto; /* เต็มหน้าจอ */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* ครอบคลุมโดยไม่ยืด */
  z-index: 0;
  margin-top: 20px;
}

/* ✅ เนื้อหาทับวิดีโอ */
.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  padding: 0 20px;
}

.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.hero-btn {
  display: inline-block;
  padding: 12px 24px;
  background-color:rgb(238, 69, 18); /* สีเหลือง */
  color: #ffff;
  font-weight: bold;
  text-decoration: none;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.hero-btn:hover {
  background-color:rgb(221, 97, 14);
}

/* ✅ Responsive สำหรับจอเล็ก */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }
}
</style>

 <!-- Products Section -->
        <section class="products-section" id="products"style="background-image: url(assets/images/White_dot_BG-scaled.jpg);">
            <div class="container"style="margin-top:0px;">
               

               
<div class="section" style="padding: 40px 20px; background-color: #f5f5f5;">
    <div class="container" style="max-width: 1200px; margin: 0 auto;">
        <h2 style="font-size: 32px; text-align: center;">
            ค้นพบเสื้อที่ใช่ <span style="color: #e63946;">สำหรับทุกไลฟ์สไตล์</span>
        </h2>
        <p style="text-align: center; font-size: 16px; margin-bottom: 40px;">
            ผลิตเสื้อด้วยผ้าคุณภาพพรีเมี่ยม มีเนื้อผ้าให้เลือกหลากหลายชนิด<br>
            ใส่ใจในทุกรายละเอียด พร้อมจัดส่งทั่วประเทศ
        </p>

        <div class="product-grid" style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;">
            <?php
            $items = [
                [
                    'title' => 'เสื้อบริษัท',
                    'desc' => 'เสื้อบริษัท สะท้อนความเป็นมืออาชีพ นำเสนอสไตล์ของคุณ พร้อมให้คุณดูดีในทุกวัน',
                    'img' => 'https://www.finixsports.com/wp-content/uploads/2024/08/S__43687946-1024x695.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#company_design'
                ],
                [
                    'title' => 'เสื้อกิจกรรม',
                    'desc' => 'พร้อมสำหรับทุกความสนุกด้วยเสื้อกิจกรรม ที่ให้คุณดูโดดเด่นและรู้สึกสบายทุกการเคลื่อนไหว',
                    'img' => 'https://www.finixsports.com/wp-content/uploads/2024/08/S__43687939-1024x695.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#activities_design'
                ],
                [
                    'title' => 'เสื้อกีฬาสี โรงเรียน',
                    'desc' => 'โชว์พลังทีมด้วยเสื้อกีฬาสีที่สร้างสรรค์เพื่อความสนุกและความสบายในการแข่งขัน',
                    'img' => 'https://www.finixsports.com/wp-content/uploads/2024/08/S__43687947.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#agency_design'
                ],
                [
                    'title' => 'เสื้อกีฬา',
                    'desc' => 'เสื้อกีฬาที่ทำให้คุณรู้สึกสบาย มั่นใจในทุกการเคลื่อนไหว',
                    'img' => 'https://www.finixsports.com/wp-content/uploads/2024/08/S__43687940-1024x687.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#sport_design'
                ],
                [
                    'title' => 'เสื้อหน่วยงานราชการ',
                    'desc' => 'เสื้อหน่วยงานราชการ บริการไว คุณภาพสูง ประทับใจทุกหน่วยงาน',
                    'img' => 'https://www.finixsports.com/wp-content/uploads/2024/08/S__43687941-1024x693.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#agency_design'
                ],
                [
                    'title' => 'เสื้อดีไซน์พิเศษ',
                    'desc' => 'ออกแบบพิเศษเฉพาะคุณ กับดีไซน์ที่ไม่ซ้ำใคร',
                    'img' => 'https://www.finixsports.com/wp-content/uploads/2024/08/S__43687942-1024x697.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#finix_design'
                ],
            ];

            foreach ($items as $item): ?>
                <div class="product-card" style="width: 300px; background: #fff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">
                    <a href="<?= $item['link'] ?>" style="text-decoration: none; color: inherit;">
                        <div class="image" style="background-image: url('<?= $item['img'] ?>'); height: 200px; background-size: cover; background-position: center;"></div>
                        <div class="content" style="padding: 20px;">
                            <h3 style="font-size: 20px; margin: 0 0 10px;"><?= $item['title'] ?></h3>
                            <p style="font-size: 14px;"><?= $item['desc'] ?></p>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

                <div class="text-center mt-5">
                    <a href="#" class="btn-hero">ดูสินค้าทั้งหมด</a>
                </div>
            </div>
        </section>


        <!-- Features Section -->
        <section class="features-section" id="features"style="margin-top:0px;background-image: url(assets/images/Banner_BG_1-scaled.jpg);">
            <div class="container">
              <h2 style="font-size: 32px; text-align: center;">
            ทำไมต้อง  <span style="color:rgb(255, 255, 255);">GT SPORT DESIGN</span>
        </h2>
        <p style="text-align: center; font-size: 16px; margin-bottom: 40px;">
            เหตุผลที่ลูกค้าเลือกใช้บริการของเรา
        </p>
        <div class="row g-4">
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <h5 class="feature-title">ส่งชิ้นผ้า<br>ให้ฟรี!</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h5 class="feature-title">ตอบแชทไว<br>ภายใน 5 นาที</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <h5 class="feature-title">ปรับแบบ<br>จนพอใจ</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-award"></i>
                            </div>
                            <h5 class="feature-title">ทุกโลโก้<br>ราคาเดียว</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <h5 class="feature-title">รวม VAT<br>แล้วไม่บวกเพิ่ม</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <h5 class="feature-title">รับประกันสินค้า<br>เคลมฟรีใน 7 วัน</h5>
                        </div>
                    </div>
                </div>
            </div>
        </section>

       
        <!-- Gallery Section -->
        <section class="gallery-section" id="gallery"style="background-image: url(assets/images/Contact_BG-scaled.jpg);">
            <div class="container"style="margin-top:0px;">
                <div class="section-title">
                    <h2>รีวิวความไว้ใจจาก <span class="highlight">GT SPORT DESIGN</span></h2>
                    <p class="lead">ผลงานการออกแบบและผลิตเสื้อกีฬาของเรา</p>
                </div>

                <div class="gallery-grid">
                    <?php if (!empty($gallery_items)): ?>
                        <?php foreach (array_slice($gallery_items, 0, 6) as $item): ?>
                            <div class="gallery-item">
                                <img src=""
                                    alt="<?php echo htmlspecialchars($item['title']); ?>">
                                <div class="gallery-overlay">
                                    <span><?php echo htmlspecialchars($item['title']); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Fallback gallery -->
                        <?php for ($i = 0; $i < 1; $i++): ?>
                            <div class="gallery-item">
                                <img src="uploads/gallery/review/LINE_ALBUM_รีวิวจากลูกค้า_241018_2.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                        <div class="gallery-item">
                                <img src="uploads/gallery/review/LINE_ALBUM_รีวิวจากลูกค้า_241018_3.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="uploads/gallery/review/LINE_ALBUM_รีวิวจากลูกค้า_241018_5.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="uploads/gallery/review/LINE_ALBUM_รีวิวจากลูกค้า_241018_7.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="uploads/gallery/review/LINE_ALBUM_รีวิวจากลูกค้า_241018_8.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="uploads/gallery/review/LINE_ALBUM_รีวิวจากลูกค้า_241018_9.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="uploads/gallery/review/LINE_ALBUM_รีวิวจากลูกค้า_241018_10.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="uploads/gallery/review/LINE_ALBUM_รีวิวจากลูกค้า_241018_11.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                        <?php endfor; ?>
                    <?php endif; ?>
                </div>

                <div class="text-center mt-5">
                    <a href="#" class="btn-hero">ดูผลงานเพิ่มเติม</a>
                </div>
            </div>
        </section>

        <!-- Reviews Section -->
        <section class="reviews-section" id="reviews">
            <div class="container">
                <div class="section-title">
                    <h2>ความคิดเห็นจาก<span class="highlight"><h2 style="color: rgb(19, 20, 20);">ลูกค้า</h2></span></h2>
                    <p class="lead"style="color: rgb(19, 20, 20);">รีวิวจากลูกค้าที่ใช้บริการจริง</p>
                </div>

                <div class="row g-4">
                    <?php if (!empty($reviews)): ?>
                        <?php foreach (array_slice($reviews, 0, 3) as $review): ?>
                            <div class="col-lg-4">
                                <div class="review-card">
                                    <div class="review-stars">
                                        <?php for ($i = 0; $i < $review['rating']; $i++): ?>
                                            <i class="fas fa-star"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <p class="review-text"><?php echo htmlspecialchars($review['comment']); ?></p>
                                    <div class="review-author">
                                        <div class="review-avatar">
                                            <?php echo strtoupper(substr($review['customer_name'] ?: 'A', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="review-name">
                                                <?php echo htmlspecialchars($review['customer_name'] ?: 'ลูกค้า'); ?></div>
                                            <small
                                                class="text-muted"><?php echo htmlspecialchars($review['product_name']); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Fallback reviews -->
                        <div class="col-lg-4">
                            <div class="review-card">
                                <div class="review-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="review-text">ใส่สบาย ลายสวย</p>
                                <div class="review-author">
                                    <div class="review-avatar">ส</div>
                                    <div>
                                        <div class="review-name">สมชาย ใจดี</div>
                                        <small class="text-muted">เสื้อกีฬาทีม</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="review-card">
                                <div class="review-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="review-text">เสื้อคุณภาพดีมาก  </p>
                                <div class="review-author">
                                    <div class="review-avatar">ส</div>
                                    <div>
                                        <div class="review-name">zeza</div>
                                        <small class="text-muted">เสื้อกีฬาทีม</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="review-card">
                                <div class="review-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="review-text">ทีมงานบริการดีเยี่ยม</p>
                                <div class="review-author">
                                    <div class="review-avatar">ส</div>
                                    <div>
                                        <div class="review-name">baet</div>
                                        <small class="text-muted">เสื้อกีฬาทีม</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>

        <?php include './includes/footer.php'; ?>

        <!-- Chat Widget -->
        <div class="chat-widget">
            <div class="chat-button" onclick="openChat()">
                <i class="fas fa-comments"></i>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Chat widget functionality
            function openChat() {
                // สำหรับตอนนี้ให้เปิด LINE
                window.open('chat_widget.php<?php echo $settings['chat-widget']; ?>', '_blank');
            }

            // Add scroll effect to navbar
            window.addEventListener('scroll', function () {
                const navbar = document.querySelector('.main-navbar');
                if (window.scrollY > 100) {
                    navbar.style.boxShadow = '0 4px 20px rgba(0,0,0,0.1)';
                } else {
                    navbar.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                }
            });
        </script>
</body>

</html>