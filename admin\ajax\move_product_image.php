<?php
require_once '../../includes/db.php';
require_once '../../includes/config.php';
require_once '../includes/auth.php';

// ตรวจสอบว่าเป็น AJAX request
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    http_response_code(403);
    die('Forbidden');
}

// ตรวจสอบสิทธิ์ผู้ใช้
requireAdmin();

// ตรวจสอบข้อมูลที่ส่งมา
if (!isset($_POST['image_id']) || !is_numeric($_POST['image_id']) || !isset($_POST['direction'])) {
    echo json_encode(['success' => false, 'message' => 'ข้อมูลไม่ถูกต้อง']);
    exit;
}

$image_id = $_POST['image_id'];
$direction = $_POST['direction'];

if ($direction !== 'up' && $direction !== 'down') {
    echo json_encode(['success' => false, 'message' => 'ทิศทางไม่ถูกต้อง']);
    exit;
}

try {
    $db = getDB();
    
    // ดึงข้อมูลรูปภาพปัจจุบัน
    $stmt = $db->prepare("SELECT * FROM product_images WHERE id = ?");
    $stmt->execute([$image_id]);
    $current_image = $stmt->fetch();
    
    if (!$current_image) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบรูปภาพ']);
        exit;
    }
    
    $product_id = $current_image['product_id'];
    $current_sort = $current_image['sort_order'];
    
    // ค้นหารูปภาพที่จะสลับตำแหน่ง
    if ($direction === 'up') {
        // หารูปภาพที่มี sort_order น้อยกว่า (อยู่ด้านบน)
        $stmt = $db->prepare("
            SELECT * FROM product_images 
            WHERE product_id = ? AND sort_order < ? 
            ORDER BY sort_order DESC 
            LIMIT 1
        ");
    } else {
        // หารูปภาพที่มี sort_order มากกว่า (อยู่ด้านล่าง)
        $stmt = $db->prepare("
            SELECT * FROM product_images 
            WHERE product_id = ? AND sort_order > ? 
            ORDER BY sort_order ASC 
            LIMIT 1
        ");
    }
    
    $stmt->execute([$product_id, $current_sort]);
    $swap_image = $stmt->fetch();
    
    if (!$swap_image) {
        echo json_encode(['success' => false, 'message' => 'ไม่สามารถเลื่อนตำแหน่งได้อีก']);
        exit;
    }
    
    // สลับตำแหน่ง
    $db->beginTransaction();
    
    // อัพเดทตำแหน่งรูปภาพปัจจุบัน
    $stmt = $db->prepare("
        UPDATE product_images 
        SET sort_order = ? 
        WHERE id = ?
    ");
    $stmt->execute([$swap_image['sort_order'], $current_image['id']]);
    
    // อัพเดทตำแหน่งรูปภาพที่สลับ
    $stmt = $db->prepare("
        UPDATE product_images 
        SET sort_order = ? 
        WHERE id = ?
    ");
    $stmt->execute([$current_sort, $swap_image['id']]);
    
    $db->commit();
    
    echo json_encode(['success' => true, 'message' => 'เลื่อนตำแหน่งรูปภาพเรียบร้อยแล้ว']);
    
} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}