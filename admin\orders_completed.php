<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ดึงข้อมูลคำสั่งซื้อที่เสร็จสิ้น
$orders = [];
$date_filter = $_GET['date_filter'] ?? 'this_month';

try {
    $date_condition = '';
    switch ($date_filter) {
        case 'today':
            $date_condition = "AND DATE(o.completed_at) = CURDATE()";
            break;
        case 'this_week':
            $date_condition = "AND YEARWEEK(o.completed_at) = YEARWEEK(NOW())";
            break;
        case 'this_month':
            $date_condition = "AND YEAR(o.completed_at) = YEAR(NOW()) AND MONTH(o.completed_at) = MONTH(NOW())";
            break;
        case 'last_month':
            $date_condition = "AND YEAR(o.completed_at) = YEAR(NOW() - INTERVAL 1 MONTH) AND MONTH(o.completed_at) = MONTH(NOW() - INTERVAL 1 MONTH)";
            break;
    }
    
    $stmt = $pdo->query("
        SELECT o.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.status = 'completed' $date_condition
        ORDER BY o.completed_at DESC
        LIMIT 50
    ");
    $orders = $stmt->fetchAll();
} catch (Exception $e) {
    // ข้อมูลจำลอง
    $orders = [
        [
            'id' => 5,
            'order_number' => 'ORD-2024-005',
            'customer_name' => 'นาย จ ประสบสุข',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'total_amount' => 950.00,
            'status' => 'completed',
            'payment_status' => 'paid',
            'completed_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
            'created_at' => date('Y-m-d H:i:s', strtotime('-8 days')),
            'delivery_status' => 'delivered',
            'notes' => 'เสื้อโปโลบริษัท 15 ตัว สีกรม'
        ],
        [
            'id' => 6,
            'order_number' => 'ORD-2024-006',
            'customer_name' => 'นางสาว ฉ มั่งมี',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'total_amount' => 1500.00,
            'status' => 'completed',
            'payment_status' => 'paid',
            'completed_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
            'created_at' => date('Y-m-d H:i:s', strtotime('-10 days')),
            'delivery_status' => 'shipped',
            'notes' => 'ชุดทีมวอลเลย์บอล 12 ตัว'
        ]
    ];
}

// คำนวณสถิติ
$total_revenue = array_sum(array_column($orders, 'total_amount'));
$avg_completion_time = 0;
if (!empty($orders)) {
    $completion_times = [];
    foreach ($orders as $order) {
        $created = new DateTime($order['created_at']);
        $completed = new DateTime($order['completed_at']);
        $completion_times[] = $created->diff($completed)->days;
    }
    $avg_completion_time = array_sum($completion_times) / count($completion_times);
}

function getDeliveryStatusBadge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning">รอจัดส่ง</span>',
        'shipped' => '<span class="badge bg-info">จัดส่งแล้ว</span>',
        'delivered' => '<span class="badge bg-success">ส่งถึงแล้ว</span>'
    ];
    return $badges[$status] ?? '<span class="badge bg-secondary">' . $status . '</span>';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>คำสั่งซื้อเสร็จสิ้น - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .order-card {
            border-left: 5px solid #28a745;
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
        .stats-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php" class="active"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">คำสั่งซื้อเสร็จสิ้น</h4>
                <small class="text-muted">รายการคำสั่งซื้อที่ดำเนินการเสร็จสิ้นแล้ว</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="orders.php">คำสั่งซื้อ</a></li>
                    <li class="breadcrumb-item active">เสร็จสิ้น</li>
                </ol>
            </nav>

            <!-- Order Status Tabs -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <a href="orders_pending.php" class="btn btn-outline-warning w-100">
                                <i class="fas fa-clock me-2"></i>รอดำเนินการ
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders_processing.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-cogs me-2"></i>กำลังผลิต
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders_completed.php" class="btn btn-success w-100">
                                <i class="fas fa-check me-2"></i>เสร็จสิ้น
                                <span class="badge bg-white text-success ms-2"><?php echo count($orders); ?></span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-2"></i>ทั้งหมด
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3>฿<?php echo number_format($total_revenue, 2); ?></h3>
                        <p class="mb-0">รายได้รวม</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3><?php echo count($orders); ?></h3>
                        <p class="mb-0">คำสั่งซื้อเสร็จสิ้น</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3><?php echo round($avg_completion_time, 1); ?></h3>
                        <p class="mb-0">วันเฉลี่ยในการผลิต</p>
                    </div>
                </div>
            </div>

            <!-- Date Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-0">กรองตามช่วงเวลา</h6>
                        </div>
                        <div class="col-md-6">
                            <select class="form-select" onchange="filterByDate(this.value)">
                                <option value="today" <?php echo $date_filter === 'today' ? 'selected' : ''; ?>>วันนี้</option>
                                <option value="this_week" <?php echo $date_filter === 'this_week' ? 'selected' : ''; ?>>สัปดาห์นี้</option>
                                <option value="this_month" <?php echo $date_filter === 'this_month' ? 'selected' : ''; ?>>เดือนนี้</option>
                                <option value="last_month" <?php echo $date_filter === 'last_month' ? 'selected' : ''; ?>>เดือนที่แล้ว</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders List -->
            <?php if (empty($orders)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                    <h3>ไม่มีคำสั่งซื้อเสร็จสิ้นในช่วงเวลานี้</h3>
                    <p class="text-muted">เปลี่ยนช่วงเวลาการกรองเพื่อดูข้อมูลเพิ่มเติม</p>
                </div>
            <?php else: ?>
                <?php foreach ($orders as $order): ?>
                    <?php 
                    $created = new DateTime($order['created_at']);
                    $completed = new DateTime($order['completed_at']);
                    $completion_days = $created->diff($completed)->days;
                    ?>
                <div class="card order-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-0"><?php echo htmlspecialchars($order['order_number']); ?></h6>
                                    <span class="badge bg-success">
                                        <i class="fas fa-check me-1"></i>เสร็จสิ้น
                                    </span>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>ลูกค้า:</strong> <?php echo htmlspecialchars($order['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                        <strong>ยอดรวม:</strong> ฿<?php echo number_format($order['total_amount'], 2); ?><br>
                                        <strong>วันที่สั่ง:</strong> <?php echo date('d/m/Y', strtotime($order['created_at'])); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>เสร็จเมื่อ:</strong> <?php echo date('d/m/Y H:i', strtotime($order['completed_at'])); ?><br>
                                        <strong>ระยะเวลาผลิต:</strong> <?php echo $completion_days; ?> วัน<br>
                                        <strong>สถานะจัดส่ง:</strong> <?php echo getDeliveryStatusBadge($order['delivery_status'] ?? 'pending'); ?>
                                    </div>
                                </div>
                                
                                <?php if (!empty($order['notes'])): ?>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>หมายเหตุ:</strong> <?php echo htmlspecialchars($order['notes']); ?>
                                    </small>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 text-end">
                                <button class="btn btn-outline-primary btn-sm mb-2" data-bs-toggle="modal" data-bs-target="#orderDetailModal<?php echo $order['id']; ?>">
                                    <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                </button><br>
                                <button class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-download me-1"></i>ใบเสร็จ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Detail Modal -->
                <div class="modal fade" id="orderDetailModal<?php echo $order['id']; ?>" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">รายละเอียดคำสั่งซื้อ: <?php echo htmlspecialchars($order['order_number']); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>ข้อมูลลูกค้า</h6>
                                        <p>
                                            <strong>ชื่อ:</strong> <?php echo htmlspecialchars($order['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                            <strong>โทร:</strong> <?php echo htmlspecialchars($order['customer_phone'] ?? 'ไม่ระบุ'); ?><br>
                                            <strong>อีเมล:</strong> <?php echo htmlspecialchars($order['customer_email'] ?? 'ไม่ระบุ'); ?>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>ข้อมูลคำสั่งซื้อ</h6>
                                        <p>
                                            <strong>เลขที่:</strong> <?php echo htmlspecialchars($order['order_number']); ?><br>
                                            <strong>วันที่สั่ง:</strong> <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?><br>
                                            <strong>เสร็จเมื่อ:</strong> <?php echo date('d/m/Y H:i', strtotime($order['completed_at'])); ?><br>
                                            <strong>ยอดรวม:</strong> ฿<?php echo number_format($order['total_amount'], 2); ?>
                                        </p>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>ข้อมูลการผลิต</h6>
                                        <p>
                                            <strong>ระยะเวลาผลิต:</strong> <?php echo $completion_days; ?> วัน<br>
                                            <strong>สถานะจัดส่ง:</strong> <?php echo getDeliveryStatusBadge($order['delivery_status'] ?? 'pending'); ?>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>การชำระเงิน</h6>
                                        <p>
                                            <strong>สถานะ:</strong> <span class="badge bg-success">ชำระแล้ว</span><br>
                                            <strong>ยอดรวม:</strong> ฿<?php echo number_format($order['total_amount'], 2); ?>
                                        </p>
                                    </div>
                                </div>
                                
                                <?php if (!empty($order['notes'])): ?>
                                <div class="mt-3">
                                    <h6>หมายเหตุ</h6>
                                    <p class="text-muted"><?php echo htmlspecialchars($order['notes']); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                                <button type="button" class="btn btn-success">
                                    <i class="fas fa-download me-1"></i>ดาวน์โหลดใบเสร็จ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function filterByDate(dateFilter) {
            window.location.href = 'orders_completed.php?date_filter=' + dateFilter;
        }
    </script>
</body>
</html>
