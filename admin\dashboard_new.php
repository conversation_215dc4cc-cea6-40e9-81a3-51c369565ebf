<?php
session_start();

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// ข้อมูลสถิติ (ตัวอย่าง)
$stats = [
    'today_orders' => 15,
    'today_sales' => 25450,
    'new_customers' => 8,
    'low_stock' => 3,
    'total_products' => 45,
    'total_customers' => 234,
    'pending_orders' => 12,
    'total_revenue' => 156780
];

$page_title = 'แดชบอร์ด';
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title><?php echo $page_title; ?> - GT Sport Design Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></link>
    
    <style>
        :root {
            --primary-color: #ee501b;
            --secondary-color: #ff6b35;
            --dark-color: #303136;
            --light-gray: #f8f9fa;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            background: var(--light-gray);
            font-family: 'Kanit', sans-serif;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-nav {
            background: white;
            padding: 15px 0;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-btn {
            background: white;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            margin: 0 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .nav-btn:hover, .nav-btn.active {
            background: var(--primary-color);
            color: white;
            text-decoration: none;
        }
        
        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid var(--primary-color);
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .stat-card.success { border-left-color: var(--success-color); }
        .stat-card.warning { border-left-color: var(--warning-color); }
        .stat-card.danger { border-left-color: var(--danger-color); }
        .stat-card.info { border-left-color: var(--info-color); }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 15px;
        }
        
        .stat-icon.primary { background: var(--primary-color); }
        .stat-icon.success { background: var(--success-color); }
        .stat-icon.warning { background: var(--warning-color); }
        .stat-icon.danger { background: var(--danger-color); }
        .stat-icon.info { background: var(--info-color); }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .stat-change {
            font-size: 0.8rem;
            padding: 3px 8px;
            border-radius: 10px;
        }
        
        .stat-change.up {
            background: #d4edda;
            color: #155724;
        }
        
        .stat-change.down {
            background: #f8d7da;
            color: #721c24;
        }
        
        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .recent-activity {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1rem;
            color: white;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-time {
            font-size: 0.8rem;
            color: #6c757d;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .action-btn {
            background: var(--light-gray);
            border: none;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            text-decoration: none;
            color: var(--dark-color);
            transition: all 0.3s ease;
            display: block;
            margin-bottom: 10px;
        }
        
        .action-btn:hover {
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
        }
        
        .table-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-pending { background: #fff3cd; color: #856404; }
        .status-processing { background: #d1ecf1; color: #0c5460; }
        .status-completed { background: #d4edda; color: #155724; }
        .status-cancelled { background: #f8d7da; color: #721c24; }
        
        @media (max-width: 768px) {
            .admin-header {
                padding: 15px 0;
            }
            
            .stat-card {
                padding: 20px;
            }
            
            .nav-btn {
                margin: 5px 0;
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>

<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="mb-0">
                        <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด GT Sport Design
                    </h2>
                    <small>ระบบจัดการหลังบ้าน</small>
                </div>
                <div class="col-md-6 text-end">
                    <span class="me-3">
                        <i class="fas fa-user me-1"></i>
                        <?php echo $_SESSION['admin_name'] ?? 'ผู้ดูแลระบบ'; ?>
                    </span>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-clock me-1"></i>
                        <span id="currentTime"></span>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Navigation -->
        <div class="admin-nav">
            <div class="text-center">
                <a href="dashboard.php" class="nav-btn active">
                    <i class="fas fa-tachometer-alt"></i>แดชบอร์ด
                </a>
                <a href="products.php" class="nav-btn">
                    <i class="fas fa-tshirt"></i>สินค้า
                </a>
                <a href="orders.php" class="nav-btn">
                    <i class="fas fa-shopping-cart"></i>คำสั่งซื้อ
                </a>
                <a href="customers.php" class="nav-btn">
                    <i class="fas fa-users"></i>ลูกค้า
                </a>
                <a href="designs.php" class="nav-btn">
                    <i class="fas fa-palette"></i>การออกแบบ
                </a>
                <a href="reports.php" class="nav-btn">
                    <i class="fas fa-chart-bar"></i>รายงาน
                </a>
                <a href="admin_guide.php" class="nav-btn">
                    <i class="fas fa-book"></i>คู่มือ
                </a>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row">
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon primary">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['today_orders']); ?></div>
                    <div class="stat-label">คำสั่งซื้อวันนี้</div>
                    <span class="stat-change up">
                        <i class="fas fa-arrow-up"></i> +12%
                    </span>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card success">
                    <div class="stat-icon success">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-number">฿<?php echo number_format($stats['today_sales']); ?></div>
                    <div class="stat-label">ยอดขายวันนี้</div>
                    <span class="stat-change up">
                        <i class="fas fa-arrow-up"></i> +8%
                    </span>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card info">
                    <div class="stat-icon info">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['new_customers']); ?></div>
                    <div class="stat-label">ลูกค้าใหม่</div>
                    <span class="stat-change up">
                        <i class="fas fa-arrow-up"></i> +15%
                    </span>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card warning">
                    <div class="stat-icon warning">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-number"><?php echo number_format($stats['low_stock']); ?></div>
                    <div class="stat-label">สินค้าใกล้หมด</div>
                    <span class="stat-change down">
                        <i class="fas fa-arrow-down"></i> ต้องเติม
                    </span>
                </div>
            </div>
        </div>

        <!-- Charts and Activities -->
        <div class="row">
            <!-- Sales Chart -->
            <div class="col-lg-8">
                <div class="chart-card">
                    <h5 class="mb-4">
                        <i class="fas fa-chart-line me-2 text-primary"></i>ยอดขายรายเดือน
                    </h5>
                    <canvas id="salesChart" height="100"></canvas>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="col-lg-4">
                <div class="recent-activity">
                    <h5 class="mb-4">
                        <i class="fas fa-clock me-2 text-primary"></i>กิจกรรมล่าสุด
                    </h5>

                    <div class="activity-item">
                        <div class="activity-icon" style="background: var(--success-color);">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="activity-content">
                            <strong>คำสั่งซื้อใหม่ #001</strong>
                            <div>คุณสมชาย สั่งซื้อเสื้อยืด 2 ตัว</div>
                            <div class="activity-time">5 นาทีที่แล้ว</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon" style="background: var(--info-color);">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="activity-content">
                            <strong>การชำระเงิน</strong>
                            <div>ได้รับการชำระเงิน ฿1,250</div>
                            <div class="activity-time">15 นาทีที่แล้ว</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon" style="background: var(--warning-color);">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="activity-content">
                            <strong>การออกแบบใหม่</strong>
                            <div>ลูกค้าส่งคำขอออกแบบ</div>
                            <div class="activity-time">30 นาทีที่แล้ว</div>
                        </div>
                    </div>

                    <div class="activity-item">
                        <div class="activity-icon" style="background: var(--primary-color);">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <strong>ลูกค้าใหม่</strong>
                            <div>คุณสมหญิง สมัครสมาชิก</div>
                            <div class="activity-time">1 ชั่วโมงที่แล้ว</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Orders and Quick Actions -->
        <div class="row">
            <!-- Recent Orders -->
            <div class="col-lg-8">
                <div class="table-card">
                    <h5 class="mb-4">
                        <i class="fas fa-list me-2 text-primary"></i>คำสั่งซื้อล่าสุด
                    </h5>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>รหัส</th>
                                    <th>ลูกค้า</th>
                                    <th>สินค้า</th>
                                    <th>ยอดรวม</th>
                                    <th>สถานะ</th>
                                    <th>วันที่</th>
                                    <th>การดำเนินการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>#001</strong></td>
                                    <td>คุณสมชาย</td>
                                    <td>เสื้อยืด x2</td>
                                    <td><strong>฿1,250</strong></td>
                                    <td><span class="status-badge status-pending">รอดำเนินการ</span></td>
                                    <td>27/01/2025</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">ดู</button>
                                        <button class="btn btn-sm btn-outline-success">อนุมัติ</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>#002</strong></td>
                                    <td>คุณสมหญิง</td>
                                    <td>เสื้อโปโล x1</td>
                                    <td><strong>฿850</strong></td>
                                    <td><span class="status-badge status-processing">กำลังผลิต</span></td>
                                    <td>26/01/2025</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">ดู</button>
                                        <button class="btn btn-sm btn-outline-info">อัปเดต</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>#003</strong></td>
                                    <td>คุณสมศักดิ์</td>
                                    <td>เสื้อกีฬา x3</td>
                                    <td><strong>฿2,100</strong></td>
                                    <td><span class="status-badge status-completed">เสร็จสิ้น</span></td>
                                    <td>25/01/2025</td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary">ดู</button>
                                        <button class="btn btn-sm btn-outline-secondary">พิมพ์</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="text-center mt-3">
                        <a href="orders.php" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>ดูคำสั่งซื้อทั้งหมด
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-lg-4">
                <div class="quick-actions">
                    <h5 class="mb-4">
                        <i class="fas fa-bolt me-2 text-primary"></i>การดำเนินการด่วน
                    </h5>

                    <a href="products.php?action=add" class="action-btn">
                        <i class="fas fa-plus-circle me-2"></i>
                        <strong>เพิ่มสินค้าใหม่</strong>
                        <small class="d-block text-muted">เพิ่มสินค้าเข้าสู่ระบบ</small>
                    </a>

                    <a href="orders.php?status=pending" class="action-btn">
                        <i class="fas fa-clock me-2"></i>
                        <strong>ตรวจสอบคำสั่งซื้อ</strong>
                        <small class="d-block text-muted"><?php echo $stats['pending_orders']; ?> รายการรอดำเนินการ</small>
                    </a>

                    <a href="customers.php?action=add" class="action-btn">
                        <i class="fas fa-user-plus me-2"></i>
                        <strong>เพิ่มลูกค้าใหม่</strong>
                        <small class="d-block text-muted">บันทึกข้อมูลลูกค้า</small>
                    </a>

                    <a href="designs.php" class="action-btn">
                        <i class="fas fa-palette me-2"></i>
                        <strong>จัดการการออกแบบ</strong>
                        <small class="d-block text-muted">ตรวจสอบคำขอออกแบบ</small>
                    </a>

                    <a href="reports.php" class="action-btn">
                        <i class="fas fa-chart-bar me-2"></i>
                        <strong>ดูรายงาน</strong>
                        <small class="d-block text-muted">สถิติและการวิเคราะห์</small>
                    </a>

                    <a href="settings.php" class="action-btn">
                        <i class="fas fa-cog me-2"></i>
                        <strong>ตั้งค่าระบบ</strong>
                        <small class="d-block text-muted">จัดการการตั้งค่า</small>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('th-TH');
            document.getElementById('currentTime').textContent = timeString;
        }

        updateTime();
        setInterval(updateTime, 1000);

        // Sales Chart
        const ctx = document.getElementById('salesChart').getContext('2d');
        const salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['ม.ค.', 'ก.พ.', 'มี.ค.', 'เม.ย.', 'พ.ค.', 'มิ.ย.'],
                datasets: [{
                    label: 'ยอดขาย (บาท)',
                    data: [12000, 19000, 15000, 25000, 22000, 30000],
                    borderColor: '#ee501b',
                    backgroundColor: 'rgba(238, 80, 27, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '฿' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Auto refresh stats every 30 seconds
        setInterval(function() {
            // In real implementation, this would fetch new data via AJAX
            console.log('Refreshing stats...');
        }, 30000);

        // Add click tracking for navigation
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.nav-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>
