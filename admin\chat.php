<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อก
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$pdo = getDbConnection();

// การส่งข้อความจาก Admin
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'send_admin_message') {
    try {
        $conversation_id = (int)$_POST['conversation_id'];
        $message = trim($_POST['message']);

        if ($conversation_id && $message) {
            $stmt = $pdo->prepare("
                INSERT INTO chat_messages (conversation_id, sender_type, sender_id, message, created_at)
                VALUES (?, 'admin', ?, ?, NOW())
            ");
            $stmt->execute([$conversation_id, $_SESSION['admin_id'], $message]);

            // ปรับ conversation
            $stmt = $pdo->prepare("
                UPDATE chat_conversations
                SET updated_at = NOW(), has_new_messages = 0
                WHERE id = ?
            ");
            $stmt->execute([$conversation_id]);

            echo json_encode(['success' => true]);
            exit;
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

// รายการ conversations
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';

$where_conditions = ['1=1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(c.customer_name LIKE ? OR cm.message LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "cc.status = ?";
    $params[] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// ใช้ตาราง chat_messages โดยตรงแทน chat_conversations
$sql = "
    SELECT
        c.id as customer_id,
        COALESCE(c.name, 'ค้าไม่ชื่อ') as customer_name,
        COALESCE(c.email, '') as customer_email,
        COUNT(cm.id) as message_count,
        MAX(cm.created_at) as last_message_time,
        SUM(CASE WHEN cm.sender_type = 'customer' AND cm.is_read = 0 THEN 1 ELSE 0 END) as unread_count
    FROM customers c
    INNER JOIN chat_messages cm ON c.id = cm.customer_id
    WHERE $where_clause
    GROUP BY c.id, c.name, c.email
    ORDER BY MAX(cm.created_at) DESC
    LIMIT 50
";

try {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $conversations = $stmt->fetchAll();
} catch (Exception $e) {
    $conversations = [];
    $error = "ไม่สามารถโหลดข้อมูลการสนทนาได้: " . $e->getMessage();
}

// สถิติ
try {
    $stats_sql = "
        SELECT
            COUNT(DISTINCT cm.customer_id) as total_conversations,
            COUNT(DISTINCT CASE WHEN DATE(cm.created_at) = CURDATE() THEN cm.customer_id END) as today_conversations,
            SUM(CASE WHEN cm.sender_type = 'customer' AND cm.is_read = 0 THEN 1 ELSE 0 END) as total_unread,
            COUNT(cm.id) as total_messages
        FROM chat_messages cm
        LEFT JOIN customers c ON cm.customer_id = c.id
    ";
    $stats_stmt = $pdo->query($stats_sql);
    $stats = $stats_stmt->fetch();

    // เพิ่มข้อมูลสถิติที่ขาดหายไป
    $stats['active_conversations'] = $stats['total_conversations'];
} catch (Exception $e) {
    $stats = [
        'total_conversations' => 0,
        'active_conversations' => 0,
        'today_conversations' => 0,
        'total_unread' => 0
    ];
}

// ถ้าไม่มีข้อมูล ให้กำหนดค่าเริ่มต้น
if (!$stats) {
    $stats = [
        'total_conversations' => 0,
        'active_conversations' => 0,
        'today_conversations' => 0,
        'total_unread' => 0
    ];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แชทสด - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
        }

        .chat-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.05);
            height: calc(100vh - 120px);
            display: flex;
            overflow: hidden;
        }

        .conversations-panel {
            width: 350px;
            border-right: 1px solid #e1e8ed;
            display: flex;
            flex-direction: column;
        }

        .conversations-header {
            padding: 20px;
            border-bottom: 1px solid #e1e8ed;
            background: #f8f9fa;
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .conversation-item {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .conversation-item:hover {
            background: #f8f9fa;
        }

        .conversation-item.active {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            color: white;
            border-color: #11be97;
        }

        .conversation-meta {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 5px;
        }

        .customer-name {
            font-weight: 600;
            font-size: 14px;
        }

        .conversation-time {
            font-size: 11px;
            opacity: 0.7;
        }

        .conversation-preview {
            font-size: 13px;
            opacity: 0.8;
            margin: 5px 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .unread-badge {
            background: #ff4757;
            color: white;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 11px;
            font-weight: 600;
        }

        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e1e8ed;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: between;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .chat-input-area {
            padding: 20px;
            border-top: 1px solid #e1e8ed;
            background: white;
        }

        .message {
            display: flex;
            margin-bottom: 15px;
            animation: slideIn 0.3s ease;
        }

        .message.admin-message {
            justify-content: flex-end;
        }

        .message.customer-message {
            justify-content: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 15px;
            border-radius: 18px;
            position: relative;
        }

        .admin-message .message-content {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            color: white;
        }

        .customer-message .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e8ed;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            display: block;
            margin-top: 5px;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stats-card.active {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stats-card.today {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stats-card.unread {
            background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
        }

        .sidebar-nav {
            padding: 0;
            margin: 20px 0;
        }

        .sidebar-nav li {
            list-style: none;
        }

        .sidebar-nav a {
            display: block;
            padding: 12px 25px;
            color: #bdc3c7;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #ee501b;
        }

        .sidebar-nav i {
            width: 20px;
            margin-right: 10px;
        }

        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }

        .sidebar-brand small {
            color: #bdc3c7;
        }

        .empty-chat {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #6c757d;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Scrollbar styling */
        .conversations-list::-webkit-scrollbar,
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .conversations-list::-webkit-scrollbar-track,
        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .conversations-list::-webkit-scrollbar-thumb,
        .chat-messages::-webkit-scrollbar-thumb {
            background: #11be97;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4>GT Sport Design</h4>
            <small>Admin Panel</small>
        </div>
        <ul class="sidebar-nav">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> แดชบอร์ด</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> คำร้องซื้อ</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> ค้า</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> ค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i> การออกแบบ</a></li>
            <li><a href="chat.php" class="active"><i class="fas fa-comments"></i> แชทสด</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-comments me-2"></i> แชทสด</h2>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary" onclick="refreshConversations()">
                    <i class="fas fa-sync-alt me-1"></i> รีเฟรช
                </button>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                    <label class="form-check-label" for="autoRefresh">
                        รีเฟรช
                    </label>
                </div>
            </div>
        </div>

        <!--  -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h3><?= number_format($stats['total_conversations']) ?></h3>
                    <p class="mb-0">การสนทนาทั้งหมด</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card active">
                    <h3><?= number_format($stats['active_conversations']) ?></h3>
                    <p class="mb-0">การสนทนาที่ใช้งาน</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card today">
                    <h3><?= number_format($stats['today_conversations']) ?></h3>
                    <p class="mb-0">การสนทนาวันนี้</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card unread">
                    <h3><?= number_format($stats['total_unread']) ?></h3>
                    <p class="mb-0">ข้อความที่ยังไม่ได้อ่าน</p>
                </div>
            </div>
        </div>

        <!-- Chat Container -->
        <div class="chat-container">
            <!-- Conversations Panel -->
            <div class="conversations-panel">
                <div class="conversations-header">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">การสนทนา</h6>
                        <span class="badge bg-primary"><?= count($conversations) ?></span>
                    </div>
                    <div class="search-box">
                        <input type="text" class="form-control form-control-sm"
                               placeholder="ค้นหาการสนทนา..."
                               onkeyup="searchConversations(this.value)">
                    </div>
                </div>

                <div class="conversations-list" id="conversationsList">
                    <?php foreach ($conversations as $conv): ?>
                    <div class="conversation-item"
                         data-customer-id="<?= $conv['customer_id'] ?>"
                         onclick="selectConversation(<?= $conv['customer_id'] ?>)">
                        <div class="conversation-meta">
                            <span class="customer-name">
                                <?= htmlspecialchars($conv['customer_name'] ?: 'ค้าไม่ชื่อ') ?>
                            </span>
                            <?php if ($conv['unread_count'] > 0): ?>
                            <span class="unread-badge"><?= $conv['unread_count'] ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="conversation-time">
                            <?= $conv['last_message_time'] ? date('d/m/Y H:i', strtotime($conv['last_message_time'])) : 'ไม่มีข้อความ' ?>
                        </div>
                        <div class="conversation-preview">
                            <?= $conv['message_count'] ?> ข้อความ
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <?php if (empty($conversations)): ?>
                    <div class="text-center py-4 text-muted">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>ไม่มีการสนทนา</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Chat Panel -->
            <div class="chat-panel">
                <div class="chat-header" id="chatHeader">
                    <div class="empty-chat">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <h5>การสนทนาเพื่อเริ่มแชท</h5>
                        <p>จากรายการทางซ้ายเพื่อดูและตอบข้อความ</p>
                    </div>
                </div>

                <div class="chat-messages" id="chatMessages" style="display: none;">
                    <!-- Messages will be loaded here -->
                </div>

                <div class="chat-input-area" id="chatInputArea" style="display: none;">
                    <form onsubmit="sendAdminMessage(event)">
                        <div class="input-group">
                            <input type="text" class="form-control" id="adminMessageInput"
                                   placeholder="ข้อความ..." required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-paper-plane"></i> ส่ง
                            </button>
                        </div>
                    </form>

                    <!-- Quick Responses -->
                    <div class="mt-2">
                        <small class="text-muted">คำตอบด่วน:</small>
                        <div class="d-flex gap-1 flex-wrap mt-1">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertQuickResponse('ขอบคุณข้อความ  เราจะตรวจสอบและตอบโดยเร็ว')">
                                ขอบคุณ
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertQuickResponse('ณารอ  เรากำลังตรวจสอบข้อมูล')">
                                รอ
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertQuickResponse('หากต้องการข้อมูลเพิ่มเติม  สามารถโทร 085-559-9164 ได้')">
                                ต่อ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentConversationId = null;
        let autoRefreshInterval = null;

        function selectConversation(customerId) {
            currentConversationId = customerId;

            // Update active conversation
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-customer-id="${customerId}"]`).classList.add('active');

            // Show chat interface
            document.getElementById('chatHeader').style.display = 'none';
            document.getElementById('chatMessages').style.display = 'block';
            document.getElementById('chatInputArea').style.display = 'block';

            // Load messages
            loadConversationMessages(customerId);

            // Mark as read
            markConversationAsRead(conversationId);
        }

        function loadConversationMessages(conversationId) {
            fetch(`chat_admin_api.php?action=get_conversation_messages&conversation_id=${conversationId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayMessages(data.messages);
                    }
                })
                .catch(error => {
                    console.error('Error loading messages:', error);
                });
        }

        function displayMessages(messages) {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '';

            messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${message.sender_type}-message`;

                const messageTime = new Date(message.created_at).toLocaleTimeString('th-TH', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                messageDiv.innerHTML = `
                    <div class="message-content">
                        <p>${message.message.replace(/\n/g, '<br>')}</p>
                        <span class="message-time">${messageTime}</span>
                    </div>
                `;

                chatMessages.appendChild(messageDiv);
            });

            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendAdminMessage(event) {
            event.preventDefault();

            const messageInput = document.getElementById('adminMessageInput');
            const message = messageInput.value.trim();

            if (!message || !currentConversationId) return;

            const formData = new FormData();
            formData.append('action', 'send_admin_message');
            formData.append('conversation_id', currentConversationId);
            formData.append('message', message);

            fetch('chat.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageInput.value = '';
                    loadConversationMessages(currentConversationId);
                    refreshConversations();
                }
            })
            .catch(error => {
                console.error('Error sending message:', error);
            });
        }

        function insertQuickResponse(text) {
            document.getElementById('adminMessageInput').value = text;
        }

        function markConversationAsRead(conversationId) {
            fetch('chat_admin_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'mark_as_read',
                    conversation_id: conversationId
                })
            });
        }

        function refreshConversations() {
            location.reload();
        }

        function searchConversations(query) {
            const conversations = document.querySelectorAll('.conversation-item');

            conversations.forEach(conv => {
                const customerName = conv.querySelector('.customer-name').textContent.toLowerCase();
                if (customerName.includes(query.toLowerCase())) {
                    conv.style.display = 'block';
                } else {
                    conv.style.display = 'none';
                }
            });
        }

        // Auto refresh
        function startAutoRefresh() {
            if (document.getElementById('autoRefresh').checked) {
                autoRefreshInterval = setInterval(() => {
                    if (currentConversationId) {
                        loadConversationMessages(currentConversationId);
                    }
                }, 5000);
            }
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        }

        document.getElementById('autoRefresh').addEventListener('change', function() {
            if (this.checked) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });

        // Start auto refresh on page load
        document.addEventListener('DOMContentLoaded', function() {
            startAutoRefresh();
        });
    </script>
</body>
</html>
