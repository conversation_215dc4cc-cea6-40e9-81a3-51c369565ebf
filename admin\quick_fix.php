<?php
require_once '../config/database.php';

echo "<h2>🔧 Quick Fix - GT-SportDesign Database</h2>";

try {
    $db = getDbConnection();
    
    // 1. ตรวจสอบตาราง users
    echo "<h3>1. ตรวจสอบตาราง users:</h3>";
    
    try {
        $stmt = $db->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<p style='color: green;'>✅ ตาราง users มีอยู่</p>";
        echo "<strong>คอลัมน์ที่มี:</strong> ";
        $columnNames = array_column($columns, 'Field');
        echo implode(', ', $columnNames);
        echo "<br><br>";
        
        // ตรวจสอบว่ามี admin หรือไม่
        $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
        $adminCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($adminCount == 0) {
            echo "<p style='color: orange;'>⚠️ ไม่มี Admin user</p>";
            
            // สร้าง admin user
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            
            if (in_array('name', $columnNames)) {
                $sql = "INSERT INTO users (name, email, password, role, is_active) VALUES (?, ?, ?, 'admin', 1)";
                $params = ['GT-SportDesign Admin', '<EMAIL>', $password];
            } elseif (in_array('full_name', $columnNames)) {
                $sql = "INSERT INTO users (full_name, email, password, role, is_active) VALUES (?, ?, ?, 'admin', 1)";
                $params = ['GT-SportDesign Admin', '<EMAIL>', $password];
            } else {
                $sql = "INSERT INTO users (username, email, password, role, is_active) VALUES (?, ?, ?, 'admin', 1)";
                $params = ['admin', '<EMAIL>', $password];
            }
            
            try {
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                echo "<p style='color: green;'>✅ สร้าง Admin user เรียบร้อย</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ ไม่สามารถสร้าง Admin: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ มี Admin user แล้ว ($adminCount คน)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users: " . $e->getMessage() . "</p>";
        
        // สร้างตาราง users ใหม่
        echo "<h3>2. สร้างตาราง users ใหม่:</h3>";
        
        $createTable = "
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(255) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(255),
            phone VARCHAR(20),
            role ENUM('customer', 'admin') DEFAULT 'customer',
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        try {
            $db->exec($createTable);
            echo "<p style='color: green;'>✅ สร้างตาราง users เรียบร้อย</p>";
            
            // เพิ่ม admin
            $password = password_hash('admin123', PASSWORD_DEFAULT);
            $stmt = $db->prepare("INSERT INTO users (username, email, password, full_name, role, is_active) VALUES (?, ?, ?, ?, 'admin', 1)");
            $stmt->execute(['admin', '<EMAIL>', $password, 'GT-SportDesign Administrator']);
            echo "<p style='color: green;'>✅ เพิ่ม Admin user เรียบร้อย</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ ไม่สามารถสร้างตาราง: " . $e->getMessage() . "</p>";
        }
    }
    
    // 3. ตรวจสอบข้อมูล Admin
    echo "<h3>3. ข้อมูล Admin ปัจจุบัน:</h3>";
    
    try {
        $stmt = $db->query("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>✅ พบ Admin user:</strong><br>";
            foreach ($admin as $key => $value) {
                if ($key !== 'password') {
                    echo "<strong>$key:</strong> $value<br>";
                }
            }
            echo "</div>";
        } else {
            echo "<p style='color: red;'>❌ ไม่พบ Admin user</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ไม่สามารถตรวจสอบ Admin: " . $e->getMessage() . "</p>";
    }
    
    // 4. สร้างตารางอื่น ๆ ที่จำเป็น
    echo "<h3>4. ตรวจสอบตารางอื่น ๆ:</h3>";
    
    $requiredTables = [
        'payment_orders' => "
        CREATE TABLE IF NOT EXISTS payment_orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id VARCHAR(50) UNIQUE NOT NULL,
            customer_name VARCHAR(255) NOT NULL,
            customer_email VARCHAR(255) NOT NULL,
            total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
            payment_status ENUM('pending', 'paid', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        'contacts' => "
        CREATE TABLE IF NOT EXISTS contacts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255) NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            status ENUM('unread', 'read') DEFAULT 'unread',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        'quote_requests' => "
        CREATE TABLE IF NOT EXISTS quote_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            quote_number VARCHAR(50) UNIQUE NOT NULL,
            customer_name VARCHAR(255) NOT NULL,
            customer_email VARCHAR(255) NOT NULL,
            quantity INT NOT NULL DEFAULT 1,
            status ENUM('pending', 'sent') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )",
        
        'chat_sessions' => "
        CREATE TABLE IF NOT EXISTS chat_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            session_id VARCHAR(100) UNIQUE NOT NULL,
            customer_name VARCHAR(255) NOT NULL,
            customer_email VARCHAR(255) NOT NULL,
            status ENUM('active', 'closed') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )"
    ];
    
    foreach ($requiredTables as $tableName => $sql) {
        try {
            $db->exec($sql);
            echo "<p style='color: green;'>✅ ตาราง $tableName พร้อมใช้งาน</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ ตาราง $tableName: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดหลัก: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🚀 ทดสอบการเข้าสู่ระบบ:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<strong>ข้อมูล Login:</strong><br>";
echo "<strong>Email:</strong> <EMAIL><br>";
echo "<strong>Password:</strong> admin123";
echo "</div>";

echo "<a href='index.php' style='background: #eb4e17; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>เข้าสู่ระบบ Admin</a>";
echo "<a href='simple_login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Simple Login</a>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 900px;
    margin: 20px auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #333;
}

p {
    line-height: 1.6;
}
</style>
