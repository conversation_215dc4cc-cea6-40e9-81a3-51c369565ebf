<?php
/**
 * GT-SportDesign - Add Sample Data
 * เพิ่มข้อมูลตัวอย่างในระบบ
 */

require_once 'config/database.php';

echo "
<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>เพิ่มข้อมูลตัวอย่าง - GT-SportDesign</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Kanit', sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 50px auto; }
        .card { border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .log { background: #f8f9fa; border-radius: 10px; padding: 15px; margin: 10px 0; font-family: monospace; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header bg-success text-white'>
                <h3><i class='fas fa-plus'></i> เพิ่มข้อมูลตัวอย่าง GT-SportDesign</h3>
            </div>
            <div class='card-body'>";

try {
    echo "<div class='log info'>📊 เริ่มเพิ่มข้อมูลตัวอย่าง...</div>";
    
    // 1. เพิ่มหมวดหมู่สินค้า
    echo "<div class='log info'>📂 เพิ่มหมวดหมู่สินค้า...</div>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM product_categories");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            $pdo->exec("
                INSERT INTO product_categories (name, description, status, sort_order) VALUES
                ('เสื้อกีฬา', 'เสื้อกีฬาสำหรับทุกประเภทกีฬา', 'active', 1),
                ('กางเกงกีฬา', 'กางเกงกีฬาคุณภาพสูง', 'active', 2),
                ('ชุดทีม', 'ชุดทีมกีฬาครบเซต', 'active', 3),
                ('อุปกรณ์เสริม', 'อุปกรณ์เสริมสำหรับนักกีฬา', 'active', 4)
            ");
            echo "<div class='log success'>✅ เพิ่มหมวดหมู่สินค้า 4 รายการ</div>";
        } else {
            echo "<div class='log info'>ℹ️ มีหมวดหมู่สินค้าอยู่แล้ว $count รายการ</div>";
        }
    } catch (Exception $e) {
        echo "<div class='log error'>❌ หมวดหมู่สินค้า: " . $e->getMessage() . "</div>";
    }
    
    // 2. เพิ่มการตั้งค่าระบบ
    echo "<div class='log info'>⚙️ เพิ่มการตั้งค่าระบบ...</div>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM system_settings");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            $pdo->exec("
                INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
                ('site_name', 'GT-SportDesign', 'text', 'ชื่อเว็บไซต์'),
                ('site_description', 'ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง', 'text', 'คำอธิบายเว็บไซต์'),
                ('contact_email', '<EMAIL>', 'text', 'อีเมลติดต่อ'),
                ('contact_phone', '************', 'text', 'เบอร์โทรติดต่อ'),
                ('contact_line', '@gtsport', 'text', 'LINE ID'),
                ('facebook_page', 'https://www.facebook.com/GTSportDesign.1', 'text', 'Facebook Page'),
                ('min_order_amount', '500', 'number', 'ยอดขั้นต่ำในการสั่งซื้อ'),
                ('shipping_cost', '50', 'number', 'ค่าจัดส่งมาตรฐาน'),
                ('free_shipping_amount', '1000', 'number', 'ยอดสั่งซื้อที่ได้จัดส่งฟรี'),
                ('order_processing_days', '3-7', 'text', 'ระยะเวลาการผลิต'),
                ('promptpay_id', '0855599164', 'text', 'PromptPay ID'),
                ('primary_color', '#eb4e17', 'text', 'สีหลักของเว็บไซต์'),
                ('secondary_color', '#ff6b35', 'text', 'สีรองของเว็บไซต์')
            ");
            echo "<div class='log success'>✅ เพิ่มการตั้งค่าระบบ 13 รายการ</div>";
        } else {
            echo "<div class='log info'>ℹ️ มีการตั้งค่าระบบอยู่แล้ว $count รายการ</div>";
        }
    } catch (Exception $e) {
        echo "<div class='log error'>❌ การตั้งค่าระบบ: " . $e->getMessage() . "</div>";
    }
    
    // 3. เพิ่มสินค้าตัวอย่าง
    echo "<div class='log info'>📦 เพิ่มสินค้าตัวอย่าง...</div>";
    try {
        // ตรวจสอบว่ามีสินค้าอยู่แล้วหรือไม่
        $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE category_id IN (SELECT id FROM product_categories)");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            $pdo->exec("
                INSERT INTO products (category_id, name, description, price, status, created_at) VALUES
                (1, 'เสื้อกีฬา Syntex โปโล', 'เสื้อโปโลกีฬาคุณภาพสูง ผ้า Syntex ระบายอากาศดี', 210.00, 'active', NOW()),
                (1, 'เสื้อกีฬา Dri-FIT', 'เสื้อกีฬาเทคโนโลยี Dri-FIT ดูดซับเหงื่อ', 250.00, 'active', NOW()),
                (2, 'กางเกงกีฬาขาสั้น', 'กางเกงกีฬาขาสั้น ผ้าเบา ใส่สบาย', 180.00, 'active', NOW()),
                (3, 'ชุดทีมฟุตบอล', 'ชุดทีมฟุตบอลครบเซต เสื้อ+กางเกง+ถุงเท้า', 450.00, 'active', NOW()),
                (1, 'เสื้อกีฬาแขนยาว', 'เสื้อกีฬาแขนยาว สำหรับฤดูหนาว', 280.00, 'active', NOW()),
                (4, 'ถุงเท้ากีฬา', 'ถุงเท้ากีฬาคุณภาพสูง', 50.00, 'active', NOW())
            ");
            echo "<div class='log success'>✅ เพิ่มสินค้าตัวอย่าง 6 รายการ</div>";
        } else {
            echo "<div class='log info'>ℹ️ มีสินค้าอยู่แล้ว $count รายการ</div>";
        }
    } catch (Exception $e) {
        echo "<div class='log error'>❌ สินค้าตัวอย่าง: " . $e->getMessage() . "</div>";
    }
    
    // 4. เพิ่มจุดรับสินค้า
    echo "<div class='log info'>📍 เพิ่มจุดรับสินค้า...</div>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM pickup_locations");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            $pdo->exec("
                INSERT INTO pickup_locations (name, address, phone, operating_hours, is_active, sort_order) VALUES
                ('ร้าน GT-SportDesign สาขาหลัก', '339/7 ม.4 ต.บ้านดู่ อ.เมืองเชียงราย จ.เชียงราย', '************', 'จันทร์-เสาร์ 9:00-18:00', 1, 1),
                ('จุดนัดรับ เซ็นทรัลเชียงราย', 'ศูนย์การค้าเซ็นทรัลเชียงราย ชั้น 1 หน้าร้าน Starbucks', '************', 'ทุกวัน 10:00-21:00', 1, 2),
                ('จุดนัดรับ บิ๊กซีเชียงราย', 'ห้างบิ๊กซี เชียงราย ชั้น 1 หน้าประตูทางเข้า', '************', 'ทุกวัน 8:00-22:00', 1, 3)
            ");
            echo "<div class='log success'>✅ เพิ่มจุดรับสินค้า 3 รายการ</div>";
        } else {
            echo "<div class='log info'>ℹ️ มีจุดรับสินค้าอยู่แล้ว $count รายการ</div>";
        }
    } catch (Exception $e) {
        echo "<div class='log error'>❌ จุดรับสินค้า: " . $e->getMessage() . "</div>";
    }
    
    // 5. สร้างโฟลเดอร์ uploads
    echo "<div class='log info'>📁 สร้างโฟลเดอร์ uploads...</div>";
    $upload_dirs = [
        'uploads',
        'uploads/products',
        'uploads/gallery',
        'uploads/designs',
        'uploads/profiles',
        'uploads/temp'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
            echo "<div class='log success'>✅ สร้างโฟลเดอร์ $dir</div>";
        } else {
            echo "<div class='log info'>ℹ️ โฟลเดอร์ $dir มีอยู่แล้ว</div>";
        }
        
        // สร้าง .htaccess สำหรับความปลอดภัย
        $htaccess_file = $dir . '/.htaccess';
        if (!file_exists($htaccess_file)) {
            file_put_contents($htaccess_file, "Options -Indexes\n");
        }
    }
    
    // สร้าง index.php ในโฟลเดอร์ uploads
    $index_file = 'uploads/index.php';
    if (!file_exists($index_file)) {
        file_put_contents($index_file, "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n");
        echo "<div class='log success'>✅ สร้างไฟล์ป้องกัน uploads/index.php</div>";
    }
    
    echo "<div class='log success'>🎉 เพิ่มข้อมูลตัวอย่างเสร็จสิ้น!</div>";
    
} catch (Exception $e) {
    echo "<div class='log error'>💥 เกิดข้อผิดพลาด: " . $e->getMessage() . "</div>";
}

echo "
            </div>
            <div class='card-footer'>
                <div class='d-flex justify-content-between'>
                    <a href='index.php' class='btn btn-primary'>
                        <i class='fas fa-home'></i> กลับหน้าแรก
                    </a>
                    <a href='admin/dashboard.php' class='btn btn-success'>
                        <i class='fas fa-tachometer-alt'></i> เข้าสู่ระบบ Admin
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
