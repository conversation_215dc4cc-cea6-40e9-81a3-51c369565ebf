/* GT Sport Design - Main Stylesheet */

:root {
    --primary-color: #ee501b;
    --secondary-color: #ff6b35;
    --dark-color: #303136;
    --light-gray: #f1f0f0;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Kanit', sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
}

/* Utility Classes */
.text-primary { color: var(--primary-color) !important; }
.bg-primary { background-color: var(--primary-color) !important; }
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}
.btn-primary:hover {
    background-color: #d44615;
    border-color: #d44615;
}

/* Header Styles */
.top-header {
    background: var(--dark-color);
    color: white;
    padding: 8px 0;
    font-size: 14px;
}

.top-header a {
    color: white;
    text-decoration: none;
}

.main-navbar {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
}

.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: 700;
    font-size: 2rem;
}

.navbar-nav .nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.contact-icons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.contact-icons a {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.contact-icons a:hover {
    transform: scale(1.1);
}

.contact-icons .phone { background: #25d366; }
.contact-icons .line { background: #00c300; }
.contact-icons .facebook { background: #1877f2; }

/* Hero Section */
.hero-section {
    color: white;
    min-height: 600px;
    position: relative;
    overflow: hidden;
}

.hero-content {
    position: relative;
    z-index: 2;
    padding: 100px 0;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.btn-hero {
    background: white;
    color: var(--primary-color);
    border: none;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-hero:hover {
    background: var(--light-gray);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Features Section */
.features-section {
    padding: 50px 0;
    background: var(--primary-color);
    color: white;
}

.feature-card {
    text-align: center;
    padding: 40px 20px;
    border-radius: 15px;
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 2rem;
}

.feature-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: white;
}

/* Products Section */
.products-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.section-title {
    text-align: center;
    margin-bottom: 60px;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 15px;
}

.section-title .highlight {
    color: var(--primary-color);
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

/* Gallery Section */
.gallery-section {
    padding: 80px 0;
    background: white;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 40px;
}

.gallery-item {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    height: 285px;
    background: var(--light-gray);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(238, 80, 27, 0.8), rgba(255, 107, 53, 0.8));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

/* Reviews Section */
.reviews-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.review-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    height: 100%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.review-card:hover {
    transform: translateY(-5px);
}

.review-stars {
    color: #ffc107;
    margin-bottom: 15px;
}

.review-text {
    font-style: italic;
    color: #666;
    margin-bottom: 20px;
}

.review-author {
    display: flex;
    align-items: center;
}

.review-avatar {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-right: 15px;
}

.review-name {
    font-weight: 600;
    color: var(--dark-color);
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: white;
    padding: 50px 0 20px;
}

.footer h5 {
    color: var(--primary-color);
    margin-bottom: 20px;
}

.footer a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 20px;
    margin-top: 30px;
    text-align: center;
    color: #999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .contact-icons {
        margin-top: 10px;
    }
    
    .section-title h2 {
        font-size: 2rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}
