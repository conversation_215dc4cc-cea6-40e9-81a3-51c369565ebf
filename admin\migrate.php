<?php
/**
 * GT-SportDesign Database Migration System
 * ระบบอัปเกรดและย้ายฐานข้อมูล
 */

session_start();
require_once '../config/database.php';

// Simple authentication
$migrate_password = 'migrate123';
$is_authenticated = isset($_SESSION['migrate_auth']) && $_SESSION['migrate_auth'] === true;

if (isset($_POST['migrate_password'])) {
    if ($_POST['migrate_password'] === $migrate_password) {
        $_SESSION['migrate_auth'] = true;
        $is_authenticated = true;
    } else {
        $error = 'รหัสผ่านไม่ถูกต้อง';
    }
}

if (!$is_authenticated) {
    ?>
    <!DOCTYPE html>
    <html lang="th">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Database Migration - GT-SportDesign</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
            .auth-container { max-width: 400px; margin: 100px auto; background: white; border-radius: 15px; padding: 30px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
        </style>
    </head>
    <body>
        <div class="auth-container">
            <h3 class="text-center mb-4">🔄 Database Migration</h3>
            <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
            <?php endif; ?>
            <form method="post">
                <div class="mb-3">
                    <label class="form-label">รหัสผ่าน Migration</label>
                    <input type="password" class="form-control" name="migrate_password" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">เข้าสู่ระบบ</button>
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}

$action = $_GET['action'] ?? 'dashboard';

// Migration definitions
$migrations = [
    '001_create_admins_table' => [
        'description' => 'สร้างตาราง admins แยกจาก users',
        'sql' => "
            CREATE TABLE IF NOT EXISTS admins (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(255) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                name VARCHAR(255) NOT NULL,
                role ENUM('super_admin', 'admin', 'editor') DEFAULT 'admin',
                permissions JSON,
                is_active BOOLEAN DEFAULT TRUE,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_email (email),
                INDEX idx_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ],
    '002_add_product_variants' => [
        'description' => 'เพิ่มตาราง product_variants สำหรับไซส์และสี',
        'sql' => "
            CREATE TABLE IF NOT EXISTS product_variants (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_id INT NOT NULL,
                size VARCHAR(10),
                color VARCHAR(50),
                color_code VARCHAR(7),
                price_adjustment DECIMAL(10,2) DEFAULT 0,
                stock_quantity INT DEFAULT 0,
                sku VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                INDEX idx_product (product_id),
                INDEX idx_sku (sku)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ],
    '003_add_order_items' => [
        'description' => 'เพิ่มตาราง order_items สำหรับรายการสินค้าในคำสั่งซื้อ',
        'sql' => "
            CREATE TABLE IF NOT EXISTS order_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id VARCHAR(50) NOT NULL,
                product_id INT NOT NULL,
                variant_id INT,
                product_name VARCHAR(255) NOT NULL,
                quantity INT NOT NULL DEFAULT 1,
                unit_price DECIMAL(10,2) NOT NULL,
                total_price DECIMAL(10,2) NOT NULL,
                design_data JSON,
                custom_text TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE SET NULL,
                INDEX idx_order (order_id),
                INDEX idx_product (product_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ],
    '004_add_design_templates' => [
        'description' => 'เพิ่มตาราง design_templates สำหรับเทมเพลตการออกแบบ',
        'sql' => "
            CREATE TABLE IF NOT EXISTS design_templates (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                category VARCHAR(100),
                preview_image VARCHAR(500),
                template_data JSON NOT NULL,
                is_premium BOOLEAN DEFAULT FALSE,
                price DECIMAL(10,2) DEFAULT 0,
                downloads INT DEFAULT 0,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_category (category),
                INDEX idx_active (is_active),
                INDEX idx_premium (is_premium)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ],
    '005_add_user_designs' => [
        'description' => 'เพิ่มตาราง user_designs สำหรับเก็บการออกแบบของผู้ใช้',
        'sql' => "
            CREATE TABLE IF NOT EXISTS user_designs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                design_name VARCHAR(255) NOT NULL,
                product_id INT,
                design_data JSON NOT NULL,
                preview_image VARCHAR(500),
                is_public BOOLEAN DEFAULT FALSE,
                is_template BOOLEAN DEFAULT FALSE,
                views INT DEFAULT 0,
                likes INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL,
                INDEX idx_user (user_id),
                INDEX idx_product (product_id),
                INDEX idx_public (is_public)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ]
];

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - GT-SportDesign</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .migrate-header { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 20px 0; margin-bottom: 30px; }
        .migrate-card { background: white; border-radius: 10px; padding: 25px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .migration-item { border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 10px; }
        .migration-pending { border-left: 4px solid #ffc107; }
        .migration-completed { border-left: 4px solid #28a745; background-color: #f8fff9; }
        .migration-error { border-left: 4px solid #dc3545; background-color: #fff8f8; }
        .btn-migrate { background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); border: none; color: white; }
        .btn-migrate:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(44, 62, 80, 0.3); color: white; }
    </style>
</head>
<body>
    <div class="migrate-header">
        <div class="container">
            <h1><i class="fas fa-database me-2"></i>GT-SportDesign Database Migration</h1>
            <p class="mb-0">ระบบอัปเกรดและจัดการฐานข้อมูล</p>
        </div>
    </div>

    <div class="container">
        <?php if ($action == 'dashboard'): ?>
        <!-- Migration Dashboard -->
        <div class="migrate-card">
            <h4>📊 Migration Status</h4>
            
            <?php
            try {
                $db = getDbConnection();
                
                // Create migrations table if not exists
                $db->exec("
                    CREATE TABLE IF NOT EXISTS migrations (
                        id VARCHAR(255) PRIMARY KEY,
                        description TEXT,
                        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        success BOOLEAN DEFAULT TRUE,
                        error_message TEXT
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                
                // Get executed migrations
                $stmt = $db->query("SELECT * FROM migrations ORDER BY executed_at DESC");
                $executed_migrations = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $executed_ids = array_column($executed_migrations, 'id');
                
                echo "<div class='alert alert-success'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ เชื่อมต่อฐานข้อมูลไม่ได้: " . $e->getMessage() . "</div>";
                $executed_ids = [];
            }
            ?>
            
            <h5>🔄 Available Migrations</h5>
            
            <?php foreach ($migrations as $migration_id => $migration): ?>
            <div class="migration-item <?php echo in_array($migration_id, $executed_ids) ? 'migration-completed' : 'migration-pending'; ?>">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">
                            <?php if (in_array($migration_id, $executed_ids)): ?>
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <?php else: ?>
                            <i class="fas fa-clock text-warning me-2"></i>
                            <?php endif; ?>
                            <?php echo $migration_id; ?>
                        </h6>
                        <p class="mb-0 text-muted"><?php echo $migration['description']; ?></p>
                        
                        <?php if (in_array($migration_id, $executed_ids)): ?>
                        <?php
                        $executed_info = array_filter($executed_migrations, function($m) use ($migration_id) {
                            return $m['id'] === $migration_id;
                        });
                        $executed_info = reset($executed_info);
                        ?>
                        <small class="text-success">
                            ✅ Executed: <?php echo date('d/m/Y H:i:s', strtotime($executed_info['executed_at'])); ?>
                        </small>
                        <?php endif; ?>
                    </div>
                    
                    <div>
                        <?php if (!in_array($migration_id, $executed_ids)): ?>
                        <a href="?action=run&migration=<?php echo $migration_id; ?>" 
                           class="btn btn-sm btn-migrate"
                           onclick="return confirm('คุณแน่ใจหรือไม่ว่าต้องการรัน migration นี้?')">
                            <i class="fas fa-play me-1"></i>Run
                        </a>
                        <?php else: ?>
                        <span class="badge bg-success">Completed</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            
            <div class="mt-4">
                <a href="?action=run_all" class="btn btn-migrate me-2"
                   onclick="return confirm('คุณแน่ใจหรือไม่ว่าต้องการรัน migration ทั้งหมด?')">
                    <i class="fas fa-play-circle me-2"></i>Run All Pending
                </a>
                <a href="?action=reset" class="btn btn-danger"
                   onclick="return confirm('คุณแน่ใจหรือไม่ว่าต้องการรีเซ็ต migration ทั้งหมด? การกระทำนี้จะลบข้อมูล migration ทั้งหมด!')">
                    <i class="fas fa-redo me-2"></i>Reset All
                </a>
            </div>
        </div>

        <?php elseif ($action == 'run' && isset($_GET['migration'])): ?>
        <!-- Run Single Migration -->
        <?php
        $migration_id = $_GET['migration'];
        if (isset($migrations[$migration_id])) {
            $migration = $migrations[$migration_id];
            
            echo "<div class='migrate-card'>";
            echo "<h4>🔄 Running Migration: $migration_id</h4>";
            echo "<p>{$migration['description']}</p>";
            
            try {
                $db = getDbConnection();
                
                // Check if already executed
                $stmt = $db->prepare("SELECT * FROM migrations WHERE id = ?");
                $stmt->execute([$migration_id]);
                if ($stmt->fetch()) {
                    echo "<div class='alert alert-warning'>⚠️ Migration already executed</div>";
                } else {
                    // Execute migration
                    $db->exec($migration['sql']);
                    
                    // Record migration
                    $stmt = $db->prepare("INSERT INTO migrations (id, description, executed_at, success) VALUES (?, ?, NOW(), TRUE)");
                    $stmt->execute([$migration_id, $migration['description']]);
                    
                    echo "<div class='alert alert-success'>✅ Migration executed successfully</div>";
                }
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Migration failed: " . $e->getMessage() . "</div>";
                
                // Record error
                try {
                    $stmt = $db->prepare("INSERT INTO migrations (id, description, executed_at, success, error_message) VALUES (?, ?, NOW(), FALSE, ?)");
                    $stmt->execute([$migration_id, $migration['description'], $e->getMessage()]);
                } catch (Exception $e2) {
                    // Ignore error recording failure
                }
            }
            
            echo "<a href='?action=dashboard' class='btn btn-secondary'>← Back to Dashboard</a>";
            echo "</div>";
        }
        ?>

        <?php elseif ($action == 'run_all'): ?>
        <!-- Run All Migrations -->
        <div class="migrate-card">
            <h4>🔄 Running All Pending Migrations</h4>
            
            <?php
            try {
                $db = getDbConnection();
                
                // Get executed migrations
                $stmt = $db->query("SELECT id FROM migrations WHERE success = TRUE");
                $executed_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $success_count = 0;
                $error_count = 0;
                
                foreach ($migrations as $migration_id => $migration) {
                    if (!in_array($migration_id, $executed_ids)) {
                        echo "<div class='migration-item'>";
                        echo "<h6>Running: $migration_id</h6>";
                        echo "<p>{$migration['description']}</p>";
                        
                        try {
                            $db->exec($migration['sql']);
                            
                            // Record migration
                            $stmt = $db->prepare("INSERT INTO migrations (id, description, executed_at, success) VALUES (?, ?, NOW(), TRUE)");
                            $stmt->execute([$migration_id, $migration['description']]);
                            
                            echo "<div class='alert alert-success'>✅ Success</div>";
                            $success_count++;
                            
                        } catch (Exception $e) {
                            echo "<div class='alert alert-danger'>❌ Error: " . $e->getMessage() . "</div>";
                            $error_count++;
                            
                            // Record error
                            try {
                                $stmt = $db->prepare("INSERT INTO migrations (id, description, executed_at, success, error_message) VALUES (?, ?, NOW(), FALSE, ?)");
                                $stmt->execute([$migration_id, $migration['description'], $e->getMessage()]);
                            } catch (Exception $e2) {
                                // Ignore error recording failure
                            }
                        }
                        
                        echo "</div>";
                    }
                }
                
                echo "<div class='alert alert-info'>";
                echo "<h6>Migration Summary:</h6>";
                echo "<p>✅ Successful: $success_count</p>";
                echo "<p>❌ Failed: $error_count</p>";
                echo "</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Database error: " . $e->getMessage() . "</div>";
            }
            ?>
            
            <a href="?action=dashboard" class="btn btn-secondary">← Back to Dashboard</a>
        </div>

        <?php elseif ($action == 'reset'): ?>
        <!-- Reset Migrations -->
        <div class="migrate-card">
            <h4>🔄 Resetting Migrations</h4>
            
            <?php
            try {
                $db = getDbConnection();
                $db->exec("DELETE FROM migrations");
                echo "<div class='alert alert-success'>✅ All migration records have been reset</div>";
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>❌ Reset failed: " . $e->getMessage() . "</div>";
            }
            ?>
            
            <a href="?action=dashboard" class="btn btn-secondary">← Back to Dashboard</a>
        </div>
        <?php endif; ?>
        
        <!-- Quick Links -->
        <div class="migrate-card">
            <h5>🔗 Quick Links</h5>
            <div class="row">
                <div class="col-md-3">
                    <a href="../admin/login.php" class="btn btn-outline-primary w-100">
                        <i class="fas fa-user-shield me-2"></i>Admin Panel
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../admin/setup_database.php" class="btn btn-outline-success w-100">
                        <i class="fas fa-database me-2"></i>Setup Database
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../deploy.php" class="btn btn-outline-info w-100">
                        <i class="fas fa-rocket me-2"></i>Deploy Dashboard
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="../index.php" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-home me-2"></i>Homepage
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
