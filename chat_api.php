<?php
header('Content-Type: application/json');
require_once './config/database.php';

try {
    $pdo = getDbConnection();

    // ข้อมูล
    $method = $_SERVER['REQUEST_METHOD'];

    if ($method === 'POST') {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        $action = $data['action'] ?? '';
    } else {
        $action = $_GET['action'] ?? '';
    }

    switch ($action) {
        case 'start_conversation':
            startConversation($pdo);
            break;

        case 'send_message':
            sendMessage($pdo, $data);
            break;

        case 'get_messages':
            getMessages($pdo);
            break;

        case 'check_new_messages':
            checkNewMessages($pdo);
            break;

        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function startConversation($pdo) {
    // สร้าง conversation ใหม่
    $stmt = $pdo->prepare("
        INSERT INTO chat_conversations (customer_id, status, created_at, updated_at)
        VALUES (?, 'active', NOW(), NOW())
    ");

    $customer_id = null; // ไม่ ระบบ login ค้า
    $stmt->execute([$customer_id]);

    $conversation_id = $pdo->lastInsertId();

    // ส่งข้อความต้อนรับจาก bot
    $welcome_message = "สสส! ต้อนรับ GT Sport Design 👕\n\nอะไรให้ช่วยไหม?\n• สอบถามราคา\n• ผลงาน\n• สอบถามส่ง";

    $stmt = $pdo->prepare("
        INSERT INTO chat_messages (conversation_id, sender_type, sender_id, message, created_at)
        VALUES (?, 'admin', NULL, ?, NOW())
    ");
    $stmt->execute([$conversation_id, $welcome_message]);

    echo json_encode([
        'success' => true,
        'conversation_id' => $conversation_id,
        'message' => 'Conversation started successfully'
    ]);
}

function sendMessage($pdo, $data) {
    $conversation_id = (int)($data['conversation_id'] ?? 0);
    $message = trim($data['message'] ?? '');

    if (!$conversation_id || !$message) {
        throw new Exception('Missing required data');
    }

    // ข้อความของค้า
    $stmt = $pdo->prepare("
        INSERT INTO chat_messages (conversation_id, sender_type, sender_id, message, created_at)
        VALUES (?, 'customer', NULL, ?, NOW())
    ");
    $stmt->execute([$conversation_id, $message]);

    // เดตเวลาใน conversation
    $stmt = $pdo->prepare("
        UPDATE chat_conversations
        SET updated_at = NOW(), has_new_messages = 1
        WHERE id = ?
    ");
    $stmt->execute([$conversation_id]);

    // ส่งข้อความตอบกลับ (bot response)
    $botResponse = generateBotResponse($message);
    if ($botResponse) {
        // รอ 1-2 วินาทีเพื่อให้เป็น
        sleep(1);

        $stmt = $pdo->prepare("
            INSERT INTO chat_messages (conversation_id, sender_type, sender_id, message, created_at)
            VALUES (?, 'admin', NULL, ?, NOW())
        ");
        $stmt->execute([$conversation_id, $botResponse]);
    }

    echo json_encode([
        'success' => true,
        'message' => 'Message sent successfully'
    ]);
}

function getMessages($pdo) {
    $conversation_id = (int)($_GET['conversation_id'] ?? 0);
    $since = (float)($_GET['since'] ?? 0);

    if (!$conversation_id) {
        throw new Exception('Missing conversation ID');
    }

    $stmt = $pdo->prepare("
        SELECT * FROM chat_messages
        WHERE conversation_id = ? AND UNIX_TIMESTAMP(created_at) > ?
        ORDER BY created_at ASC
    ");
    $stmt->execute([$conversation_id, $since]);
    $messages = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'messages' => $messages
    ]);
}

function checkNewMessages($pdo) {
    $conversation_id = (int)($_GET['conversation_id'] ?? 0);
    $since = (float)($_GET['since'] ?? 0);

    if (!$conversation_id) {
        throw new Exception('Missing conversation ID');
    }

    $stmt = $pdo->prepare("
        SELECT COUNT(*) as new_count FROM chat_messages
        WHERE conversation_id = ? AND sender_type = 'admin' AND UNIX_TIMESTAMP(created_at) > ?
    ");
    $stmt->execute([$conversation_id, $since]);
    $result = $stmt->fetch();

    $new_count = (int)$result['new_count'];

    echo json_encode([
        'success' => true,
        'has_new_messages' => $new_count > 0,
        'new_count' => $new_count
    ]);
}

function generateBotResponse($message) {
    $message = strtolower($message);

    // คำตอบฐาน
    $responses = [
        'ราคา' => "ราคาค้า:\n🔹 เอื้อเยื้อ เอื้อมต้น 180 บาท\n🔹 เอื้อโปโล เอื้อมต้น 220 บาท\n🔹 แจ็คเก็ต เอื้อมต้น 350 บาท\n\nราคาอยู่จำนวนและการออกแบบ ต้องการใบเสนอราคาไหม?",

        'ผลงาน' => "ผลงานของเราได้:\n📱 Facebook: GT Sport Design\n📸 Instagram: @gt_sport_design\n🌐 เว็บไซต์: หน้าแรกของเรา\n\nต้องการให้ส่งผลงานเฉพาะด้านใดไหม?",

        'ส่ง' => "ส่งของเรา:\n🚚 ส่งว่ประเทศ\n📦 EMS/Kerry Express\n💰 ค่าส่ง 50 บาท (เมื่อส่ง 1,000 บาท ขึ้น)\n⏰ ระยะเวลา 2-3 ทำการ\n\nอยู่แล้วไหม?",

        'ส่งซื้อ' => "ส่งซื้อ:\n1️⃣ เลือกค้าและออกแบบ\n2️⃣ จำนวนและขนาด\n3️⃣ คำส่งซื้อ\n4️⃣ ชำระ\n5️⃣ รอค้า\n\nต้องการเริ่มออกแบบเลยไหม?",

        'ต่อ' => "ช่องทางต่อเรา:\n📞 โทร: 085-559-9164\n📱 LINE: @gtsport\n📧 Email: <EMAIL>\n🏪 ร้าน: มหานคร\n\nต้องการข้อมูลอะไรไหม?",

        'เวลา' => "เวลาทำการของเรา:\n🕐 จันทร์-ศุกร์: 09:00-18:00\n🕐 เสาร์: 09:00-17:00\n🕐 อาทิตย์:\n\nนอกเวลาทำการสามารถฝากข้อความไว้ได้  เราจะตอบโดยเร็ว! "
    ];

    // ตรวจสอบคำ
    foreach ($responses as $keyword => $response) {
        if (strpos($message, $keyword) !== false) {
            return $response;
        }
    }

    // คำตอบไป
    $general_responses = [
        "ขอบคุณข้อความ! 😊 จะตอบในไม่ช้า ต้องการสอบถามอะไรไหม?",
        "ได้ข้อความแล้ว  ตรวจสอบข้อมูลให้ ณารอ! ",
        "สสส ขอบคุณ GT Sport Design 👕 อะไรให้ช่วยไหม?"
    ];

    return $general_responses[array_rand($general_responses)];
}
?>
