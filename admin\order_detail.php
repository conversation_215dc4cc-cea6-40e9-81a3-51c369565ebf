<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id'])) {
    http_response_code(403);
    exit('Unauthorized');
}

$order_id = (int)($_GET['id'] ?? 0);

if (!$order_id) {
    exit('Invalid order ID');
}

$pdo = getDbConnection();

try {
    // ดึงข้อมูลคำสั่งซื้อ
    $stmt = $pdo->prepare("
        SELECT o.*, c.name as customer_name, c.email as customer_email,
               c.phone as customer_phone, c.address as customer_address
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();

    if (!$order) {
        exit('Order not found');
    }

    // ดึงรายการสินค้าในคำสั่งซื้อ
    $stmt = $pdo->prepare("
        SELECT oi.*, p.name as product_name, p.code as product_code,
               pi.file_path as product_image
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.image_type = 'main'
        WHERE oi.order_id = ?
    ");
    $stmt->execute([$order_id]);
    $order_items = $stmt->fetchAll();

    // ดึงประวัติการอัพเดตสถานะ
    $stmt = $pdo->prepare("
        SELECT * FROM order_history
        WHERE order_id = ?
        ORDER BY created_at DESC
    ");
    $stmt->execute([$order_id]);
    $order_history = $stmt->fetchAll();

} catch (Exception $e) {
    exit('Error loading order details: ' . $e->getMessage());
}

// สถานะแปลภาษาไทย
$status_text = [
    'pending' => 'รอดำเนินการ',
    'processing' => 'กำลังดำเนินการ',
    'completed' => 'เสร็จสิ้น',
    'cancelled' => 'ยกเลิก'
];

$payment_status_text = [
    'pending' => 'รอชำระ',
    'paid' => 'ชำระแล้ว',
    'failed' => 'ชำระไม่สำเร็จ'
];
?>

<div class="order-detail">
    <!-- ข้อมูลคำสั่งซื้อ -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h6 class="text-muted">ข้อมูลคำสั่งซื้อ</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>เลขที่คำสั่งซื้อ:</strong></td>
                    <td><?= htmlspecialchars($order['order_number']) ?></td>
                </tr>
                <tr>
                    <td><strong>วันที่สั่งซื้อ:</strong></td>
                    <td><?= date('d/m/Y H:i:s', strtotime($order['created_at'])) ?></td>
                </tr>
                <tr>
                    <td><strong>สถานะ:</strong></td>
                    <td>
                        <span class="badge bg-<?= $order['status'] === 'completed' ? 'success' : ($order['status'] === 'cancelled' ? 'danger' : 'warning') ?>">
                            <?= $status_text[$order['status']] ?? $order['status'] ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>สถานะการชำระ:</strong></td>
                    <td>
                        <span class="badge bg-<?= $order['payment_status'] === 'paid' ? 'success' : 'warning' ?>">
                            <?= $payment_status_text[$order['payment_status']] ?? $order['payment_status'] ?>
                        </span>
                    </td>
                </tr>
                <tr>
                    <td><strong>ยอดรวม:</strong></td>
                    <td><strong class="text-primary">฿<?= number_format($order['total_amount']) ?></strong></td>
                </tr>
            </table>
        </div>
        <div class="col-md-6">
            <h6 class="text-muted">ข้อมูลลูกค้า</h6>
            <table class="table table-sm">
                <tr>
                    <td><strong>ชื่อ:</strong></td>
                    <td><?= htmlspecialchars($order['customer_name'] ?: 'ไม่ระบุ') ?></td>
                </tr>
                <tr>
                    <td><strong>อีเมล:</strong></td>
                    <td><?= htmlspecialchars($order['customer_email'] ?: 'ไม่ระบุ') ?></td>
                </tr>
                <tr>
                    <td><strong>เบอร์โทร:</strong></td>
                    <td><?= htmlspecialchars($order['customer_phone'] ?: 'ไม่ระบุ') ?></td>
                </tr>
                <tr>
                    <td><strong>ที่อยู่:</strong></td>
                    <td><?= htmlspecialchars($order['customer_address'] ?: 'ไม่ระบุ') ?></td>
                </tr>
                <tr>
                    <td><strong>ที่อยู่จัดส่ง:</strong></td>
                    <td><?= htmlspecialchars($order['shipping_address'] ?: 'ไม่ระบุ') ?></td>
                </tr>
            </table>
        </div>
    </div>

    <!-- รายการสินค้า -->
    <div class="mb-4">
        <h6 class="text-muted">รายการสินค้า</h6>
        <div class="table-responsive">
            <table class="table table-sm table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>รูปภาพ</th>
                        <th>สินค้า</th>
                        <th>รหัส</th>
                        <th>ราคา</th>
                        <th>จำนวน</th>
                        <th>รวม</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($order_items as $item): ?>
                    <tr>
                        <td>
                            <?php if ($item['product_image']): ?>
                                <img src="<?= htmlspecialchars($item['product_image']) ?>"
                                     alt="Product" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <strong><?= htmlspecialchars($item['product_name'] ?: 'ไม่ระบุ') ?></strong>
                            <?php if ($item['product_options']): ?>
                                <br><small class="text-muted"><?= htmlspecialchars($item['product_options']) ?></small>
                            <?php endif; ?>
                        </td>
                        <td><?= htmlspecialchars($item['product_code'] ?: '') ?></td>
                        <td>฿<?= number_format($item['unit_price']) ?></td>
                        <td><?= number_format($item['quantity']) ?></td>
                        <td><strong>฿<?= number_format($item['total_price']) ?></strong></td>
                    </tr>
                    <?php endforeach; ?>
                    <tr class="table-light">
                        <td colspan="5" class="text-end"><strong>ยอดรวมทั้งหมด:</strong></td>
                        <td><strong class="text-primary fs-5">฿<?= number_format($order['total_amount']) ?></strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- หมายเหตุจากแอดมิน -->
    <?php if ($order['admin_note']): ?>
    <div class="mb-4">
        <h6 class="text-muted">หมายเหตุจากแอดมิน</h6>
        <div class="alert alert-info">
            <?= nl2br(htmlspecialchars($order['admin_note'])) ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- หมายเหตุจากลูกค้า -->
    <?php if ($order['customer_note']): ?>
    <div class="mb-4">
        <h6 class="text-muted">หมายเหตุจากลูกค้า</h6>
        <div class="alert alert-light">
            <?= nl2br(htmlspecialchars($order['customer_note'])) ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- ประวัติการดำเนินการ -->
    <?php if ($order_history): ?>
    <div class="mb-4">
        <h6 class="text-muted">ประวัติการดำเนินการ</h6>
        <div class="timeline">
            <?php foreach ($order_history as $history): ?>
            <div class="timeline-item mb-3">
                <div class="card">
                    <div class="card-body py-2">
                        <div class="d-flex justify-content-between">
                            <span><?= htmlspecialchars($history['description']) ?></span>
                            <small class="text-muted"><?= date('d/m/Y H:i', strtotime($history['created_at'])) ?></small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- ข้อมูลการชำระเงิน -->
    <div class="mb-4">
        <h6 class="text-muted">ข้อมูลการชำระเงิน</h6>
        <table class="table table-sm">
            <tr>
                <td><strong>วิธีการชำระ:</strong></td>
                <td><?= htmlspecialchars($order['payment_method'] ?: 'ไม่ระบุ') ?></td>
            </tr>
            <tr>
                <td><strong>สถานะการชำระ:</strong></td>
                <td>
                    <span class="badge bg-<?= $order['payment_status'] === 'paid' ? 'success' : 'warning' ?>">
                        <?= $payment_status_text[$order['payment_status']] ?? $order['payment_status'] ?>
                    </span>
                </td>
            </tr>
            <?php if ($order['payment_date']): ?>
            <tr>
                <td><strong>วันที่ชำระ:</strong></td>
                <td><?= date('d/m/Y H:i:s', strtotime($order['payment_date'])) ?></td>
            </tr>
            <?php endif; ?>
            <?php if ($order['transaction_id']): ?>
            <tr>
                <td><strong>รหัสอ้างอิง:</strong></td>
                <td><?= htmlspecialchars($order['transaction_id']) ?></td>
            </tr>
            <?php endif; ?>
        </table>
    </div>

    <!-- การดำเนินการ -->
    <div class="d-flex gap-2 justify-content-end">
        <button class="btn btn-warning btn-sm" onclick="updateStatus(<?= $order['id'] ?>, '<?= $order['status'] ?>')">
            <i class="fas fa-edit"></i> อัพเดตสถานะ
        </button>
        <button class="btn btn-info btn-sm" onclick="addNote(<?= $order['id'] ?>)">
            <i class="fas fa-sticky-note"></i> เพิ่มหมายเหตุ
        </button>
        <button class="btn btn-success btn-sm" onclick="printOrder(<?= $order['id'] ?>)">
            <i class="fas fa-print"></i> พิมพ์ใบสั่งซื้อ
        </button>
        <button class="btn btn-primary btn-sm" onclick="sendEmail(<?= $order['id'] ?>)">
            <i class="fas fa-envelope"></i> ส่งอีเมลแจ้งลูกค้า
        </button>
    </div>
</div>

<style>
.order-detail .table td {
    padding: 0.5rem;
    border: none;
}
.order-detail .table-bordered td,
.order-detail .table-bordered th {
    border: 1px solid #dee2e6;
}
.timeline-item {
    position: relative;
}
.timeline-item::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    width: 8px;
    height: 8px;
    background: #007bff;
    border-radius: 50%;
    transform: translateY(-50%);
}
</style>

<script>
function printOrder(orderId) {
    window.open(`print_order.php?id=${orderId}`, '_blank');
}

function sendEmail(orderId) {
    if (confirm('ต้องการส่งอีเมลแจ้งสถานะให้ลูกค้าหรือไม่?')) {
        fetch(`send_order_email.php?id=${orderId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('ส่งอีเมลเรียบร้อยแล้ว');
                } else {
                    alert('เกิดข้อผิดพลาด: ' + data.message);
                }
            })
            .catch(error => {
                alert('เกิดข้อผิดพลาดในการส่งอีเมล');
            });
    }
}
</script>
