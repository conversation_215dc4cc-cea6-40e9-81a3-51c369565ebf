<?php
// แสดงข้อ<|im_start|>พลาด<|im_start|>้งหมดเพื่อการทดสอบ
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// ข้อมูลการเชื่อมต่อ
$host = 'localhost';
$dbname = 'gtsportd_web2';
$username = 'gtsportd_public';
$password = 'Zgw5D2WnhZBruXWpr7Fg';

try {
    // ทดสอบการเชื่อมต่อ
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2 style='color:green'>การเชื่อมต่อสำเร็จ!</h2>";
    
    // แสดงรายการตาราง
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h3>รายการตารางในฐานข้อมูล:</h3>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<h2 style='color:red'>การเชื่อมต่อล้มเหลว!</h2>";
    echo "<p>ข้อ<|im_start|>พลาด: " . $e->getMessage() . "</p>";
}
?>
