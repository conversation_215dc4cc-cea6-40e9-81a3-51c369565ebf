<?php
session_start();

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

$page_title = 'คู่มือการใช้งานระบบ';
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title><?php echo $page_title; ?> - GT Sport Design Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #ee501b;
            --secondary-color: #ff6b35;
            --dark-color: #303136;
            --light-gray: #f8f9fa;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            background: var(--light-gray);
            font-family: 'Kanit', sans-serif;
        }
        
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        
        .guide-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .guide-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .guide-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .section-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li i {
            color: var(--primary-color);
            margin-right: 15px;
            width: 20px;
            text-align: center;
        }
        
        .menu-structure {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .menu-item {
            padding: 8px 0;
            border-left: 3px solid var(--primary-color);
            padding-left: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .submenu-item {
            margin-left: 20px;
            border-left-color: var(--info-color);
            font-size: 0.9rem;
        }
        
        .quick-access {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .quick-btn {
            background: white;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
            display: block;
        }
        
        .quick-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
            text-decoration: none;
        }
        
        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-active { background: #d1edff; color: #0c63e4; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-success { background: #d4edda; color: #155724; }
        
        .workflow-step {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid var(--primary-color);
            position: relative;
        }
        
        .step-number {
            position: absolute;
            top: -10px;
            left: -10px;
            width: 30px;
            height: 30px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .back-to-admin {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: var(--primary-color);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-to-admin:hover {
            background: var(--secondary-color);
            color: white;
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            .admin-header {
                padding: 40px 0;
            }
            
            .guide-card {
                padding: 20px;
            }
            
            .quick-access {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <div class="admin-header">
        <div class="container text-center">
            <h1 class="display-4 fw-bold mb-3" data-aos="fade-up">
                <i class="fas fa-book me-3"></i>คู่มือการใช้งานระบบ
            </h1>
            <p class="lead mb-4" data-aos="fade-up" data-aos-delay="200">
                GT Sport Design - ระบบจัดการหลังบ้าน<br>
                คู่มือครบครันสำหรับผู้ดูแลระบบ
            </p>
            <div class="d-flex justify-content-center gap-3" data-aos="fade-up" data-aos-delay="400">
                <span class="status-badge status-success">
                    <i class="fas fa-check me-1"></i>ระบบพร้อมใช้งาน
                </span>
                <span class="status-badge status-active">
                    <i class="fas fa-user me-1"></i>ผู้ใช้: <?php echo $_SESSION['admin_name'] ?? 'Admin'; ?>
                </span>
            </div>
        </div>
    </div>

    <div class="guide-container">
        <!-- Quick Access -->
        <div class="guide-card" data-aos="fade-up">
            <h3 class="text-center mb-4">
                <i class="fas fa-rocket me-2 text-primary"></i>เข้าถึงด่วน
            </h3>
            <div class="quick-access">
                <a href="dashboard.php" class="quick-btn">
                    <i class="fas fa-tachometer-alt fa-2x mb-3"></i>
                    <h6>แดชบอร์ด</h6>
                    <small>ภาพรวมระบบ</small>
                </a>
                <a href="products.php" class="quick-btn">
                    <i class="fas fa-tshirt fa-2x mb-3"></i>
                    <h6>จัดการสินค้า</h6>
                    <small>เพิ่ม แก้ไข ลบสินค้า</small>
                </a>
                <a href="orders.php" class="quick-btn">
                    <i class="fas fa-shopping-cart fa-2x mb-3"></i>
                    <h6>คำสั่งซื้อ</h6>
                    <small>จัดการออเดอร์</small>
                </a>
                <a href="customers.php" class="quick-btn">
                    <i class="fas fa-users fa-2x mb-3"></i>
                    <h6>ลูกค้า</h6>
                    <small>ข้อมูลลูกค้า</small>
                </a>
                <a href="designs.php" class="quick-btn">
                    <i class="fas fa-palette fa-2x mb-3"></i>
                    <h6>การออกแบบ</h6>
                    <small>จัดการดีไซน์</small>
                </a>
                <a href="reports.php" class="quick-btn">
                    <i class="fas fa-chart-bar fa-2x mb-3"></i>
                    <h6>รายงาน</h6>
                    <small>สถิติและรายงาน</small>
                </a>
            </div>
        </div>

        <!-- System Overview -->
        <div class="row">
            <div class="col-lg-6">
                <div class="guide-card" data-aos="fade-right">
                    <div class="section-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h4 class="text-center mb-4">ภาพรวมระบบ</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i>ระบบจัดการสินค้าครบครัน</li>
                        <li><i class="fas fa-check"></i>ระบบคำสั่งซื้อแบบเรียลไทม์</li>
                        <li><i class="fas fa-check"></i>ระบบออกแบบเสื้อออนไลน์</li>
                        <li><i class="fas fa-check"></i>ระบบจัดการลูกค้า</li>
                        <li><i class="fas fa-check"></i>ระบบรายงานและสถิติ</li>
                        <li><i class="fas fa-check"></i>ระบบแชทและการสื่อสาร</li>
                        <li><i class="fas fa-check"></i>ระบบการจองและนัดหมาย</li>
                        <li><i class="fas fa-check"></i>ระบบจัดการคอนเทนต์</li>
                    </ul>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="guide-card" data-aos="fade-left">
                    <div class="section-icon">
                        <i class="fas fa-sitemap"></i>
                    </div>
                    <h4 class="text-center mb-4">โครงสร้างเมนู</h4>
                    <div class="menu-structure">
                        <div class="menu-item">
                            <i class="fas fa-tachometer-alt me-2"></i><strong>แดชบอร์ด</strong>
                        </div>
                        <div class="menu-item">
                            <i class="fas fa-tshirt me-2"></i><strong>จัดการสินค้า</strong>
                            <div class="submenu-item">• รายการสินค้า</div>
                            <div class="submenu-item">• เพิ่มสินค้าใหม่</div>
                            <div class="submenu-item">• หมวดหมู่สินค้า</div>
                            <div class="submenu-item">• จัดการสต็อก</div>
                        </div>
                        <div class="menu-item">
                            <i class="fas fa-shopping-cart me-2"></i><strong>คำสั่งซื้อ</strong>
                            <div class="submenu-item">• รายการคำสั่งซื้อ</div>
                            <div class="submenu-item">• สร้างคำสั่งซื้อ</div>
                            <div class="submenu-item">• รอดำเนินการ</div>
                            <div class="submenu-item">• กำลังผลิต</div>
                            <div class="submenu-item">• เสร็จสิ้น</div>
                        </div>
                        <div class="menu-item">
                            <i class="fas fa-users me-2"></i><strong>ลูกค้า</strong>
                            <div class="submenu-item">• จัดการลูกค้า</div>
                            <div class="submenu-item">• เพิ่มลูกค้าใหม่</div>
                        </div>
                        <div class="menu-item">
                            <i class="fas fa-palette me-2"></i><strong>การออกแบบ</strong>
                            <div class="submenu-item">• จัดการการออกแบบ</div>
                            <div class="submenu-item">• เทมเพลต</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Workflow Guide -->
        <div class="guide-card" data-aos="fade-up">
            <h3 class="text-center mb-4">
                <i class="fas fa-tasks me-2 text-primary"></i>ขั้นตอนการทำงาน
            </h3>

            <div class="row">
                <div class="col-lg-6">
                    <h5 class="mb-3">📦 การจัดการคำสั่งซื้อ</h5>
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <h6>รับคำสั่งซื้อ</h6>
                        <p class="mb-0">ตรวจสอบคำสั่งซื้อใหม่ในหน้าแดชบอร์ด</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <h6>ยืนยันคำสั่งซื้อ</h6>
                        <p class="mb-0">ตรวจสอบรายละเอียดและยืนยันการรับออเดอร์</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <h6>เริ่มผลิต</h6>
                        <p class="mb-0">เปลี่ยนสถานะเป็น "กำลังผลิต"</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">4</div>
                        <h6>จัดส่งสินค้า</h6>
                        <p class="mb-0">อัปเดตสถานะเป็น "เสร็จสิ้น" และแจ้งลูกค้า</p>
                    </div>
                </div>

                <div class="col-lg-6">
                    <h5 class="mb-3">🎨 การจัดการการออกแบบ</h5>
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <h6>รับคำขอออกแบบ</h6>
                        <p class="mb-0">ลูกค้าส่งคำขอผ่านระบบออนไลน์</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <h6>สร้างดีไซน์</h6>
                        <p class="mb-0">ใช้เครื่องมือออกแบบในระบบ</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <h6>ส่งให้ลูกค้าตรวจสอบ</h6>
                        <p class="mb-0">แชร์ดีไซน์ผ่านระบบแชท</p>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">4</div>
                        <h6>อนุมัติและผลิต</h6>
                        <p class="mb-0">เมื่อลูกค้าอนุมัติแล้วเริ่มผลิต</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Detail -->
        <div class="row">
            <div class="col-lg-4">
                <div class="guide-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="section-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 class="text-center mb-4">รายงานและสถิติ</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-chart-bar"></i>รายงานยอดขายรายวัน/เดือน</li>
                        <li><i class="fas fa-users"></i>สถิติลูกค้าใหม่</li>
                        <li><i class="fas fa-tshirt"></i>สินค้าขายดี</li>
                        <li><i class="fas fa-money-bill"></i>รายได้รวม</li>
                        <li><i class="fas fa-clock"></i>เวลาเฉลี่ยในการผลิต</li>
                        <li><i class="fas fa-star"></i>คะแนนความพึงพอใจ</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="guide-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="section-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h4 class="text-center mb-4">การสื่อสาร</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-comment"></i>แชทสดกับลูกค้า</li>
                        <li><i class="fas fa-bell"></i>การแจ้งเตือนอัตโนมัติ</li>
                        <li><i class="fas fa-envelope"></i>ส่งอีเมลแจ้งสถานะ</li>
                        <li><i class="fab fa-line"></i>เชื่อมต่อ LINE Official</li>
                        <li><i class="fab fa-facebook"></i>เชื่อมต่อ Facebook</li>
                        <li><i class="fas fa-phone"></i>บันทึกการโทร</li>
                    </ul>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="guide-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="section-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <h4 class="text-center mb-4">การจองและนัดหมาย</h4>
                    <ul class="feature-list">
                        <li><i class="fas fa-calendar-plus"></i>จัดการปฏิทินการจอง</li>
                        <li><i class="fas fa-clock"></i>กำหนดเวลาส่งมอบ</li>
                        <li><i class="fas fa-user-clock"></i>นัดหมายพบลูกค้า</li>
                        <li><i class="fas fa-map-marker"></i>จัดการสถานที่</li>
                        <li><i class="fas fa-reminder"></i>แจ้งเตือนล่วงหน้า</li>
                        <li><i class="fas fa-history"></i>ประวัติการนัดหมาย</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="guide-card" data-aos="fade-up">
            <h3 class="text-center mb-4">
                <i class="fas fa-cog me-2 text-primary"></i>การตั้งค่าระบบ
            </h3>

            <div class="row">
                <div class="col-lg-6">
                    <h5 class="mb-3">⚙️ ตั้งค่าทั่วไป</h5>
                    <ul class="feature-list">
                        <li><i class="fas fa-store"></i>ข้อมูลร้าน (ชื่อ, ที่อยู่, โทรศัพท์)</li>
                        <li><i class="fas fa-clock"></i>เวลาทำการ</li>
                        <li><i class="fas fa-money-bill"></i>สกุลเงินและภาษี</li>
                        <li><i class="fas fa-truck"></i>ค่าจัดส่ง</li>
                        <li><i class="fas fa-percent"></i>ส่วนลดและโปรโมชั่น</li>
                    </ul>
                </div>

                <div class="col-lg-6">
                    <h5 class="mb-3">👥 จัดการผู้ใช้</h5>
                    <ul class="feature-list">
                        <li><i class="fas fa-user-shield"></i>สิทธิ์ผู้ดูแลระบบ</li>
                        <li><i class="fas fa-users-cog"></i>จัดการบัญชีพนักงาน</li>
                        <li><i class="fas fa-key"></i>เปลี่ยนรหัสผ่าน</li>
                        <li><i class="fas fa-history"></i>บันทึกการเข้าใช้งาน</li>
                        <li><i class="fas fa-shield-alt"></i>ความปลอดภัย</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Tips and Tricks -->
        <div class="guide-card" data-aos="fade-up">
            <h3 class="text-center mb-4">
                <i class="fas fa-lightbulb me-2 text-primary"></i>เทิปการใช้งาน
            </h3>

            <div class="row">
                <div class="col-lg-6">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>เทิปสำหรับมือใหม่</h6>
                        <ul class="mb-0">
                            <li>เริ่มต้นจากการตั้งค่าข้อมูลร้านให้ครบถ้วน</li>
                            <li>เพิ่มสินค้าพื้นฐานก่อนเปิดให้บริการ</li>
                            <li>ทดสอบระบบการสั่งซื้อก่อนใช้งานจริง</li>
                            <li>ตั้งค่าการแจ้งเตือนให้เหมาะสม</li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>เทิปเพิ่มประสิทธิภาพ</h6>
                        <ul class="mb-0">
                            <li>ใช้ระบบรายงานเพื่อวิเคราะห์ยอดขาย</li>
                            <li>ตอบแชทลูกค้าให้เร็วเพื่อเพิ่มความพึงพอใจ</li>
                            <li>อัปเดตสถานะคำสั่งซื้อให้เป็นปัจจุบัน</li>
                            <li>สำรองข้อมูลเป็นประจำ</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="guide-card text-center" data-aos="fade-up">
            <h3 class="mb-4">
                <i class="fas fa-headset me-2 text-primary"></i>ต้องการความช่วยเหลือ?
            </h3>
            <p class="lead mb-4">ทีมงานพร้อมให้การสนับสนุนตลอด 24 ชั่วโมง</p>
            <div class="row justify-content-center">
                <div class="col-md-3">
                    <a href="tel:************" class="btn btn-outline-primary btn-lg w-100 mb-3">
                        <i class="fas fa-phone me-2"></i>โทรหาเรา
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="https://line.me/ti/p/@gtsport" class="btn btn-outline-success btn-lg w-100 mb-3">
                        <i class="fab fa-line me-2"></i>LINE Support
                    </a>
                </div>
                <div class="col-md-3">
                    <a href="mailto:<EMAIL>" class="btn btn-outline-info btn-lg w-100 mb-3">
                        <i class="fas fa-envelope me-2"></i>อีเมล
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Admin Button -->
    <a href="dashboard.php" class="back-to-admin" title="กลับสู่แดชบอร์ด">
        <i class="fas fa-arrow-left"></i>
    </a>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add click tracking for quick access buttons
        document.querySelectorAll('.quick-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const page = this.querySelector('h6').textContent;
                console.log(`เข้าถึงหน้า: ${page}`);
            });
        });
    </script>
</body>
</html>
