<?php
session_start();
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ทดสอบ Navbar Effects - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #ee501b 0%, #ff6b35 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 1000px;
            margin: 100px auto 50px;
        }
        
        .effect-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #ee501b;
        }
        
        .demo-navbar {
            background: white;
            border-radius: 10px;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .demo-nav-item {
            display: inline-block;
            margin: 0 15px;
            padding: 10px 15px;
            color: #303136;
            text-decoration: none;
            position: relative;
            transition: all 0.3s ease;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .demo-nav-item:hover {
            color: #ee501b;
            background: rgba(238, 80, 27, 0.1);
            text-decoration: none;
        }
        
        .demo-nav-item::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, #ee501b, #ff6b35);
            transition: all 0.3s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .demo-nav-item:hover::after {
            width: 80%;
        }
        
        .demo-nav-item.active {
            color: #ee501b;
            background: rgba(238, 80, 27, 0.1);
            font-weight: 600;
        }
        
        .demo-nav-item.active::after {
            width: 80%;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li i {
            color: #ee501b;
            margin-right: 10px;
            width: 20px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        .highlight {
            color: #ffd700;
        }
        
        .status-badge {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
            background: #d1edff;
            color: #0c63e4;
            margin-bottom: 20px;
        }
    </style>
</head>

<body>
<?php include 'includes/header.php'; ?>

<div class="container">
    <div class="test-container">
        <div class="text-center mb-5">
            <h1 class="display-4 fw-bold text-primary mb-3">
                <i class="fas fa-bars me-3"></i>ทดสอบ Navbar Effects
            </h1>
            <p class="lead">ทดสอบเอฟเฟกต์ Active State และ Hover Line ของ Navigation Bar</p>
            <span class="status-badge">
                <i class="fas fa-check me-1"></i>Navbar Effects เปิดใช้งานแล้ว
            </span>
        </div>
        
        <!-- Active State Demo -->
        <div class="effect-demo">
            <h4><i class="fas fa-mouse-pointer me-2"></i>Active State & Hover Effects</h4>
            <p class="text-muted mb-3">ทดสอบเอฟเฟกต์เมื่อ hover และ active state</p>
            
            <div class="demo-navbar">
                <a href="#" class="demo-nav-item active">
                    <i class="fas fa-home me-1"></i>หน้าแรก
                </a>
                <a href="#" class="demo-nav-item">
                    <i class="fas fa-box me-1"></i>สินค้า
                </a>
                <a href="#" class="demo-nav-item">
                    <i class="fas fa-building me-1"></i>เกี่ยวกับเรา
                </a>
                <a href="#" class="demo-nav-item">
                    <i class="fas fa-palette me-1"></i>ออกแบบเสื้อ
                </a>
                <a href="#" class="demo-nav-item">
                    <i class="fas fa-phone me-1"></i>ติดต่อเรา
                </a>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>✨ เอฟเฟกต์ที่ได้:</h6>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i>Hover Line Animation</li>
                        <li><i class="fas fa-check"></i>Background Color Change</li>
                        <li><i class="fas fa-check"></i>Active State Highlighting</li>
                        <li><i class="fas fa-check"></i>Smooth Transitions</li>
                        <li><i class="fas fa-check"></i>Auto-detect Current Page</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>🎯 การทำงาน:</h6>
                    <ul class="feature-list">
                        <li><i class="fas fa-arrow-right"></i>Hover เพื่อดูเส้นใต้</li>
                        <li><i class="fas fa-arrow-right"></i>Active state ตาม URL</li>
                        <li><i class="fas fa-arrow-right"></i>Gradient line effect</li>
                        <li><i class="fas fa-arrow-right"></i>Mobile responsive</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- CSS Code -->
        <div class="effect-demo">
            <h4><i class="fas fa-code me-2"></i>CSS Code ที่ใช้</h4>
            <p class="text-muted">โค้ด CSS สำหรับสร้างเอฟเฟกต์</p>
            
            <div class="code-block">
<span class="highlight">/* Navbar Link Styling */</span>
.navbar-nav .nav-link {
    color: var(--dark-color) !important;
    font-weight: 500;
    margin: 0 10px;
    padding: 10px 15px !important;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 8px;
}

<span class="highlight">/* Hover Effects */</span>
.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(238, 80, 27, 0.1);
}

<span class="highlight">/* Hover Line Effect */</span>
.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: all 0.3s ease;
    transform: translateX(-50%);
    border-radius: 2px;
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

<span class="highlight">/* Active State */</span>
.navbar-nav .nav-item.active .nav-link {
    color: var(--primary-color) !important;
    background: rgba(238, 80, 27, 0.1);
    font-weight: 600;
}

.navbar-nav .nav-item.active .nav-link::after {
    width: 80%;
}
            </div>
        </div>
        
        <!-- JavaScript Code -->
        <div class="effect-demo">
            <h4><i class="fas fa-code me-2"></i>JavaScript Code ที่ใช้</h4>
            <p class="text-muted">โค้ด JavaScript สำหรับตั้งค่า Active State</p>
            
            <div class="code-block">
<span class="highlight">// Auto-detect current page and set active state</span>
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    
    <span class="highlight">// Remove active class from all nav items</span>
    document.querySelectorAll('.navbar-nav .nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    <span class="highlight">// Add active class based on current path</span>
    document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPath || (currentPath === '/' && href === '/')) {
            link.closest('.nav-item').classList.add('active');
        }
    });
});
            </div>
        </div>
        
        <!-- Test Navigation -->
        <div class="effect-demo">
            <h4><i class="fas fa-link me-2"></i>ทดสอบ Navigation</h4>
            <p class="text-muted">คลิกลิงก์เพื่อทดสอบ Active State</p>
            
            <div class="row g-3">
                <div class="col-md-4">
                    <a href="/" class="btn btn-outline-primary w-100">
                        <i class="fas fa-home me-2"></i>หน้าแรก
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="/products" class="btn btn-outline-primary w-100">
                        <i class="fas fa-box me-2"></i>สินค้า
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="/about" class="btn btn-outline-primary w-100">
                        <i class="fas fa-building me-2"></i>เกี่ยวกับเรา
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="/design" class="btn btn-outline-primary w-100">
                        <i class="fas fa-palette me-2"></i>ออกแบบเสื้อ
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="/contact" class="btn btn-outline-primary w-100">
                        <i class="fas fa-phone me-2"></i>ติดต่อเรา
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="/test_urls" class="btn btn-outline-secondary w-100">
                        <i class="fas fa-link me-2"></i>ทดสอบ URLs
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="mt-4 p-4 bg-info bg-opacity-10 rounded">
            <h6><i class="fas fa-info-circle me-2"></i>วิธีการทดสอบ:</h6>
            <ol>
                <li><strong>Hover Effect:</strong> เลื่อนเมาส์ไปที่เมนูด้านบนเพื่อดูเอฟเฟกต์</li>
                <li><strong>Active State:</strong> คลิกลิงก์เพื่อไปหน้าต่างๆ และดู Active State</li>
                <li><strong>Mobile Test:</strong> ลองใช้บนมือถือเพื่อทดสอบ Responsive</li>
                <li><strong>Animation:</strong> สังเกตเส้นใต้ที่เลื่อนออกมาเมื่อ Hover</li>
            </ol>
        </div>
        
        <!-- Back to Home -->
        <div class="text-center mt-5">
            <a href="/" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-home me-2"></i>กลับหน้าแรก
            </a>
            <a href="/test_urls" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-link me-2"></i>ทดสอบ Clean URLs
            </a>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Demo navbar interaction
document.querySelectorAll('.demo-nav-item').forEach(item => {
    item.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Remove active from all demo items
        document.querySelectorAll('.demo-nav-item').forEach(nav => {
            nav.classList.remove('active');
        });
        
        // Add active to clicked item
        this.classList.add('active');
        
        // Show feedback
        const text = this.textContent.trim();
        alert(`คลิก: ${text}\n\nในเว็บไซต์จริง จะไปหน้า ${text} และ Active State จะเปลี่ยนอัตโนมัติ`);
    });
});
</script>

</body>
</html>
