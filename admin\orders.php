<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

// $pdo ถูกสร้างแล้วใน database.php
$message = '';
$error = '';

// จัดการการอัพเดตสถานะ
if ($_POST && isset($_POST['action'])) {
    $action = $_POST['action'];

    try {
        if ($action === 'update_status') {
            $order_id = (int)$_POST['order_id'];
            $status = $_POST['status'];

            $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$status, $order_id]);

            $message = 'อัพเดตสถานะคำสั่งซื้อเรียบร้อยแล้ว';
        }

        if ($action === 'add_note') {
            $order_id = (int)$_POST['order_id'];
            $note = trim($_POST['note']);

            $stmt = $pdo->prepare("UPDATE orders SET admin_note = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$note, $order_id]);

            $message = 'เพิ่มหมายเหตุเรียบร้อยแล้ว';
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// ตัวแปรสำหรับการค้นหาและกรอง
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// สร้างคำสั่ง SQL สำหรับค้นหา
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(o.order_number LIKE ? OR c.name LIKE ? OR c.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(o.created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(o.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// นับจำนวนคำสั่งซื้อทั้งหมด
$count_sql = "SELECT COUNT(*) as total FROM orders o LEFT JOIN customers c ON o.customer_id = c.id $where_clause";
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_orders = $count_stmt->fetch()['total'];
$total_pages = ceil($total_orders / $limit);

// ดึงข้อมูลคำสั่งซื้อ
$sql = "
    SELECT o.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone
    FROM orders o
    LEFT JOIN customers c ON o.customer_id = c.id
    $where_clause
    ORDER BY o.created_at DESC
    LIMIT $limit OFFSET $offset
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$orders = $stmt->fetchAll();

// สถิติการสั่งซื้อ
try {
    $stats_sql = "
        SELECT
            COUNT(*) as total_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'processing' THEN 1 END) as processing_orders,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders,
            SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_revenue,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_orders
        FROM orders
    ";
    $stats_stmt = $pdo->query($stats_sql);
    $stats = $stats_stmt->fetch();
} catch (Exception $e) {
    $stats = [
        'total_orders' => 0,
        'pending_orders' => 0,
        'processing_orders' => 0,
        'completed_orders' => 0,
        'cancelled_orders' => 0,
        'total_revenue' => 0,
        'today_orders' => 0
    ];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการคำสั่งซื้อ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }

        .main-content {
            margin-left: 280px;
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.05);
            margin-bottom: 20px;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stats-card.pending {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .stats-card.processing {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .stats-card.completed {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .stats-card.revenue {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending { background-color: #ffeaa7; color: #fdcb6e; }
        .status-processing { background-color: #74b9ff; color: white; }
        .status-completed { background-color: #00b894; color: white; }
        .status-cancelled { background-color: #fd79a8; color: white; }

        .search-form {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.05);
        }

        .order-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }

        .table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #2c3e50;
        }

        .action-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 8px;
            font-size: 12px;
            margin: 2px;
        }

        .sidebar-nav {
            padding: 0;
            margin: 20px 0;
        }

        .sidebar-nav li {
            list-style: none;
        }

        .sidebar-nav a {
            display: block;
            padding: 12px 25px;
            color: #bdc3c7;
            text-decoration: none;
            border-left: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: #ee501b;
        }

        .sidebar-nav i {
            width: 20px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4>GT Sport Design</h4>
            <small>Admin Panel</small>
        </div>
        <ul class="sidebar-nav">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> แดชบอร์ด</a></li>
            <li><a href="orders.php" class="active"><i class="fas fa-shopping-cart"></i> คำสั่งซื้อ</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i> สินค้า</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i> การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i> แชทสด</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-shopping-cart me-2"></i> จัดการคำสั่งซื้อ</h2>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="fas fa-download me-2"></i> ส่งออกข้อมูล
            </button>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i> <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i> <?= htmlspecialchars($error) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- สถิติ -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <h3><?= number_format($stats['total_orders']) ?></h3>
                    <p class="mb-0">คำสั่งซื้อทั้งหมด</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card pending">
                    <h3><?= number_format($stats['pending_orders']) ?></h3>
                    <p class="mb-0">รอดำเนินการ</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card completed">
                    <h3><?= number_format($stats['completed_orders']) ?></h3>
                    <p class="mb-0">เสร็จสิ้น</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card revenue">
                    <h3>฿<?= number_format($stats['total_revenue']) ?></h3>
                    <p class="mb-0">รายได้รวม</p>
                </div>
            </div>
        </div>

        <!-- ฟอร์มค้นหา -->
        <div class="search-form">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">ค้นหา</label>
                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>"
                           placeholder="เลขที่คำสั่งซื้อ, ชื่อลูกค้า, อีเมล">
                </div>
                <div class="col-md-2">
                    <label class="form-label">สถานะ</label>
                    <select class="form-select" name="status">
                        <option value="">ทั้งหมด</option>
                        <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>รอดำเนินการ</option>
                        <option value="processing" <?= $status_filter === 'processing' ? 'selected' : '' ?>>กำลังดำเนินการ</option>
                        <option value="completed" <?= $status_filter === 'completed' ? 'selected' : '' ?>>เสร็จสิ้น</option>
                        <option value="cancelled" <?= $status_filter === 'cancelled' ? 'selected' : '' ?>>ยกเลิก</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">วันที่เริ่มต้น</label>
                    <input type="date" class="form-control" name="date_from" value="<?= htmlspecialchars($date_from) ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">วันที่สิ้นสุด</label>
                    <input type="date" class="form-control" name="date_to" value="<?= htmlspecialchars($date_to) ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary flex-fill">
                            <i class="fas fa-search me-1"></i> ค้นหา
                        </button>
                        <a href="orders.php" class="btn btn-outline-secondary">
                            <i class="fas fa-undo me-1"></i> รีเซ็ต
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- ตารางคำสั่งซื้อ -->
        <div class="card order-table">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>เลขที่คำสั่งซื้อ</th>
                                <th>ลูกค้า</th>
                                <th>จำนวนเงิน</th>
                                <th>สถานะ</th>
                                <th>วันที่สั่งซื้อ</th>
                                <th>การดำเนินการ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                            <tr>
                                <td>
                                    <strong><?= htmlspecialchars($order['order_number']) ?></strong>
                                </td>
                                <td>
                                    <div>
                                        <strong><?= htmlspecialchars($order['customer_name'] ?: 'ไม่ระบุ') ?></strong><br>
                                        <small class="text-muted"><?= htmlspecialchars($order['customer_email'] ?: '') ?></small><br>
                                        <small class="text-muted"><?= htmlspecialchars($order['customer_phone'] ?: '') ?></small>
                                    </div>
                                </td>
                                <td>
                                    <strong>฿<?= number_format($order['total_amount']) ?></strong>
                                </td>
                                <td>
                                    <span class="status-badge status-<?= $order['status'] ?>">
                                        <?php
                                        $status_text = [
                                            'pending' => 'รอดำเนินการ',
                                            'processing' => 'กำลังดำเนินการ',
                                            'completed' => 'เสร็จสิ้น',
                                            'cancelled' => 'ยกเลิก'
                                        ];
                                        echo $status_text[$order['status']] ?? $order['status'];
                                        ?>
                                    </span>
                                </td>
                                <td>
                                    <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?>
                                </td>
                                <td>
                                    <button class="action-btn btn-primary" onclick="viewOrder(<?= $order['id'] ?>)">
                                        <i class="fas fa-eye"></i> ดู
                                    </button>
                                    <button class="action-btn btn-warning" onclick="updateStatus(<?= $order['id'] ?>, '<?= $order['status'] ?>')">
                                        <i class="fas fa-edit"></i> สถานะ
                                    </button>
                                    <button class="action-btn btn-info" onclick="addNote(<?= $order['id'] ?>)">
                                        <i class="fas fa-sticky-note"></i> หมายเหตุ
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <?php if (empty($orders)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="text-muted">ไม่พบคำสั่งซื้อ</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                <?php
                $query_params = $_GET;
                unset($query_params['page']);
                $query_string = http_build_query($query_params);
                ?>

                <li class="page-item <?= $page <= 1 ? 'disabled' : '' ?>">
                    <a class="page-link" href="?page=<?= $page - 1 ?>&<?= $query_string ?>">ก่อนหน้า</a>
                </li>

                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>&<?= $query_string ?>"><?= $i ?></a>
                    </li>
                <?php endfor; ?>

                <li class="page-item <?= $page >= $total_pages ? 'disabled' : '' ?>">
                    <a class="page-link" href="?page=<?= $page + 1 ?>&<?= $query_string ?>">ถัดไป</a>
                </li>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

    <!-- Modal สำหรับอัพเดตสถานะ -->
    <div class="modal fade" id="statusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">อัพเดตสถานะคำสั่งซื้อ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="update_status">
                        <input type="hidden" name="order_id" id="status_order_id">

                        <div class="mb-3">
                            <label class="form-label">สถานะใหม่</label>
                            <select class="form-select" name="status" id="new_status" required>
                                <option value="pending">รอดำเนินการ</option>
                                <option value="processing">กำลังดำเนินการ</option>
                                <option value="completed">เสร็จสิ้น</option>
                                <option value="cancelled">ยกเลิก</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">บันทึก</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal สำหรับเพิ่มหมายเหตุ -->
    <div class="modal fade" id="noteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เพิ่มหมายเหตุ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_note">
                        <input type="hidden" name="order_id" id="note_order_id">

                        <div class="mb-3">
                            <label class="form-label">หมายเหตุ</label>
                            <textarea class="form-control" name="note" rows="4" placeholder="ใส่หมายเหตุสำหรับคำสั่งซื้อนี้..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">บันทึก</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal สำหรับดูรายละเอียดคำสั่งซื้อ -->
    <div class="modal fade" id="orderDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รายละเอียดคำสั่งซื้อ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="orderDetailContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateStatus(orderId, currentStatus) {
            document.getElementById('status_order_id').value = orderId;
            document.getElementById('new_status').value = currentStatus;
            new bootstrap.Modal(document.getElementById('statusModal')).show();
        }

        function addNote(orderId) {
            document.getElementById('note_order_id').value = orderId;
            new bootstrap.Modal(document.getElementById('noteModal')).show();
        }

        function viewOrder(orderId) {
            // Load order details via AJAX
            fetch(`order_detail.php?id=${orderId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('orderDetailContent').innerHTML = html;
                    new bootstrap.Modal(document.getElementById('orderDetailModal')).show();
                })
                .catch(error => {
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
                });
        }
    </script>
</body>
</html>
