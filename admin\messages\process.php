<?php
session_start();
require_once '../../includes/config.php';
require_once '../../includes/db.php';

// Get action from POST or GET
$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');

if ($action === 'mark_read') {
    // Get message ID
    $message_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    // Validate data
    if ($message_id <= 0) {
        $_SESSION['message'] = 'ID ข้อความไม่ต้อง';
        $_SESSION['message_type'] = 'error';
        header('Location: index.php');
        exit;
    }
    
    // In a real application, update the database
    // $sql = "UPDATE contacts_messages SET status = 'read', updated_at = NOW() WHERE id = ?";
    // $params = [$message_id];
    // db_execute($sql, $params);
    
    $_SESSION['message'] = 'ทำเครืองหมายข้อความว่าอ่านแล้ว';
    $_SESSION['message_type'] = 'success';
    
    // Log the action
    // log_action('mark_message_read', 'Marked message ID: ' . $message_id . ' as read');
    
    // Redirect back to the referring page or index
    $redirect = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'index.php';
    header('Location: ' . $redirect);
    exit;
} elseif ($action === 'reply') {
    // Get form data
    $message_id = isset($_POST['message_id']) ? (int)$_POST['message_id'] : 0;
    $reply_text = isset($_POST['reply_text']) ? sanitize_input($_POST['reply_text']) : '';
    $recipient_email = isset($_POST['recipient_email']) ? sanitize_input($_POST['recipient_email']) : '';
    
    // Validate data
    if ($message_id <= 0 || empty($reply_text) || empty($recipient_email)) {
        $_SESSION['message'] = 'กรอกข้อมูลให้ครบถ้วน';
        $_SESSION['message_type'] = 'error';
        header('Location: reply.php?id=' . $message_id);
        exit;
    }
    
    // In a real application:
    // 1. Update the original message status
    // $sql = "UPDATE contacts_messages SET status = 'replied', updated_at = NOW() WHERE id = ?";
    // $params = [$message_id];
    // db_execute($sql, $params);
    
    // 2. Store the reply in the database
    // $sql = "INSERT INTO message_replies (message_id, reply_text, admin_id, created_at) VALUES (?, ?, ?, NOW())";
    // $params = [$message_id, $reply_text, $_SESSION['admin_id']];
    // db_execute($sql, $params);
    
    // 3. Send the email (in a real application)
    // $subject = "ตอบจาก " . get_setting('site_title');
    // $headers = "From: " . get_setting('contact_email') . "\r\n";
    // $headers .= "Reply-To: " . get_setting('contact_email') . "\r\n";
    // $headers .= "MIME-Version: 1.0\r\n";
    // $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    
    // $message_body = "<html><body>";
    // $message_body .= "<p>ค้า,</p>";
    // $message_body .= "<p>" . nl2br($reply_text) . "</p>";
    // $message_body .= "<p>ขอบคุณต่อเรา<br>" . get_setting('site_title') . "</p>";
    // $message_body .= "</body></html>";
    
    // mail($recipient_email, $subject, $message_body, $headers);
    
    $_SESSION['message'] = 'ส่งการตอบกลับแล้ว';
    $_SESSION['message_type'] = 'success';
    
    // Log the action
    // log_action('reply_message', 'Replied to message ID: ' . $message_id);
    
    header('Location: index.php');
    exit;
} elseif ($action === 'delete') {
    // Get message ID
    $message_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    
    // Validate data
    if ($message_id <= 0) {
        $_SESSION['message'] = 'ID ข้อความไม่ต้อง';
        $_SESSION['message_type'] = 'error';
        header('Location: index.php');
        exit;
    }
    
    // In a real application, delete from database
    // $sql = "DELETE FROM contacts_messages WHERE id = ?";
    // $params = [$message_id];
    // db_execute($sql, $params);
    
    // Also delete related replies
    // $sql = "DELETE FROM message_replies WHERE message_id = ?";
    // $params = [$message_id];
    // db_execute($sql, $params);
    
    $_SESSION['message'] = 'ลบข้อความแล้ว';
    $_SESSION['message_type'] = 'success';
    
    // Log the action
    // log_action('delete_message', 'Deleted message ID: ' . $message_id);
    
    header('Location: index.php');
    exit;
} else {
    $_SESSION['message'] = 'การกระทำไม่ต้อง';
    $_SESSION['message_type'] = 'error';
    header('Location: index.php');
    exit;
}







