<?php
/**
 * GT-SportDesign - Order Tracking
 * Professional order tracking system
 * Version: 2.0 - Production Ready
 */

session_start();
require_once './config/database.php';

$db = getDbConnection();
$order = null;
$error_message = '';

// Handle order lookup
if ($_SERVER['REQUEST_METHOD'] === 'POST' || !empty($_GET['order_id'])) {
    $order_id = trim($_POST['order_id'] ?? $_GET['order_id'] ?? '');
    $email = trim($_POST['email'] ?? $_GET['email'] ?? '');
    
    if (!empty($order_id)) {
        try {
            $stmt = $db->prepare("
                SELECT * FROM payment_orders 
                WHERE order_id = ? 
                AND (customer_email = ? OR ? = '')
                ORDER BY created_at DESC 
                LIMIT 1
            ");
            $stmt->execute([$order_id, $email, $email]);
            $order = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$order) {
                $error_message = 'ไม่พบคำสั่งซื้อ กรุณาตรวจสอบรหัสคำสั่งซื้อและอีเมลอีกครั้ง';
            }
        } catch (Exception $e) {
            $error_message = 'เกิดข้อผิดพลาดในการค้นหา กรุณาลองใหม่อีกครั้ง';
        }
    } else {
        $error_message = 'กรุณากรอกรหัสคำสั่งซื้อ';
    }
}

function getStatusText($status) {
    $statuses = [
        'pending' => 'รอชำระเงิน',
        'pending_verification' => 'รอตรวจสอบการชำระเงิน',
        'confirmed' => 'ยืนยันการชำระเงินแล้ว',
        'paid' => 'ชำระเงินแล้ว',
        'processing' => 'กำลังผลิต',
        'shipping' => 'จัดส่งแล้ว',
        'completed' => 'เสร็จสิ้น',
        'cancelled' => 'ยกเลิก',
        'failed' => 'ล้มเหลว'
    ];
    return $statuses[$status] ?? $status;
}

function getStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'pending_verification' => 'info',
        'confirmed' => 'primary',
        'paid' => 'success',
        'processing' => 'info',
        'shipping' => 'primary',
        'completed' => 'success',
        'cancelled' => 'secondary',
        'failed' => 'danger'
    ];
    return $colors[$status] ?? 'secondary';
}

function getProgressPercentage($status) {
    $progress = [
        'pending' => 10,
        'pending_verification' => 25,
        'confirmed' => 40,
        'paid' => 50,
        'processing' => 70,
        'shipping' => 85,
        'completed' => 100,
        'cancelled' => 0,
        'failed' => 0
    ];
    return $progress[$status] ?? 0;
}
?>

<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ติดตามคำสั่งซื้อ - GT-SportDesign</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: #f8f9fa;
        }
        
        .hero-section {
            background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
            color: white;
            padding: 80px 0;
            margin-bottom: 50px;
        }
        
        .tracking-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
        
        .order-status {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .progress-timeline {
            position: relative;
            padding: 20px 0;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
        }
        
        .timeline-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
            z-index: 2;
        }
        
        .timeline-content {
            flex: 1;
        }
        
        .timeline-line {
            position: absolute;
            left: 19px;
            top: 40px;
            bottom: -20px;
            width: 2px;
            background: #dee2e6;
        }
        
        .timeline-item:last-child .timeline-line {
            display: none;
        }
        
        .order-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .search-form {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include './includes/header.php'; ?>
    
    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="display-4 fw-bold mb-3">ติดตามคำสั่งซื้อ</h1>
                    <p class="lead mb-4">ตรวจสอบสถานะคำสั่งซื้อและการจัดส่งของคุณ</p>
                </div>
                <div class="col-lg-4 text-center">
                    <i class="fas fa-shipping-fast" style="font-size: 8rem; opacity: 0.3;"></i>
                </div>
            </div>
        </div>
    </section>
    
    <div class="container">
        <!-- Search Form -->
        <?php if (!$order): ?>
        <div class="search-form">
            <h3 class="mb-4"><i class="fas fa-search me-2"></i>ค้นหาคำสั่งซื้อ</h3>
            
            <?php if ($error_message): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            </div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">รหัสคำสั่งซื้อ *</label>
                        <input type="text" name="order_id" class="form-control" 
                               placeholder="เช่น ORD-20241201-001" 
                               value="<?php echo htmlspecialchars($_POST['order_id'] ?? ''); ?>" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">อีเมล (ไม่บังคับ)</label>
                        <input type="email" name="email" class="form-control" 
                               placeholder="อีเมลที่ใช้สั่งซื้อ" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-2"></i>ค้นหา
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <?php endif; ?>
        
        <!-- Order Details -->
        <?php if ($order): ?>
        <div class="tracking-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3><i class="fas fa-receipt me-2"></i>รายละเอียดคำสั่งซื้อ</h3>
                <a href="track_order.php" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>ค้นหาใหม่
                </a>
            </div>
            
            <!-- Order Status -->
            <div class="order-status">
                <h4 class="mb-3">สถานะปัจจุบัน</h4>
                <span class="badge bg-<?php echo getStatusColor($order['payment_status']); ?> fs-5 px-3 py-2">
                    <?php echo getStatusText($order['payment_status']); ?>
                </span>
                
                <!-- Progress Bar -->
                <div class="progress mt-3" style="height: 10px;">
                    <div class="progress-bar bg-<?php echo getStatusColor($order['payment_status']); ?>" 
                         style="width: <?php echo getProgressPercentage($order['payment_status']); ?>%"></div>
                </div>
            </div>
            
            <!-- Order Information -->
            <div class="order-details">
                <div class="detail-row">
                    <strong>รหัสคำสั่งซื้อ:</strong>
                    <span><?php echo htmlspecialchars($order['order_id']); ?></span>
                </div>
                <div class="detail-row">
                    <strong>สินค้า:</strong>
                    <span><?php echo htmlspecialchars($order['product_name']); ?></span>
                </div>
                <?php if ($order['product_size']): ?>
                <div class="detail-row">
                    <strong>ไซส์:</strong>
                    <span><?php echo htmlspecialchars($order['product_size']); ?></span>
                </div>
                <?php endif; ?>
                <div class="detail-row">
                    <strong>จำนวน:</strong>
                    <span><?php echo number_format($order['quantity']); ?> ชิ้น</span>
                </div>
                <div class="detail-row">
                    <strong>ราคารวม:</strong>
                    <span class="text-primary fw-bold">฿<?php echo number_format($order['total_amount'], 2); ?></span>
                </div>
                <div class="detail-row">
                    <strong>ชื่อผู้สั่ง:</strong>
                    <span><?php echo htmlspecialchars($order['customer_name']); ?></span>
                </div>
                <div class="detail-row">
                    <strong>อีเมล:</strong>
                    <span><?php echo htmlspecialchars($order['customer_email']); ?></span>
                </div>
                <?php if ($order['customer_phone']): ?>
                <div class="detail-row">
                    <strong>เบอร์โทร:</strong>
                    <span><?php echo htmlspecialchars($order['customer_phone']); ?></span>
                </div>
                <?php endif; ?>
                <div class="detail-row">
                    <strong>วันที่สั่งซื้อ:</strong>
                    <span><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></span>
                </div>
                <?php if ($order['payment_date']): ?>
                <div class="detail-row">
                    <strong>วันที่ชำระเงิน:</strong>
                    <span><?php echo date('d/m/Y H:i', strtotime($order['payment_date'])); ?></span>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Timeline -->
            <div class="mt-4">
                <h5><i class="fas fa-history me-2"></i>ประวัติการดำเนินการ</h5>
                <div class="progress-timeline">
                    <div class="timeline-item">
                        <div class="timeline-icon bg-success">
                            <i class="fas fa-check"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>ได้รับคำสั่งซื้อ</h6>
                            <small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></small>
                        </div>
                        <div class="timeline-line"></div>
                    </div>
                    
                    <?php if (in_array($order['payment_status'], ['paid', 'confirmed', 'processing', 'shipping', 'completed'])): ?>
                    <div class="timeline-item">
                        <div class="timeline-icon bg-success">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>ชำระเงินแล้ว</h6>
                            <small class="text-muted">
                                <?php echo $order['payment_date'] ? date('d/m/Y H:i', strtotime($order['payment_date'])) : 'รอข้อมูล'; ?>
                            </small>
                        </div>
                        <div class="timeline-line"></div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (in_array($order['payment_status'], ['processing', 'shipping', 'completed'])): ?>
                    <div class="timeline-item">
                        <div class="timeline-icon bg-info">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>กำลังผลิต</h6>
                            <small class="text-muted">อยู่ระหว่างการผลิต</small>
                        </div>
                        <div class="timeline-line"></div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (in_array($order['payment_status'], ['shipping', 'completed'])): ?>
                    <div class="timeline-item">
                        <div class="timeline-icon bg-primary">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>จัดส่งแล้ว</h6>
                            <small class="text-muted">สินค้าอยู่ระหว่างการจัดส่ง</small>
                        </div>
                        <div class="timeline-line"></div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($order['payment_status'] === 'completed'): ?>
                    <div class="timeline-item">
                        <div class="timeline-icon bg-success">
                            <i class="fas fa-flag-checkered"></i>
                        </div>
                        <div class="timeline-content">
                            <h6>เสร็จสิ้น</h6>
                            <small class="text-muted">ส่งมอบสินค้าเรียบร้อย</small>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="text-center mt-4">
                <a href="contact.php" class="btn btn-outline-primary me-2">
                    <i class="fas fa-question-circle me-2"></i>สอบถามเพิ่มเติม
                </a>
                <a href="products.php" class="btn btn-primary">
                    <i class="fas fa-shopping-cart me-2"></i>สั่งซื้อเพิ่ม
                </a>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Footer -->
    <?php include './includes/footer.php'; ?>
    
    <!-- Bootstrap JS -->
     <button class="chat-toggle" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
    </button>

    <div class="chat-widget" id="chatWidget">
        <div class="chat-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-comments"></i> แชทสด</h6>
                <button class="btn btn-sm text-white" onclick="toggleChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="text-center text-muted py-3">
                <small>พิมพ์ข้อความเพื่อเริ่มการสนทนา</small>
            </div>
        </div>
        <div class="p-3">
            <div class="input-group">
                <input type="text" class="form-control" id="chatInput" placeholder="พิมพ์ข้อความ..."
                       onkeypress="if(event.key==='Enter') sendMessage()">
                <button class="btn btn-primary" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let chatOpen = false;

        function toggleChat() {
            const widget = document.getElementById('chatWidget');
            const toggle = document.querySelector('.chat-toggle');

            if (chatOpen) {
                widget.style.display = 'none';
                toggle.innerHTML = '<i class="fas fa-comments"></i>';
                chatOpen = false;
            } else {
                widget.style.display = 'block';
                toggle.innerHTML = '<i class="fas fa-times"></i>';
                chatOpen = true;

                // Auto message
                if (!localStorage.getItem('chatWelcome')) {
                    setTimeout(() => {
                        addMessage('GT Sport Design', 'สวัสดีครับ! มีอะไรให้เราช่วยไหมครับ?', 'admin');
                        localStorage.setItem('chatWelcome', 'shown');
                    }, 1000);
                }
            }
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                addMessage('คุณ', message, 'user');
                input.value = '';

                // Auto reply
                setTimeout(() => {
                    const replies = [
                        'ขอบคุณสำหรับข้อความครับ เราจะติดต่อกลับไปเร็วๆ นี้',
                        'สำหรับข้อมูลเพิ่มเติม สามารถโทร ************ ได้เลยครับ',
                        'หรือสามารถ LINE มาที่ @gtsport ได้ครับ'
                    ];
                    const reply = replies[Math.floor(Math.random() * replies.length)];
                    addMessage('GT Sport Design', reply, 'admin');
                }, 1500);
            }
        }

        function addMessage(sender, text, type) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `mb-2 ${type === 'user' ? 'text-end' : ''}`;

            messageDiv.innerHTML = `
                <div class="d-inline-block p-2 rounded ${type === 'user' ? 'bg-primary text-white' : 'bg-light'}">
                    <small class="fw-bold">${sender}</small><br>
                    <span>${text}</span>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Auto-resize contact form
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });
        });
    </script>

