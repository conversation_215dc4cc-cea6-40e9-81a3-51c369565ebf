<?php
session_start();

// ถ้าล็อกอินแล้วให้ไปหน้า dashboard
if (isset($_SESSION['admin_id']) || isset($_SESSION['admin_logged_in'])) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // ตรวจสอบข้อมูลส่วนตัว (ในการใช้งานส่วนตัวควรเชื่อมต่อกับฐานข้อมูล)
    if ($username === 'admin' && $password === 'admin123') {
        $_SESSION['admin_id'] = 1;
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_name'] = '<|im_start|>้<|im_start|>ระบบ';
        $_SESSION['admin_fullname'] = 'น้ีน์ระบบ GT Sport Design';
        $_SESSION['admin_role'] = 'Super Admin';
        
        header('Location: dashboard.php');
        exit();
    } else {
        $error_message = 'ชื่อ้ใช้ผ่านไม่ถูกต้อง';
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - GT Sport Design Admin</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            min-height: 500px;
        }
        
        .login-left {
            background: linear-gradient(135deg, #ee501b 0%, #ff6b35 100%);
            color: white;
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 60px 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .brand-logo {
            font-size: 3rem;
            margin-bottom: 20px;
        }
        
        .brand-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .login-form {
            width: 100%;
        }
        
        .form-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .form-subtitle {
            color: #6c757d;
            margin-bottom: 30px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #ee501b;
            box-shadow: 0 0 0 0.2rem rgba(238, 80, 27, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #ee501b 0%, #ff6b35 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(238, 80, 27, 0.4);
            color: white;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .features {
            margin-top: 40px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        
        .demo-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #ee501b;
        }
        
        .demo-info h6 {
            color: #ee501b;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .demo-credentials {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
        
        @media (max-width: 768px) {
            .login-left {
                padding: 40px 20px;
            }
            
            .login-right {
                padding: 40px 20px;
            }
            
            .brand-title {
                font-size: 1.5rem;
            }
            
            .form-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0 h-100">
                <!-- Left Side -->
                <div class="col-lg-6">
                    <div class="login-left h-100">
                        <div>
                            <div class="brand-logo">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <h1 class="brand-title">GT Sport Design</h1>
                            <p class="brand-subtitle">ระบบร้านเสื้อผ้า</p>
                            
                            <div class="features">
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <strong>รายงานยอดขาย</strong><br>
                                        <small>ยอดขายแบบลไทม์</small>
                                    </div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div>
                                        <strong>ค้า</strong><br>
                                        <small>ระบบ CRM ครบครัน</small>
                                    </div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div>
                                        <strong>ค้า</strong><br>
                                        <small>สต็อกและคำร้องซื้อ</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Side -->
                <div class="col-lg-6">
                    <div class="login-right">
                        <div class="login-form">
                            <h2 class="form-title">เข้าสู่ระบบ</h2>
                            <p class="form-subtitle">ใส่ข้อมูลเพื่อเข้าสู่ระบบ</p>
                            
                            <?php if ($error_message): ?>
                                <div class="alert alert-danger" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <?php echo $error_message; ?>
                                </div>
                            <?php endif; ?>
                            
                            <form method="POST" action="">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="username" name="username" placeholder="ชื่อ้ใช้" required>
                                    <label for="username"><i class="fas fa-user me-2"></i>ชื่อ้ใช้</label>
                                </div>
                                
                                <div class="form-floating mb-4">
                                    <input type="password" class="form-control" id="password" name="password" placeholder="รหัสผ่าน" required>
                                    <label for="password"><i class="fas fa-lock me-2"></i>รหัสผ่าน</label>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="remember">
                                        <label class="form-check-label" for="remember">
                                            จดจำการเข้าสู่ระบบ
                                        </label>
                                    </div>
                                    <a href="#" class="text-decoration-none" style="color: #ee501b;">ลืมรหัสผ่าน?</a>
                                </div>
                                
                                <button type="submit" class="btn btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                                </button>
                            </form>
                            
                            <!-- Demo Info -->
                            <div class="demo-info">
                                <h6><i class="fas fa-info-circle me-2"></i>ข้อมูลทดสอบ</h6>
                                <p class="mb-2">ใช้ข้อมูลด้านล่างเพื่อเข้าสู่ระบบทดสอบ:</p>
                                <div class="demo-credentials">
                                    <strong>ชื่อ้ใช้:</strong> admin<br>
                                    <strong>รหัสผ่าน:</strong> admin123
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.remove();
                }, 500);
            });
        }, 5000);
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!username || !password) {
                e.preventDefault();
                alert('กรอกชื่อ้ใช้และรหัสผ่าน');
            }
        });
    </script>
</body>
</html>

