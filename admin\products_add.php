<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ดึงข้อมูลหมวดหมู่สินค้า
$categories = [];
try {
    $stmt = $pdo->query("SELECT * FROM product_categories WHERE status = 'active' ORDER BY sort_order, name");
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    $categories = [
        ['id' => 1, 'name' => 'เสื้อกีฬา'],
        ['id' => 2, 'name' => 'กางเกงกีฬา'],
        ['id' => 3, 'name' => 'ชุดทีม'],
        ['id' => 4, 'name' => 'อุปกรณ์เสริม']
    ];
}

// จัดการการเพิ่มสินค้า
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $category_id = (int)($_POST['category_id'] ?? 0);
    $price = (float)($_POST['price'] ?? 0);
    $cost_price = (float)($_POST['cost_price'] ?? 0);
    $stock_quantity = (int)($_POST['stock_quantity'] ?? 0);
    $min_stock_level = (int)($_POST['min_stock_level'] ?? 5);
    $sku = trim($_POST['sku'] ?? '');
    $weight = (float)($_POST['weight'] ?? 0);
    $dimensions = trim($_POST['dimensions'] ?? '');
    $material = trim($_POST['material'] ?? '');
    $care_instructions = trim($_POST['care_instructions'] ?? '');
    $status = $_POST['status'] ?? 'active';
    $is_featured = isset($_POST['is_featured']) ? 1 : 0;
    $meta_title = trim($_POST['meta_title'] ?? '');
    $meta_description = trim($_POST['meta_description'] ?? '');
    
    if ($name && $category_id > 0 && $price > 0) {
        try {
            // ตรวจสอบ SKU ซ้ำ
            if ($sku) {
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE sku = ?");
                $stmt->execute([$sku]);
                if ($stmt->fetchColumn() > 0) {
                    $error = 'รหัสสินค้า (SKU) นี้มีอยู่แล้ว';
                }
            }
            
            if (!$error) {
                $stmt = $pdo->prepare("
                    INSERT INTO products (
                        category_id, name, description, price, cost_price, 
                        stock_quantity, min_stock_level, sku, weight, dimensions,
                        material, care_instructions, status, is_featured,
                        meta_title, meta_description, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
                ");
                
                $stmt->execute([
                    $category_id, $name, $description, $price, $cost_price,
                    $stock_quantity, $min_stock_level, $sku, $weight, $dimensions,
                    $material, $care_instructions, $status, $is_featured,
                    $meta_title, $meta_description
                ]);
                
                $product_id = $pdo->lastInsertId();
                
                // จัดการการอัปโหลดรูปภาพ
                if (isset($_FILES['product_images']) && !empty($_FILES['product_images']['name'][0])) {
                    $upload_dir = '../uploads/products/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }
                    
                    foreach ($_FILES['product_images']['name'] as $key => $filename) {
                        if ($_FILES['product_images']['error'][$key] === UPLOAD_ERR_OK) {
                            $file_extension = pathinfo($filename, PATHINFO_EXTENSION);
                            $new_filename = 'product_' . $product_id . '_' . time() . '_' . $key . '.' . $file_extension;
                            $file_path = $upload_dir . $new_filename;
                            
                            if (move_uploaded_file($_FILES['product_images']['tmp_name'][$key], $file_path)) {
                                // บันทึกข้อมูลรูปภาพในฐานข้อมูล
                                try {
                                    $stmt = $pdo->prepare("
                                        INSERT INTO file_uploads (original_name, file_name, file_path, file_size, file_type, upload_type, created_at)
                                        VALUES (?, ?, ?, ?, ?, 'product', NOW())
                                    ");
                                    $stmt->execute([
                                        $filename,
                                        $new_filename,
                                        'uploads/products/' . $new_filename,
                                        $_FILES['product_images']['size'][$key],
                                        $_FILES['product_images']['type'][$key]
                                    ]);
                                    
                                    $file_id = $pdo->lastInsertId();
                                    
                                    // เชื่อมโยงรูปภาพกับสินค้า
                                    $stmt = $pdo->prepare("
                                        INSERT INTO product_images (product_id, file_id, image_type, sort_order, created_at)
                                        VALUES (?, ?, ?, ?, NOW())
                                    ");
                                    $image_type = ($key === 0) ? 'main' : 'gallery';
                                    $stmt->execute([$product_id, $file_id, $image_type, $key]);
                                } catch (Exception $e) {
                                    // ถ้าไม่มีตารางรูปภาพ ก็ข้าม
                                }
                            }
                        }
                    }
                }
                
                $success = 'เพิ่มสินค้าเรียบร้อยแล้ว';
                
                // รีเฟรชหน้า
                header('Location: products_add.php?success=1');
                exit();
            }
        } catch (Exception $e) {
            $error = 'ไม่สามารถเพิ่มสินค้าได้: ' . $e->getMessage();
        }
    } else {
        $error = 'กรุณากรอกข้อมูลที่จำเป็น (ชื่อสินค้า, หมวดหมู่, ราคา)';
    }
}

// แสดงข้อความ
if (isset($_GET['success'])) {
    $success = 'เพิ่มสินค้าเรียบร้อยแล้ว';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เพิ่มสินค้าใหม่ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
        .form-control:focus {
            border-color: #ee501b;
            box-shadow: 0 0 0 0.2rem rgba(238, 80, 27, 0.25);
        }
        .image-preview {
            max-width: 100px;
            max-height: 100px;
            margin: 5px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php" class="active"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">เพิ่มสินค้าใหม่</h4>
                <small class="text-muted">เพิ่มสินค้าใหม่เข้าสู่ระบบ</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="products.php">จัดการสินค้า</a></li>
                    <li class="breadcrumb-item active">เพิ่มสินค้าใหม่</li>
                </ol>
            </nav>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    <div class="mt-2">
                        <a href="products.php" class="btn btn-sm btn-outline-success">ดูรายการสินค้า</a>
                        <a href="products_add.php" class="btn btn-sm btn-outline-primary">เพิ่มสินค้าอีก</a>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Product Form -->
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <!-- ข้อมูลพื้นฐาน -->
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">ข้อมูลพื้นฐาน</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label class="form-label">ชื่อสินค้า <span class="text-danger">*</span></label>
                                            <input type="text" name="name" class="form-control" required>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">รหัสสินค้า (SKU)</label>
                                            <input type="text" name="sku" class="form-control" placeholder="เช่น GT-001">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">คำอธิบายสินค้า</label>
                                    <textarea name="description" class="form-control" rows="4" placeholder="รายละเอียดสินค้า คุณสมบัติ วัสดุ ฯลฯ"></textarea>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                            <select name="category_id" class="form-select" required>
                                                <option value="">เลือกหมวดหมู่</option>
                                                <?php foreach ($categories as $category): ?>
                                                    <option value="<?php echo $category['id']; ?>">
                                                        <?php echo htmlspecialchars($category['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">สถานะ</label>
                                            <select name="status" class="form-select">
                                                <option value="active">ใช้งาน</option>
                                                <option value="inactive">ปิดใช้งาน</option>
                                                <option value="draft">แบบร่าง</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ข้อมูลราคาและสต็อก -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">ราคาและสต็อก</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">ราคาขาย <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">฿</span>
                                                <input type="number" name="price" class="form-control" step="0.01" min="0" required>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">ราคาต้นทุน</label>
                                            <div class="input-group">
                                                <span class="input-group-text">฿</span>
                                                <input type="number" name="cost_price" class="form-control" step="0.01" min="0">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">จำนวนสต็อก</label>
                                            <input type="number" name="stock_quantity" class="form-control" min="0" value="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">สต็อกขั้นต่ำ</label>
                                            <input type="number" name="min_stock_level" class="form-control" min="0" value="5">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ข้อมูลเพิ่มเติม -->
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">ข้อมูลเพิ่มเติม</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">น้ำหนัก (กรัม)</label>
                                            <input type="number" name="weight" class="form-control" step="0.1" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">ขนาด</label>
                                            <input type="text" name="dimensions" class="form-control" placeholder="เช่น S, M, L, XL">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">วัสดุ</label>
                                    <input type="text" name="material" class="form-control" placeholder="เช่น ผ้าโพลีเอสเตอร์ 100%">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">วิธีการดูแล</label>
                                    <textarea name="care_instructions" class="form-control" rows="2" placeholder="เช่น ซักด้วยน้ำเย็น ห้ามใช้น้ำยาฟอกขาว"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- รูปภาพและการตั้งค่า -->
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">รูปภาพสินค้า</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">อัปโหลดรูปภาพ</label>
                                    <input type="file" name="product_images[]" class="form-control" multiple accept="image/*" onchange="previewImages(this)">
                                    <small class="text-muted">รองรับไฟล์ JPG, PNG, GIF (รูปแรกจะเป็นรูปหลัก)</small>
                                </div>
                                <div id="imagePreview" class="mt-2"></div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">การตั้งค่า</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured">
                                        <label class="form-check-label" for="is_featured">
                                            สินค้าแนะนำ (แสดงในหน้าแรก)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">SEO</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Meta Title</label>
                                    <input type="text" name="meta_title" class="form-control">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Meta Description</label>
                                    <textarea name="meta_description" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ปุ่มบันทึก -->
                <div class="d-flex justify-content-between mt-4">
                    <a href="products.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>ยกเลิก
                    </a>
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>บันทึกสินค้า
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImages(input) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = '';
            
            if (input.files) {
                Array.from(input.files).forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'image-preview';
                        img.title = file.name + (index === 0 ? ' (รูปหลัก)' : '');
                        preview.appendChild(img);
                    };
                    reader.readAsDataURL(file);
                });
            }
        }
    </script>
</body>
</html>
