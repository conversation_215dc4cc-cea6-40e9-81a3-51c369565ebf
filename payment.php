<?php
require_once './config/database.php';
$pdo = getDbConnection();

session_start();

$order_number = $_GET['order'] ?? $_SESSION['order_number'] ?? null;

if (!$order_number) {
    header("Location: index.php");
    exit;
}

// ดึงข้อมูลคำสั่งซื้อ
$stmt = $pdo->prepare("
    SELECT
        o.*,
        c.name as customer_name,
        c.email as customer_email,
        c.phone as customer_phone,
        c.address as customer_address,
        d.design_name,
        d.preview_image,
        p.name as product_name
    FROM orders o
    LEFT JOIN customers c ON o.customer_id = c.id
    LEFT JOIN designs d ON o.design_id = d.id
    LEFT JOIN products p ON d.product_id = p.id
    WHERE o.order_number = ?
");
$stmt->execute([$order_number]);
$order = $stmt->fetch();

if (!$order) {
    header("Location: index.php");
    exit;
}

// ข้อมูล PromptPay
$promptpay_id = "0855599164"; // เบอร์โทรศัพท์ที่ลงทะเบียน PromptPay
$amount = $order['total_amount'];

// ฟังก์ชันสร้าง PromptPay QR Code
function generatePromptPayQR($promptpay_id, $amount) {
    // ใช้ API ภายนอกสำหรับสร้าง QR Code
    $api_url = "https://api.qrserver.com/v1/create-qr-code/";

    // สร้าง payload สำหรับ PromptPay
    $payload = buildPromptPayPayload($promptpay_id, $amount);

    $params = [
        'size' => '300x300',
        'data' => $payload,
        'format' => 'png'
    ];

    return $api_url . '?' . http_build_query($params);
}

function buildPromptPayPayload($promptpay_id, $amount) {
    // สร้าง payload ตาม EMVCo Specification (simplified version)
    $payload = "00020101021129370016A000000677010111";
    $payload .= "0213" . $promptpay_id;
    $payload .= "54047642"; // Currency: THB
    $payload .= "5802TH"; // Country: Thailand

    if ($amount > 0) {
        $amount_str = number_format($amount, 2, '.', '');
        $amount_length = sprintf("%02d", strlen($amount_str));
        $payload .= "53" . $amount_length . $amount_str;
    }

    // คำนวณ checksum (simplified)
    $checksum = sprintf("%04X", crc16($payload . "6304"));
    $payload .= "6304" . $checksum;

    return $payload;
}

function crc16($data) {
    $crc = 0xFFFF;
    for ($i = 0; $i < strlen($data); $i++) {
        $crc ^= ord($data[$i]) << 8;
        for ($j = 0; $j < 8; $j++) {
            if ($crc & 0x8000) {
                $crc = ($crc << 1) ^ 0x1021;
            } else {
                $crc = $crc << 1;
            }
        }
    }
    return $crc & 0xFFFF;
}

// จัดการการอัพโหลดหลักฐานการชำระเงิน
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['payment_slip'])) {
    $upload_dir = '../uploads/payment_slips/';

    // สร้างโฟลเดอร์ถ้าไม่มี
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    $file = $_FILES['payment_slip'];
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'pdf'];

    if (in_array($file_extension, $allowed_extensions) && $file['size'] <= 5000000) { // 5MB limit
        $new_filename = $order_number . '_' . time() . '.' . $file_extension;
        $upload_path = $upload_dir . $new_filename;

        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
            // บันทึกข้อมูลการชำระเงิน
            $stmt = $pdo->prepare("
                UPDATE orders
                SET payment_slip = ?, payment_status = 'pending_verification', updated_at = NOW()
                WHERE order_number = ?
            ");
            $stmt->execute([$new_filename, $order_number]);

            $success = "อัพโหลดหลักฐานการชำระเงินสำเร็จ เราจะตรวจสอบและแจ้งให้ทราบภายใน 1-2 ชั่วโมง";
        } else {
            $error = "เกิดข้อผิดพลาดในการอัพโหลดไฟล์";
        }
    } else {
        $error = "ไฟล์ไม่ถูกต้อง อนุญาตเฉพาะ JPG, PNG, PDF ขนาดไม่เกิน 5MB";
    }
}

$qr_code_url = generatePromptPayQR($promptpay_id, $amount);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>การชำระเงิน - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .payment-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .card-header {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 20px 30px;
        }

        .card-body {
            padding: 30px;
        }

        .payment-status {
            text-align: center;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
        }

        .payment-status.pending {
            background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
            color: white;
        }

        .payment-status.completed {
            background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
            color: white;
        }

        .qr-code-section {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .qr-code {
            border: 3px solid #11be97;
            border-radius: 15px;
            padding: 20px;
            background: white;
            display: inline-block;
            margin-bottom: 20px;
        }

        .bank-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .bank-account {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #11be97;
        }

        .bank-logo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #11be97;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 15px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 2px dashed #11be97;
            margin-top: 30px;
        }

        .upload-section.dragover {
            background: rgba(17, 190, 151, 0.1);
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(17, 190, 151, 0.3);
        }

        .order-summary {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-row:last-child {
            border-bottom: none;
            font-weight: 600;
            color: #11be97;
        }

        .steps {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 20px;
            color: #6c757d;
        }

        .step.active {
            color: #11be97;
            font-weight: 600;
        }

        .step.completed {
            color: #28a745;
            font-weight: 600;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: 600;
        }

        .step.active .step-number {
            background: #11be97;
            color: white;
        }

        .step.completed .step-number {
            background: #28a745;
            color: white;
        }

        .timer {
            font-size: 1.2em;
            font-weight: 600;
            color: #ff6b35;
        }

        .payment-methods {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
        }

        .payment-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: rgba(255,255,255,0.9);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .payment-tab.active {
            background: white;
            border-color: #11be97;
            box-shadow: 0 5px 15px rgba(17, 190, 151, 0.2);
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h2 class="text-white mb-3">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design
            </h2>
            <div class="steps">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <span>ออกแบบ</span>
                </div>
                <div class="step active">
                    <div class="step-number">2</div>
                    <span>ชำระเงิน</span>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <span>ดำเนินการ</span>
                </div>
            </div>
        </div>

        <!-- สถานะการชำระเงิน -->
        <div class="payment-status <?= $order['payment_status'] === 'completed' ? 'completed' : 'pending' ?>">
            <?php if ($order['payment_status'] === 'completed'): ?>
                <i class="fas fa-check-circle fa-3x mb-3"></i>
                <h4>ชำระเงินสำเร็จ</h4>
                <p class="mb-0">เราได้รับการชำระเงินแล้ว และกำลังดำเนินการผลิตสินค้า</p>
            <?php elseif ($order['payment_status'] === 'pending_verification'): ?>
                <i class="fas fa-clock fa-3x mb-3"></i>
                <h4>รอตรวจสอบการชำระเงิน</h4>
                <p class="mb-0">เราได้รับหลักฐานการชำระเงินแล้ว กำลังตรวจสอบภายใน 1-2 ชั่วโมง</p>
            <?php else: ?>
                <i class="fas fa-credit-card fa-3x mb-3"></i>
                <h4>รอการชำระเงิน</h4>
                <p class="mb-0">กรุณาชำระเงินภายใน <span class="timer" id="timer">30:00</span> นาที</p>
            <?php endif; ?>
        </div>

        <?php if (isset($success)): ?>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle me-2"></i><?= htmlspecialchars($success) ?>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- ข้อมูลคำสั่งซื้อ -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>คำสั่งซื้อ</h5>
                    </div>
                    <div class="card-body">
                        <div class="order-summary">
                            <div class="info-row">
                                <span>เลขคำสั่งซื้อ</span>
                                <span class="fw-bold"><?= htmlspecialchars($order['order_number']) ?></span>
                            </div>
                            <div class="info-row">
                                <span>ชื่อการออกแบบ</span>
                                <span><?= htmlspecialchars($order['design_name']) ?></span>
                            </div>
                            <div class="info-row">
                                <span>สินค้า</span>
                                <span><?= htmlspecialchars($order['product_name']) ?></span>
                            </div>
                            <div class="info-row">
                                <span>จำนวน</span>
                                <span><?= (int)$order['quantity'] ?> ตัว</span>
                            </div>
                            <div class="info-row">
                                <span>ลูกค้า</span>
                                <span><?= htmlspecialchars($order['customer_name']) ?></span>
                            </div>
                            <div class="info-row">
                                <span>ยอดรวม</span>
                                <span><?= number_format($order['total_amount']) ?> บาท</span>
                            </div>
                        </div>

                        <div class="mt-3 text-center">
                            <?php if ($order['preview_image']): ?>
                                <img src="<?= htmlspecialchars($order['preview_image']) ?>"
                                     alt="Design Preview" class="img-fluid rounded" style="max-height: 150px;">
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- การชำระเงิน -->
            <div class="col-md-8">
                <!-- เลือกวิธีการชำระเงิน -->
                <div class="payment-methods">
                    <div class="payment-tab active" onclick="showPaymentMethod('promptpay')">
                        <i class="fas fa-qrcode fa-2x mb-2 text-success"></i>
                        <h6>PromptPay QR</h6>
                        <small class="text-muted">สแกนจ่าย</small>
                    </div>
                    <div class="payment-tab" onclick="showPaymentMethod('bank')">
                        <i class="fas fa-university fa-2x mb-2 text-primary"></i>
                        <h6>โอนธนาคาร</h6>
                        <small class="text-muted">แอปธนาคาร</small>
                    </div>
                </div>

                <!-- PromptPay QR Code -->
                <div id="promptpay-section" class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-qrcode me-2"></i>PromptPay QR Code</h5>
                    </div>
                    <div class="card-body">
                        <div class="qr-code-section">
                            <h6 class="mb-3">สแกน QR Code เพื่อชำระเงิน</h6>
                            <div class="qr-code">
                                <img src="<?= $qr_code_url ?>" alt="PromptPay QR Code" width="250" height="250">
                            </div>
                            <p class="mb-2"><strong>ยอดที่ต้องชำระ: <?= number_format($amount) ?> บาท</strong></p>
                            <p class="text-muted">
                                1. เปิดแอปธนาคารที่รองรับ PromptPay<br>
                                2. สแกน QR Code ด้านบน<br>
                                3. ตรวจสอบยอดเงินและชำระ<br>
                                4. อัพโหลดหลักฐานการชำระเงิน
                            </p>
                        </div>
                    </div>
                </div>

                <!-- โอนธนาคาร -->
                <div id="bank-section" class="card" style="display: none;">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-university me-2"></i>โอนผ่านธนาคาร</h5>
                    </div>
                    <div class="card-body">
                        <div class="bank-info">
                            <h6 class="mb-3">เลือกบัญชีธนาคารที่ต้องการโอน</h6>

                            <div class="bank-account">
                                <div class="bank-logo">
                                    <i class="fas fa-landmark"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">ธนาคารกสิกรไทย</h6>
                                    <p class="mb-1">เลขที่บัญชี: <strong>123-4-56789-0</strong></p>
                                    <p class="mb-0 text-muted">ชื่อบัญชี: GT Sport Design Co., Ltd.</p>
                                </div>
                                <button class="btn btn-outline-primary btn-sm" onclick="copyAccount('123-4-56789-0')">
                                    <i class="fas fa-copy"></i> คัดลอก
                                </button>
                            </div>

                            <div class="bank-account">
                                <div class="bank-logo bg-primary">
                                    <i class="fas fa-landmark"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">ธนาคารกรุงไทย</h6>
                                    <p class="mb-1">เลขที่บัญชี: <strong>987-6-54321-0</strong></p>
                                    <p class="mb-0 text-muted">ชื่อบัญชี: GT Sport Design Co., Ltd.</p>
                                </div>
                                <button class="btn btn-outline-primary btn-sm" onclick="copyAccount('987-6-54321-0')">
                                    <i class="fas fa-copy"></i> คัดลอก
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>วิธีการโอนเงิน</h6>
                            <ol class="mb-0">
                                <li>เปิดแอปธนาคารของคุณ</li>
                                <li>เลือกโอนเงิน และกรอกเลขบัญชีด้านบน</li>
                                <li>ใส่จำนวนเงิน <strong><?= number_format($amount) ?> บาท</strong></li>
                                <li>โอนเงินและถ่ายภาพหลักฐาน</li>
                                <li>อัพโหลดหลักฐานการโอนด้านล่าง</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <!-- อัพโหลดหลักฐานการชำระเงิน -->
                <?php if ($order['payment_status'] !== 'completed'): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-upload me-2"></i>อัพโหลดหลักฐานการชำระเงิน</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data">
                                <div class="upload-section" id="upload-area">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h6>ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์</h6>
                                    <p class="text-muted mb-3">รองรับไฟล์ JPG, PNG, PDF ขนาดไม่เกิน 5MB</p>

                                    <input type="file" id="payment_slip" name="payment_slip" class="file-input"
                                           accept=".jpg,.jpeg,.png,.pdf" required>
                                    <label for="payment_slip" class="upload-btn">
                                        <i class="fas fa-plus me-2"></i>เลือกไฟล์
                                    </label>

                                    <div id="file-preview" style="display: none;" class="mt-3">
                                        <p class="mb-2">ไฟล์ที่เลือก:</p>
                                        <div id="file-info" class="bg-white p-3 rounded"></div>
                                        <button type="submit" class="btn btn-success mt-3">
                                            <i class="fas fa-upload me-2"></i>อัพโหลดหลักฐาน
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Chat Widget -->
    <?php include 'chat_widget.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Timer สำหรับการชำระเงิน (30 นาที)
        let timeLeft = 30 * 60; // 30 minutes in seconds

        function updateTimer() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            const timerElement = document.getElementById('timer');

            if (timerElement) {
                timerElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;

                if (timeLeft <= 0) {
                    timerElement.textContent = "หมดเวลา";
                    timerElement.classList.add('text-danger');
                } else if (timeLeft <= 300) { // 5 minutes
                    timerElement.classList.add('text-danger');
                }
            }

            if (timeLeft > 0) {
                timeLeft--;
                setTimeout(updateTimer, 1000);
            }
        }

        // เริ่ม timer
        updateTimer();

        // การเปลี่ยนวิธีการชำระเงิน
        function showPaymentMethod(method) {
            // ซ่อนทุก section
            document.getElementById('promptpay-section').style.display = 'none';
            document.getElementById('bank-section').style.display = 'none';

            // แสดง section ที่เลือก
            document.getElementById(method + '-section').style.display = 'block';

            // อัพเดต tab
            document.querySelectorAll('.payment-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.closest('.payment-tab').classList.add('active');
        }

        // คัดลอกเลขบัญชี
        function copyAccount(accountNumber) {
            navigator.clipboard.writeText(accountNumber).then(() => {
                alert('คัดลอกเลขบัญชีแล้ว: ' + accountNumber);
            });
        }

        // การอัพโหลดไฟล์
        const uploadArea = document.getElementById('upload-area');
        const fileInput = document.getElementById('payment_slip');
        const filePreview = document.getElementById('file-preview');
        const fileInfo = document.getElementById('file-info');

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showFilePreview(files[0]);
            }
        });

        // เมื่อเลือกไฟล์
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                showFilePreview(e.target.files[0]);
            }
        });

        function showFilePreview(file) {
            const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB
            const fileType = file.type;

            fileInfo.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-file-image fa-2x text-primary me-3"></i>
                    <div>
                        <h6 class="mb-1">${file.name}</h6>
                        <small class="text-muted">ขนาด: ${fileSize} MB | ประเภท: ${fileType}</small>
                    </div>
                </div>
            `;

            filePreview.style.display = 'block';
        }

        // ตรวจสอบสถานะการชำระเงินทุก 30 วินาที
        setInterval(() => {
            fetch('check_payment_status.php?order=<?= urlencode($order_number) ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'completed') {
                        location.reload();
                    }
                })
                .catch(error => console.error('Error:', error));
        }, 30000);
    </script>
</body>
</html>
