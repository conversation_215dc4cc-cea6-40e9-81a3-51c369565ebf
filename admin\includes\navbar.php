<nav class="admin-navbar">
    <!-- Left side -->
    <div class="navbar-left">
        <button class="navbar-btn d-lg-none" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
        <h1 class="navbar-title"><?php echo $page_title ?? 'แดชบอร์ด'; ?></h1>
        <div class="navbar-subtitle">
            <i class="fas fa-clock me-1"></i>
            <span id="currentDateTime"><?php echo date('d/m/Y H:i'); ?></span>
        </div>
    </div>

    <!-- Right side -->
    <div class="navbar-right">
        <!-- Quick Search -->
        <div class="navbar-item d-none d-md-block">
            <div class="input-group" style="width: 250px;">
                <input type="text" class="form-control form-control-sm" placeholder="ค้นหา..." id="quickSearch">
                <button class="btn btn-outline-secondary btn-sm" type="button">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <!-- Notifications -->
        <div class="navbar-item dropdown">
            <button class="navbar-btn" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-bell"></i>
                <span class="notification-badge">3</span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                <li class="dropdown-header d-flex justify-content-between align-items-center">
                    <span>การแจ้งเตือน</span>
                    <span class="badge bg-primary">3</span>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" href="orders.php">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-shopping-cart text-success"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-bold">คำสั่งซื้อใหม่ #001</div>
                                <small class="text-muted">คุณสมชาย สั่งซื้อเสื้อยืด</small>
                            </div>
                            <small class="text-muted">5 นาที</small>
                        </div>
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="chat.php">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-comment text-info"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-bold">ข้อความใหม่</div>
                                <small class="text-muted">คุณสมหญิง ส่งข้อความ</small>
                            </div>
                            <small class="text-muted">10 นาที</small>
                        </div>
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="products_stock.php">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-bold">สินค้าใกล้หมด</div>
                                <small class="text-muted">เสื้อยืดสีขาว เหลือ 5 ตัว</small>
                            </div>
                            <small class="text-muted">1 ชม.</small>
                        </div>
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-center" href="notifications.php">ดูทั้งหมด</a></li>
            </ul>
        </div>

        <!-- Messages -->
        <div class="navbar-item dropdown">
            <button class="navbar-btn" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-envelope"></i>
                <span class="notification-badge">2</span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                <li class="dropdown-header d-flex justify-content-between align-items-center">
                    <span>ข้อความ</span>
                    <span class="badge bg-info">2</span>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" href="chat.php">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="user-avatar" style="width: 35px; height: 35px; font-size: 0.8rem;">ส</div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-bold">คุณสมชาย</div>
                                <small class="text-muted">สอบถามราคาเสื้อทีม</small>
                            </div>
                            <small class="text-muted">5 นาที</small>
                        </div>
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" href="chat.php">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="user-avatar" style="width: 35px; height: 35px; font-size: 0.8rem;">ห</div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-bold">คุณสมหญิง</div>
                                <small class="text-muted">ขอดูตัวอย่างการออกแบบ</small>
                            </div>
                            <small class="text-muted">15 นาที</small>
                        </div>
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-center" href="chat.php">ดูทั้งหมด</a></li>
            </ul>
        </div>

        <!-- User Menu -->
        <div class="navbar-item dropdown">
            <button class="navbar-btn d-flex align-items-center" type="button" data-bs-toggle="dropdown">
                <div class="user-info me-2">
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($admin_name ?? 'A', 0, 1)); ?>
                    </div>
                    <div class="user-details d-none d-lg-block">
                        <div class="user-name"><?php echo $admin_name ?? 'ผู้ดูแลระบบ'; ?></div>
                        <div class="user-role">
                            <?php
                            $role_text = [
                                'super_admin' => 'ผู้ดูแลระบบสูงสุด',
                                'admin' => 'ผู้ดูแลระบบ',
                                'staff' => 'พนักงาน'
                            ];
                            echo $role_text[$admin_role ?? 'admin'] ?? 'ผู้ดูแลระบบ';
                            ?>
                        </div>
                    </div>
                </div>
                <i class="fas fa-chevron-down"></i>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li class="dropdown-header">
                    <div class="text-center">
                        <div class="user-avatar mx-auto mb-2" style="width: 50px; height: 50px; font-size: 1.2rem;">
                            <?php echo strtoupper(substr($admin_name ?? 'A', 0, 1)); ?>
                        </div>
                        <div class="fw-bold"><?php echo $admin_name ?? 'ผู้ดูแลระบบ'; ?></div>
                        <small class="text-muted"><?php echo $admin_role ?? 'admin'; ?></small>
                    </div>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="profile.php"><i class="fas fa-user me-2"></i>โปรไฟล์</a></li>
                <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>ตั้งค่า</a></li>
                <li><a class="dropdown-item" href="admin_guide.php"><i class="fas fa-book me-2"></i>คู่มือการใช้งาน</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item text-danger" href="logout.php" onclick="return confirm('ต้องการออกจากระบบหรือไม่?')"><i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ</a></li>
            </ul>
        </div>
    </div>
</nav>

<script>
// Update current time
function updateDateTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    };
    const dateTimeString = now.toLocaleDateString('th-TH', options);
    const element = document.getElementById('currentDateTime');
    if (element) {
        element.textContent = dateTimeString;
    }
}

// Update time every minute
updateDateTime();
setInterval(updateDateTime, 60000);

// Sidebar toggle for mobile
document.getElementById('sidebarToggle')?.addEventListener('click', function() {
    const sidebar = document.getElementById('adminSidebar');
    sidebar.classList.toggle('show');
});

// Quick search functionality
document.getElementById('quickSearch')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const query = this.value.trim();
        if (query) {
            // Implement search functionality
            console.log('Searching for:', query);
            // You can redirect to a search page or implement AJAX search
        }
    }
});
</script>
