<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

// ดึงข้อมูลรีวิว
$reviews = [];
$total_reviews = 0;
$error = '';

try {
    // ลองดึงจากตาราง reviews
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM reviews");
        $total_reviews = $stmt->fetchColumn();
        
        $stmt = $pdo->query("
            SELECT r.*, c.name as customer_name, p.name as product_name
            FROM reviews r
            LEFT JOIN customers c ON r.customer_id = c.id
            LEFT JOIN products p ON r.product_id = p.id
            ORDER BY r.created_at DESC
            LIMIT 20
        ");
        $reviews = $stmt->fetchAll();
    } catch (Exception $e) {
        // ถ้าไม่มีตาราง reviews ให้สร้างข้อมูลจำลอง
        $reviews = [
            [
                'id' => 1,
                'customer_name' => 'นาย ก ใจดี',
                'product_name' => 'เสื้อกีฬา Syntex โปโล',
                'rating' => 5,
                'comment' => 'คุณภาพดีมาก ผ้านุ่ม ใส่สบาย แนะนำเลยครับ',
                'status' => 'approved',
                'created_at' => date('Y-m-d H:i:s'),
                'is_featured' => 1
            ],
            [
                'id' => 2,
                'customer_name' => 'นางสาว ข สวยงาม',
                'product_name' => 'ชุดทีมฟุตบอล',
                'rating' => 4,
                'comment' => 'ชุดสวย คุณภาพดี แต่ส่งช้าหน่อย',
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'is_featured' => 0
            ],
            [
                'id' => 3,
                'customer_name' => 'นาย ค มีสุข',
                'product_name' => 'เสื้อกีฬา Dri-FIT',
                'rating' => 5,
                'comment' => 'ประทับใจมาก ระบายอากาศดี เหงื่อแห้งเร็ว',
                'status' => 'approved',
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'is_featured' => 1
            ]
        ];
        $total_reviews = count($reviews);
    }
} catch (Exception $e) {
    $error = 'เกิดข้อผิดพลาดในการโหลดข้อมูล: ' . $e->getMessage();
}

// จัดการการอัปเดตสถานะรีวิว
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $review_id = (int)($_POST['review_id'] ?? 0);
    
    if ($action === 'update_status' && $review_id > 0) {
        $new_status = $_POST['status'] ?? '';
        $is_featured = isset($_POST['is_featured']) ? 1 : 0;
        
        try {
            $stmt = $pdo->prepare("
                UPDATE reviews 
                SET status = ?, is_featured = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $is_featured, $review_id]);
            
            header('Location: reviews.php?success=1');
            exit();
        } catch (Exception $e) {
            $error = 'ไม่สามารถอัปเดตสถานะได้: ' . $e->getMessage();
        }
    }
}

function getStars($rating) {
    $stars = '';
    for ($i = 1; $i <= 5; $i++) {
        if ($i <= $rating) {
            $stars .= '<i class="fas fa-star text-warning"></i>';
        } else {
            $stars .= '<i class="far fa-star text-muted"></i>';
        }
    }
    return $stars;
}

function getStatusBadge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning">รอตรวจสอบ</span>',
        'approved' => '<span class="badge bg-success">อนุมัติ</span>',
        'rejected' => '<span class="badge bg-danger">ปฏิเสธ</span>'
    ];
    return $badges[$status] ?? '<span class="badge bg-secondary">' . $status . '</span>';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการรีวิว - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .review-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        .review-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .review-card.featured {
            border-left: 5px solid #ffc107;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php" class="active"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">จัดการรีวิว</h4>
                <small class="text-muted">ดูและจัดการรีวิวจากลูกค้า</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>อัปเดตข้อมูลเรียบร้อยแล้ว
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $total_reviews; ?></h3>
                            <p class="mb-0">รีวิวทั้งหมด</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo count(array_filter($reviews, fn($r) => $r['status'] === 'pending')); ?></h3>
                            <p class="mb-0">รอตรวจสอบ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo count(array_filter($reviews, fn($r) => $r['status'] === 'approved')); ?></h3>
                            <p class="mb-0">อนุมัติแล้ว</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?php echo count(array_filter($reviews, fn($r) => $r['is_featured'] == 1)); ?></h3>
                            <p class="mb-0">รีวิวเด่น</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reviews List -->
            <?php if (empty($reviews)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                    <h3>ยังไม่มีรีวิว</h3>
                    <p class="text-muted">เมื่อลูกค้าส่งรีวิวเข้ามา จะแสดงที่นี่</p>
                </div>
            <?php else: ?>
                <?php foreach ($reviews as $review): ?>
                <div class="review-card <?php echo $review['is_featured'] ? 'featured' : ''; ?>">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="mb-1"><?php echo htmlspecialchars($review['customer_name'] ?? 'ลูกค้า'); ?></h6>
                                    <small class="text-muted"><?php echo htmlspecialchars($review['product_name'] ?? 'สินค้า'); ?></small>
                                </div>
                                <div class="text-end">
                                    <?php echo getStars($review['rating']); ?>
                                    <small class="text-muted d-block"><?php echo date('d/m/Y H:i', strtotime($review['created_at'])); ?></small>
                                </div>
                            </div>
                            <p class="mb-2"><?php echo htmlspecialchars($review['comment']); ?></p>
                            <div class="d-flex gap-2">
                                <?php echo getStatusBadge($review['status']); ?>
                                <?php if ($review['is_featured']): ?>
                                    <span class="badge bg-warning"><i class="fas fa-star me-1"></i>รีวิวเด่น</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#reviewModal<?php echo $review['id']; ?>">
                                <i class="fas fa-edit me-1"></i>จัดการ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Review Modal -->
                <div class="modal fade" id="reviewModal<?php echo $review['id']; ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">จัดการรีวิว</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form method="POST">
                                <div class="modal-body">
                                    <input type="hidden" name="action" value="update_status">
                                    <input type="hidden" name="review_id" value="<?php echo $review['id']; ?>">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">สถานะ</label>
                                        <select name="status" class="form-select" required>
                                            <option value="pending" <?php echo $review['status'] === 'pending' ? 'selected' : ''; ?>>รอตรวจสอบ</option>
                                            <option value="approved" <?php echo $review['status'] === 'approved' ? 'selected' : ''; ?>>อนุมัติ</option>
                                            <option value="rejected" <?php echo $review['status'] === 'rejected' ? 'selected' : ''; ?>>ปฏิเสธ</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="is_featured" id="featured<?php echo $review['id']; ?>" 
                                                   <?php echo $review['is_featured'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="featured<?php echo $review['id']; ?>">
                                                ตั้งเป็นรีวิวเด่น (แสดงในหน้าแรก)
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-info">
                                        <strong>รีวิวนี้:</strong><br>
                                        <strong>ลูกค้า:</strong> <?php echo htmlspecialchars($review['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                        <strong>สินค้า:</strong> <?php echo htmlspecialchars($review['product_name'] ?? 'ไม่ระบุ'); ?><br>
                                        <strong>คะแนน:</strong> <?php echo $review['rating']; ?>/5 ดาว<br>
                                        <strong>ความคิดเห็น:</strong> <?php echo htmlspecialchars($review['comment']); ?>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                    <button type="submit" class="btn btn-primary">บันทึก</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
