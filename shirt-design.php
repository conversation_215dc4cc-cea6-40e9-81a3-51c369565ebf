<?php
session_start();

// ข้อมูลแบบเสื้อ
$shirt_templates = [
    [
        'id' => 1,
        'name' => 'เสื้อกีฬาแขนสั้น',
        'image' => 'assets/images/templates/short-sleeve.png',
        'category' => 'sport',
        'price' => 350
    ],
    [
        'id' => 2,
        'name' => 'เสื้อกีฬาแขนยาว',
        'image' => 'assets/images/templates/long-sleeve.png',
        'category' => 'sport',
        'price' => 380
    ],
    [
        'id' => 3,
        'name' => 'เสื้อโปโล',
        'image' => 'assets/images/templates/polo.png',
        'category' => 'company',
        'price' => 320
    ],
    [
        'id' => 4,
        'name' => 'เสื้อยืดคอกลม',
        'image' => 'assets/images/templates/round-neck.png',
        'category' => 'activity',
        'price' => 280
    ]
];

// สีเสื้อ
$shirt_colors = [
    ['name' => 'แดง', 'code' => '#dc3545', 'value' => 'red'],
    ['name' => 'น้ำเงิน', 'code' => '#0d6efd', 'value' => 'blue'],
    ['name' => 'เขียว', 'code' => '#198754', 'value' => 'green'],
    ['name' => 'เหลือง', 'code' => '#ffc107', 'value' => 'yellow'],
    ['name' => 'ส้ม', 'code' => '#fd7e14', 'value' => 'orange'],
    ['name' => 'ม่วง', 'code' => '#6f42c1', 'value' => 'purple'],
    ['name' => 'ขาว', 'code' => '#ffffff', 'value' => 'white'],
    ['name' => 'ดำ', 'code' => '#000000', 'value' => 'black']
];

// ไซส์เสื้อ
$shirt_sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'];

$selected_template = $_GET['template'] ?? 1;
$selected_color = $_GET['color'] ?? 'red';
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ออกแบบเสื้อ - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="ออกแบบเสื้อกีฬาด้วยตนเอง ง่าย สะดวก ฟรี! พร้อมเครื่องมือออกแบบที่ทันสมัย">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/banner.css" rel="stylesheet">
    
    <style>
        /* Design Page Styles */
        .design-hero {
            background: var(--gradient-primary);
            color: white;
            padding: 100px 0 60px;
            position: relative;
            overflow: hidden;
        }
        
        .design-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,0 1000,300 1000,1000 0,700"/></svg>');
            background-size: cover;
        }
        
        .design-workspace {
            padding: 40px 0;
            min-height: 80vh;
        }
        
        .design-panel {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            position: sticky;
            top: 100px;
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }
        
        .canvas-area {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            min-height: 600px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .shirt-preview {
            width: 300px;
            height: 350px;
            background: #f8f9fa;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .shirt-preview:hover {
            transform: scale(1.05);
        }
        
        .shirt-svg {
            width: 250px;
            height: 300px;
            transition: all 0.3s ease;
        }
        
        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .template-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .template-item:hover,
        .template-item.active {
            border-color: var(--primary-color);
            background: var(--light-gray);
            transform: translateY(-2px);
        }
        
        .template-item img {
            width: 60px;
            height: 70px;
            object-fit: contain;
            margin-bottom: 10px;
        }
        
        .color-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 30px;
        }
        
        .color-item {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .color-item:hover,
        .color-item.active {
            border-color: var(--primary-color);
            transform: scale(1.1);
        }
        
        .color-item.active::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .size-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 30px;
        }
        
        .size-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }
        
        .size-item:hover,
        .size-item.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }
        
        .design-tools {
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
            margin-top: 20px;
        }
        
        .tool-section {
            margin-bottom: 25px;
        }
        
        .tool-section h6 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .text-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .text-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .upload-area {
            border: 2px dashed #e9ecef;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background: var(--light-gray);
        }
        
        .upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(238, 80, 27, 0.1);
        }
        
        .price-summary {
            background: var(--light-gray);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .price-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        
        .price-total {
            border-top: 2px solid var(--primary-color);
            padding-top: 15px;
            margin-top: 15px;
            font-weight: 700;
            font-size: 1.2rem;
        }
        
        .btn-design-action {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-design-action:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(238, 80, 27, 0.4);
            color: white;
        }
        
        .design-preview-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-weight: bold;
            font-size: 18px;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 10;
        }
        
        .design-preview-logo {
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translate(-50%, -50%);
            max-width: 80px;
            max-height: 80px;
            z-index: 10;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .design-hero {
                padding: 80px 0 40px;
            }
            
            .design-panel {
                position: static;
                max-height: none;
            }
            
            .shirt-preview {
                width: 250px;
                height: 300px;
            }
            
            .shirt-svg {
                width: 200px;
                height: 250px;
            }
            
            .template-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .color-grid {
                grid-template-columns: repeat(4, 1fr);
            }
            
            .size-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>

<body>
<?php include 'includes/header.php'; ?>

<!-- Design Hero Section -->
<section class="design-hero" data-aos="fade-in">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3" data-aos="fade-up">
                    ออกแบบเสื้อของคุณ
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="200">
                    เครื่องมือออกแบบเสื้อที่ง่ายและทันสมัย<br>
                    ออกแบบได้ตามใจ ฟรี! ไม่มีค่าใช้จ่าย
                </p>
                <div class="d-flex flex-wrap gap-3" data-aos="fade-up" data-aos-delay="400">
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-magic me-2"></i>ออกแบบง่าย
                    </span>
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-eye me-2"></i>ดูผลแบบเรียลไทม์
                    </span>
                    <span class="badge bg-light text-dark fs-6 px-3 py-2">
                        <i class="fas fa-download me-2"></i>ดาวน์โหลดได้
                    </span>
                </div>
            </div>
            <div class="col-lg-4 text-center" data-aos="fade-left">
                <i class="fas fa-palette" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Design Workspace -->
<section class="design-workspace">
    <div class="container-fluid">
        <div class="row">
            <!-- Design Panel -->
            <div class="col-lg-4">
                <div class="design-panel" data-aos="fade-right">
                    <!-- Template Selection -->
                    <div class="tool-section">
                        <h6><i class="fas fa-tshirt"></i>เลือกแบบเสื้อ</h6>
                        <div class="template-grid">
                            <?php foreach ($shirt_templates as $template): ?>
                            <div class="template-item <?php echo $selected_template == $template['id'] ? 'active' : ''; ?>"
                                 onclick="selectTemplate(<?php echo $template['id']; ?>)">
                                <img src="<?php echo $template['image']; ?>"
                                     alt="<?php echo $template['name']; ?>"
                                     onerror="this.src='https://via.placeholder.com/60x70/ee501b/ffffff?text=T'">
                                <div class="small"><?php echo $template['name']; ?></div>
                                <div class="small text-primary">฿<?php echo $template['price']; ?></div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Color Selection -->
                    <div class="tool-section">
                        <h6><i class="fas fa-palette"></i>เลือกสีเสื้อ</h6>
                        <div class="color-grid">
                            <?php foreach ($shirt_colors as $color): ?>
                            <div class="color-item <?php echo $selected_color === $color['value'] ? 'active' : ''; ?>"
                                 style="background-color: <?php echo $color['code']; ?>;"
                                 onclick="selectColor('<?php echo $color['value']; ?>')"
                                 title="<?php echo $color['name']; ?>">
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Size Selection -->
                    <div class="tool-section">
                        <h6><i class="fas fa-ruler"></i>เลือกไซส์</h6>
                        <div class="size-grid">
                            <?php foreach ($shirt_sizes as $size): ?>
                            <div class="size-item" onclick="selectSize('<?php echo $size; ?>')">
                                <?php echo $size; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- Design Tools -->
                    <div class="design-tools">
                        <!-- Text Tool -->
                        <div class="tool-section">
                            <h6><i class="fas fa-font"></i>เพิ่มข้อความ</h6>
                            <input type="text" class="text-input" id="designText"
                                   placeholder="พิมพ์ข้อความที่ต้องการ"
                                   onchange="updateText()">
                            <div class="row g-2">
                                <div class="col-6">
                                    <select class="form-select" id="fontSize" onchange="updateText()">
                                        <option value="16">เล็ก</option>
                                        <option value="20" selected>กลาง</option>
                                        <option value="24">ใหญ่</option>
                                        <option value="28">ใหญ่มาก</option>
                                    </select>
                                </div>
                                <div class="col-6">
                                    <select class="form-select" id="textColor" onchange="updateText()">
                                        <option value="white">ขาว</option>
                                        <option value="black">ดำ</option>
                                        <option value="red">แดง</option>
                                        <option value="blue">น้ำเงิน</option>
                                        <option value="yellow">เหลือง</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Logo Upload -->
                        <div class="tool-section">
                            <h6><i class="fas fa-image"></i>อัปโหลดโลโก้</h6>
                            <div class="upload-area" onclick="document.getElementById('logoUpload').click()">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-2"></i>
                                <div>คลิกเพื่ออัปโหลดโลโก้</div>
                                <small class="text-muted">PNG, JPG (ขนาดไม่เกิน 2MB)</small>
                            </div>
                            <input type="file" id="logoUpload" accept="image/*" style="display: none;" onchange="uploadLogo(this)">
                        </div>

                        <!-- Quantity -->
                        <div class="tool-section">
                            <h6><i class="fas fa-calculator"></i>จำนวน</h6>
                            <div class="input-group">
                                <button class="btn btn-outline-secondary" onclick="changeQuantity(-1)">-</button>
                                <input type="number" class="form-control text-center" id="quantity" value="1" min="1" onchange="updatePrice()">
                                <button class="btn btn-outline-secondary" onclick="changeQuantity(1)">+</button>
                            </div>
                        </div>
                    </div>

                    <!-- Price Summary -->
                    <div class="price-summary">
                        <h6 class="text-primary mb-3">สรุปราคา</h6>
                        <div class="price-item">
                            <span>ราคาเสื้อ:</span>
                            <span id="basePrice">฿350</span>
                        </div>
                        <div class="price-item">
                            <span>จำนวน:</span>
                            <span id="quantityDisplay">1 ตัว</span>
                        </div>
                        <div class="price-item">
                            <span>ค่าออกแบบ:</span>
                            <span class="text-success">ฟรี!</span>
                        </div>
                        <div class="price-total">
                            <div class="price-item">
                                <span>รวมทั้งสิ้น:</span>
                                <span id="totalPrice" class="text-primary">฿350</span>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-4">
                        <button class="btn btn-design-action" onclick="saveDesign()">
                            <i class="fas fa-save me-2"></i>บันทึกการออกแบบ
                        </button>
                        <button class="btn btn-design-action" onclick="downloadDesign()">
                            <i class="fas fa-download me-2"></i>ดาวน์โหลดภาพ
                        </button>
                        <button class="btn btn-design-action" onclick="orderNow()">
                            <i class="fas fa-shopping-cart me-2"></i>สั่งซื้อเลย
                        </button>
                    </div>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="col-lg-8">
                <div class="canvas-area" data-aos="fade-left">
                    <h4 class="mb-4">ตัวอย่างเสื้อ</h4>

                    <div class="shirt-preview" id="shirtPreview">
                        <!-- Shirt SVG will be generated here -->
                        <svg class="shirt-svg" viewBox="0 0 300 350" id="shirtSvg">
                            <!-- Basic T-shirt shape -->
                            <path d="M50 80 L50 60 Q50 50 60 50 L90 50 Q100 40 120 40 L180 40 Q200 40 210 50 L240 50 Q250 50 250 60 L250 80 L250 320 Q250 330 240 330 L60 330 Q50 330 50 320 Z"
                                  fill="#dc3545" stroke="#000" stroke-width="2" id="shirtBody"/>

                            <!-- Sleeves -->
                            <ellipse cx="40" cy="70" rx="25" ry="40" fill="#dc3545" stroke="#000" stroke-width="2" id="leftSleeve"/>
                            <ellipse cx="260" cy="70" rx="25" ry="40" fill="#dc3545" stroke="#000" stroke-width="2" id="rightSleeve"/>

                            <!-- Collar -->
                            <path d="M120 40 Q150 30 180 40" fill="none" stroke="#000" stroke-width="3"/>
                        </svg>

                        <!-- Design Elements -->
                        <div class="design-preview-text" id="previewText" style="display: none;"></div>
                        <img class="design-preview-logo" id="previewLogo" style="display: none;">
                    </div>

                    <div class="row g-3 mt-3">
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                                <h6>ดูผลแบบเรียลไทม์</h6>
                                <small class="text-muted">เห็นผลการออกแบบทันที</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-mobile-alt fa-2x text-primary mb-2"></i>
                                <h6>ใช้งานง่าย</h6>
                                <small class="text-muted">ออกแบบได้ทุกอุปกรณ์</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <i class="fas fa-thumbs-up fa-2x text-primary mb-2"></i>
                                <h6>คุณภาพสูง</h6>
                                <small class="text-muted">ผลิตด้วยเทคโนโลยีทันสมัย</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<script>
// Initialize AOS
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// Design state
let designState = {
    template: <?php echo $selected_template; ?>,
    color: '<?php echo $selected_color; ?>',
    size: 'M',
    text: '',
    textColor: 'white',
    fontSize: 20,
    logo: null,
    quantity: 1,
    basePrice: 350
};

// Color mapping
const colorMap = {
    'red': '#dc3545',
    'blue': '#0d6efd',
    'green': '#198754',
    'yellow': '#ffc107',
    'orange': '#fd7e14',
    'purple': '#6f42c1',
    'white': '#ffffff',
    'black': '#000000'
};

// Template selection
function selectTemplate(templateId) {
    designState.template = templateId;

    // Update active state
    document.querySelectorAll('.template-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.closest('.template-item').classList.add('active');

    // Update price based on template
    const template = <?php echo json_encode($shirt_templates); ?>.find(t => t.id === templateId);
    designState.basePrice = template.price;

    updatePreview();
    updatePrice();
}

// Color selection
function selectColor(color) {
    designState.color = color;

    // Update active state
    document.querySelectorAll('.color-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.classList.add('active');

    updatePreview();
}

// Size selection
function selectSize(size) {
    designState.size = size;

    // Update active state
    document.querySelectorAll('.size-item').forEach(item => {
        item.classList.remove('active');
    });
    event.target.classList.add('active');
}

// Update text
function updateText() {
    const text = document.getElementById('designText').value;
    const fontSize = document.getElementById('fontSize').value;
    const textColor = document.getElementById('textColor').value;

    designState.text = text;
    designState.fontSize = fontSize;
    designState.textColor = textColor;

    const previewText = document.getElementById('previewText');
    if (text) {
        previewText.textContent = text;
        previewText.style.display = 'block';
        previewText.style.fontSize = fontSize + 'px';
        previewText.style.color = textColor;
    } else {
        previewText.style.display = 'none';
    }
}

// Upload logo
function uploadLogo(input) {
    const file = input.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const previewLogo = document.getElementById('previewLogo');
            previewLogo.src = e.target.result;
            previewLogo.style.display = 'block';
            designState.logo = e.target.result;
        };
        reader.readAsDataURL(file);
    }
}

// Change quantity
function changeQuantity(delta) {
    const quantityInput = document.getElementById('quantity');
    let newQuantity = parseInt(quantityInput.value) + delta;
    if (newQuantity < 1) newQuantity = 1;

    quantityInput.value = newQuantity;
    designState.quantity = newQuantity;
    updatePrice();
}

// Update preview
function updatePreview() {
    const shirtBody = document.getElementById('shirtBody');
    const leftSleeve = document.getElementById('leftSleeve');
    const rightSleeve = document.getElementById('rightSleeve');

    const color = colorMap[designState.color];

    shirtBody.setAttribute('fill', color);
    leftSleeve.setAttribute('fill', color);
    rightSleeve.setAttribute('fill', color);
}

// Update price
function updatePrice() {
    const basePrice = designState.basePrice;
    const quantity = designState.quantity;
    const total = basePrice * quantity;

    document.getElementById('basePrice').textContent = '฿' + basePrice.toLocaleString();
    document.getElementById('quantityDisplay').textContent = quantity + ' ตัว';
    document.getElementById('totalPrice').textContent = '฿' + total.toLocaleString();
}

// Save design
function saveDesign() {
    if (!designState.text && !designState.logo) {
        alert('กรุณาเพิ่มข้อความหรือโลโก้ก่อนบันทึก');
        return;
    }

    // Save to localStorage or send to server
    localStorage.setItem('savedDesign', JSON.stringify(designState));
    alert('บันทึกการออกแบบเรียบร้อยแล้ว!');
}

// Download design
function downloadDesign() {
    if (!designState.text && !designState.logo) {
        alert('กรุณาเพิ่มข้อความหรือโลโก้ก่อนดาวน์โหลด');
        return;
    }

    // Create canvas and download
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = 600;
    canvas.height = 700;

    // Draw shirt background
    ctx.fillStyle = colorMap[designState.color];
    ctx.fillRect(100, 160, 400, 480);

    // Draw text if exists
    if (designState.text) {
        ctx.fillStyle = designState.textColor;
        ctx.font = `${designState.fontSize * 2}px Arial`;
        ctx.textAlign = 'center';
        ctx.fillText(designState.text, 300, 400);
    }

    // Download
    const link = document.createElement('a');
    link.download = 'my-shirt-design.png';
    link.href = canvas.toDataURL();
    link.click();
}

// Order now
function orderNow() {
    if (!designState.text && !designState.logo) {
        alert('กรุณาเพิ่มข้อความหรือโลโก้ก่อนสั่งซื้อ');
        return;
    }

    // Save design and redirect to checkout
    localStorage.setItem('orderDesign', JSON.stringify(designState));
    window.location.href = 'checkout.php';
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
    updatePrice();

    // Load saved design if exists
    const savedDesign = localStorage.getItem('savedDesign');
    if (savedDesign) {
        const design = JSON.parse(savedDesign);
        // Apply saved design...
    }
});

// Drag and drop for logo upload
const uploadArea = document.querySelector('.upload-area');

uploadArea.addEventListener('dragover', function(e) {
    e.preventDefault();
    this.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', function(e) {
    e.preventDefault();
    this.classList.remove('dragover');
});

uploadArea.addEventListener('drop', function(e) {
    e.preventDefault();
    this.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            const input = document.getElementById('logoUpload');
            input.files = files;
            uploadLogo(input);
        }
    }
});
</script>

</body>
</html>
