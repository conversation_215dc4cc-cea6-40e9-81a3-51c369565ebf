
 <!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ติดต่อเรา - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #ee501b;
            --secondary-color: #ff6b35;
        }

        .navbar-brand { color: var(--primary-color) !important; font-weight: bold; font-size: 1.5rem; }
        .btn-primary { background: var(--primary-color); border: none; }
        .btn-primary:hover { background: #d44615; }
        .contact-card { border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-radius: 15px; }
        .contact-icon { color: var(--primary-color); font-size: 2rem; }
        .map-container { height: 300px; border-radius: 15px; overflow: hidden; }
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 300px;
            max-height: 400px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 25px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
        }
        .chat-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 1001;
        }
        .chat-header {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            border-radius: 15px 15px 0 0;
        }
        .chat-messages {
            height: 250px;
            overflow-y: auto;
            padding: 15px;
        }
        .contact-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 80px 0;
        }
    </style>
</head>

<?php include './includes/header.php'; ?>

<!-- ✅ HTML -->
<body class="home wp-singular page-template page-template-elementor_header_footer page page-id-2021 wp-custom-logo wp-embed-responsive wp-theme-hello-elementor hello-elementor-default elementor-default elementor-template-full-width elementor-kit-7 elementor-page elementor-page-2021"></body>

	 <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: sans-serif;
    }

    .hero-section {
      width: 100%;
    }

    .desktop,
    .mobile {
      width: 100%;
    }

    .desktop {
      display: block;
    }

    .mobile {
      display: none;
    }

    img {
      width: 100%;
      height: auto;
      display: block;
    }
	.hero-section,
    .desktop,
    .mobile {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 0;
}

    @media (max-width: 768px) {
      .desktop {
        display: none;
      }

      .mobile {
        display: block;
      }
	  .mobile img {
        max-height: 400px;
        object-fit: cover;
}
    }
  </style>

    <!-- แสดงบน Desktop -->
    <div class="desktop">
      <img src="assets/images/banner/3.jpg" alt="Banner for Desktop">
    </div>

    <!-- แสดงบน Mobile -->
    <div class="mobile">
      <img src="assets/images/banner/3.jpg" alt="Banner for Mobile">
    </div>




 <!-- Products Section -->
        <section class="products-section" id="products"style="">
            <div class="container"style="margin-top:0px;">
               

               
<div class="section" style="padding: 40px 20px; background-color: #f5f5f5;">
    <div class="container" style="max-width: 1200px; margin: 0 auto;">
        <h2 style="font-size: 32px; text-align: center;">
            ค้นพบเสื้อที่ใช่ <span style="color: #e63946;">สำหรับทุกไลฟ์สไตล์</span>
        </h2>
        <p style="text-align: center; font-size: 16px; margin-bottom: 40px;">
            ผลิตเสื้อด้วยผ้าคุณภาพพรีเมี่ยม มีเนื้อผ้าให้เลือกหลากหลายชนิด<br>
            ใส่ใจในทุกรายละเอียด พร้อมจัดส่งทั่วประเทศ
        </p>

        <div class="product-grid" style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;">
            <?php
            $items = [
                [
                    'title' => 'เสื้อบริษัท',
                    'desc' => 'เสื้อบริษัท สะท้อนความเป็นมืออาชีพ นำเสนอสไตล์ของคุณ พร้อมให้คุณดูดีในทุกวัน',
                    'img' => 'uploads/products/475382494_122220623120074738_924515879995798050_n.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#company_design'
                ],
                [
                    'title' => 'เสื้อกิจกรรม',
                    'desc' => 'พร้อมสำหรับทุกความสนุกด้วยเสื้อกิจกรรม ที่ให้คุณดูโดดเด่นและรู้สึกสบายทุกการเคลื่อนไหว',
                    'img' => 'uploads/products/475670051_122220623066074738_2795502938249037331_n.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#activities_design'
                ],
                [
                    'title' => 'เสื้อกีฬาสี โรงเรียน',
                    'desc' => 'โชว์พลังทีมด้วยเสื้อกีฬาสีที่สร้างสรรค์เพื่อความสนุกและความสบายในการแข่งขัน',
                    'img' => 'uploads/products/475697812_122220623084074738_101296545669764346_n.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#agency_design'
                ],
                [
                    'title' => 'เสื้อกีฬา',
                    'desc' => 'เสื้อกีฬาที่ทำให้คุณรู้สึกสบาย มั่นใจในทุกการเคลื่อนไหว',
                    'img' => 'uploads/products/475755576_122220623090074738_8040878751312398557_n.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#sport_design'
                ],
                [
                    'title' => 'เสื้อหน่วยงานราชการ',
                    'desc' => 'เสื้อหน่วยงานราชการ บริการไว คุณภาพสูง ประทับใจทุกหน่วยงาน',
                    'img' => 'uploads/products/475815335_122220623102074738_309201310605034286_n.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#agency_design'
                ],
                [
                    'title' => 'เสื้อดีไซน์พิเศษ',
                    'desc' => 'ออกแบบพิเศษเฉพาะคุณ กับดีไซน์ที่ไม่ซ้ำใคร',
                    'img' => 'uploads/products/475925098_122220623750074738_1631174588409703078_n.jpg',
                    'link' => 'https://www.finixsports.com/shirt-design/#finix_design'
                ],
            ];

            foreach ($items as $item): ?>
                <div class="product-card" style="width: 300px; background: #fff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 10px rgba(0,0,0,0.1);">
                    <a href="<?= $item['link'] ?>" style="text-decoration: none; color: inherit;">
                        <div class="image" style="background-image: url('<?= $item['img'] ?>'); height: 200px; background-size: cover; background-position: center;"></div>
                        <div class="content" style="padding: 20px;">
                            <h3 style="font-size: 20px; margin: 0 0 10px;"><?= $item['title'] ?></h3>
                            <p style="font-size: 14px;"><?= $item['desc'] ?></p>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

                <div class="text-center mt-5">
                    <a href="#" class="btn-hero">ดูสินค้าทั้งหมด</a>
                </div>
            </div>
        </section>


        <!-- Features Section -->
        <section class="features-section" id="features"style="">
            <div class="container">
              <h2 style="font-size: 32px; text-align: center;">
            ทำไมต้อง  <span style="color:rgb(255, 255, 255);">GT SPORT DESIGN</span>
        </h2>
        <p style="text-align: center; font-size: 16px; margin-bottom: 40px;">
            เหตุผลที่ลูกค้าเลือกใช้บริการของเรา
        </p>
        <div class="row g-4">
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <h5 class="feature-title">ส่งชิ้นผ้า<br>ให้ฟรี!</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h5 class="feature-title">ตอบแชทไว<br>ภายใน 5 นาที</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <h5 class="feature-title">ปรับแบบ<br>จนพอใจ</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-award"></i>
                            </div>
                            <h5 class="feature-title">ทุกโลโก้<br>ราคาเดียว</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-file-invoice"></i>
                            </div>
                            <h5 class="feature-title">รวม VAT<br>แล้วไม่บวกเพิ่ม</h5>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <h5 class="feature-title">รับประกันสินค้า<br>เคลมฟรีใน 7 วัน</h5>
                        </div>
                    </div>
                </div>
            </div>
        </section>

       
        <!-- Gallery Section -->
        <section class="gallery-section" id="gallery"style="">
            <div class="container"style="margin-top:0px;">
                <div class="section-title">
                    <h2>รีวิวความไว้ใจจาก <span class="highlight">GT SPORT DESIGN</span></h2>
                    <p class="lead">ผลงานการออกแบบและผลิตเสื้อกีฬาของเรา</p>
                </div>

                <div class="gallery-grid">
                    <?php if (!empty($gallery_items)): ?>
                        <?php foreach (array_slice($gallery_items, 0, 6) as $item): ?>
                            <div class="gallery-item">
                                <img src=""
                                    alt="<?php echo htmlspecialchars($item['title']); ?>">
                                <div class="gallery-overlay">
                                    <span><?php echo htmlspecialchars($item['title']); ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Fallback gallery -->
                        <?php for ($i = 0; $i < 1; $i++): ?>
                            <div class="gallery-item">
                                <img src="https://img2.pic.in.th/pic/LINE_ALBUM__241018_1.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                        <div class="gallery-item">
                                <img src="https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_2.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_3755795ed32b03ea7.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_5.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="https://img5.pic.in.th/file/secure-sv1/644002183119016645_n.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="https://img2.pic.in.th/pic/LINE_ALBUM__241018_7.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="https://img2.pic.in.th/pic/LINE_ALBUM__241018_8.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                             <div class="gallery-item">
                                <img src="https://img2.pic.in.th/pic/LINE_ALBUM__241018_10.jpg" alt="ผลงานการออกแบบ">
                                <div class="gallery-overlay">
                                    <span>ผลงานการออกแบบ</span>
                                </div>
                            </div>
                        <?php endfor; ?>
                    <?php endif; ?>
                </div>

                <div class="text-center mt-5">
                    <a href="#" class="btn-hero">ดูผลงานเพิ่มเติม</a>
                </div>
            </div>
        </section>

        <!-- Reviews Section -->
        <section class="reviews-section" id="reviews">
            <div class="container">
                <div class="section-title">
                    <h2>ความคิดเห็นจาก<span class="highlight"><h2 style="color: rgb(19, 20, 20);">ลูกค้า</h2></span></h2>
                    <p class="lead"style="color: rgb(19, 20, 20);">รีวิวจากลูกค้าที่ใช้บริการจริง</p>
                </div>

                <div class="row g-4">
                    <?php if (!empty($reviews)): ?>
                        <?php foreach (array_slice($reviews, 0, 3) as $review): ?>
                            <div class="col-lg-4">
                                <div class="review-card">
                                    <div class="review-stars">
                                        <?php for ($i = 0; $i < $review['rating']; $i++): ?>
                                            <i class="fas fa-star"></i>
                                        <?php endfor; ?>
                                    </div>
                                    <p class="review-text"><?php echo htmlspecialchars($review['comment']); ?></p>
                                    <div class="review-author">
                                        <div class="review-avatar">
                                            <?php echo strtoupper(substr($review['customer_name'] ?: 'A', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="review-name">
                                                <?php echo htmlspecialchars($review['customer_name'] ?: 'ลูกค้า'); ?></div>
                                            <small
                                                class="text-muted"><?php echo htmlspecialchars($review['product_name']); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Fallback reviews -->
                        <div class="col-lg-4">
                            <div class="review-card">
                                <div class="review-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="review-text">ใส่สบาย ลายสวย</p>
                                <div class="review-author">
                                    <div class="review-avatar">ส</div>
                                    <div>
                                        <div class="review-name">สมชาย ใจดี</div>
                                        <small class="text-muted">เสื้อกีฬาทีม</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="review-card">
                                <div class="review-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="review-text">เสื้อคุณภาพดีมาก  </p>
                                <div class="review-author">
                                    <div class="review-avatar">ส</div>
                                    <div>
                                        <div class="review-name">zeza</div>
                                        <small class="text-muted">เสื้อกีฬาทีม</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="review-card">
                                <div class="review-stars">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <p class="review-text">ทีมงานบริการดีเยี่ยม</p>
                                <div class="review-author">
                                    <div class="review-avatar">ส</div>
                                    <div>
                                        <div class="review-name">baet</div>
                                        <small class="text-muted">เสื้อกีฬาทีม</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </section>

               
                <div class="elementor-element elementor-element-9f99210 e-flex e-con-boxed e-con e-parent e-lazyloaded" data-id="9f99210" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
					<div class="e-con-inner">
				<div class="elementor-element elementor-element-3de93a1 elementor-widget elementor-widget-heading animated fadeIn" data-id="3de93a1" data-element_type="widget" data-settings="{&quot;_animation&quot;:&quot;fadeIn&quot;}" data-widget_type="heading.default">
				<div class="elementor-widget-container">
					<h2 class="elementor-heading-title elementor-size-default">VIDEO</h2>				</div>
				</div>
		<div class="elementor-element elementor-element-0394c0f e-grid e-con-full e-con e-child" data-id="0394c0f" data-element_type="container">
				<div class="elementor-element elementor-element-e362bf7 elementor-widget elementor-widget-video" data-id="e362bf7" data-element_type="widget" data-settings="{&quot;youtube_url&quot;:&quot;https:\/\/www.youtube.com\/watch?v=XHOmBV4js_E&quot;,&quot;video_type&quot;:&quot;youtube&quot;,&quot;controls&quot;:&quot;yes&quot;}" data-widget_type="video.default">
				<div class="elementor-widget-container">
							<div class="elementor-wrapper elementor-open-inline">
			<iframe class="elementor-video" frameborder="0" allowfullscreen="" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" title="Video Placeholder" width="640" height="360" src="https://www.youtube.com/embed/XHOmBV4js_E?controls=1&amp;rel=0&amp;playsinline=0&amp;cc_load_policy=0&amp;autoplay=0&amp;enablejsapi=1&amp;origin=https%3A%2F%2Fwww.finixsports.com&amp;widgetid=1&amp;forigin=https%3A%2F%2Fwww.finixsports.com%2F&amp;aoriginsup=1&amp;gporigin=https%3A%2F%2Fwww.finixsports.com%2Fshirt-design%2F&amp;vf=1" id="widget2" data-gtm-yt-inspected-14="true"></iframe>		</div>
						</div>
				</div>
		
               
        <?php include './includes/footer.php'; ?>

            <!-- Live Chat Widget -->
    <button class="chat-toggle" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
    </button>

    <div class="chat-widget" id="chatWidget">
        <div class="chat-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-comments"></i> แชทสด</h6>
                <button class="btn btn-sm text-white" onclick="toggleChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="text-center text-muted py-3">
                <small>พิมพ์ข้อความเพื่อเริ่มการสนทนา</small>
            </div>
        </div>
        <div class="p-3">
            <div class="input-group">
                <input type="text" class="form-control" id="chatInput" placeholder="พิมพ์ข้อความ..."
                       onkeypress="if(event.key==='Enter') sendMessage()">
                <button class="btn btn-primary" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let chatOpen = false;

        function toggleChat() {
            const widget = document.getElementById('chatWidget');
            const toggle = document.querySelector('.chat-toggle');

            if (chatOpen) {
                widget.style.display = 'none';
                toggle.innerHTML = '<i class="fas fa-comments"></i>';
                chatOpen = false;
            } else {
                widget.style.display = 'block';
                toggle.innerHTML = '<i class="fas fa-times"></i>';
                chatOpen = true;

                // Auto message
                if (!localStorage.getItem('chatWelcome')) {
                    setTimeout(() => {
                        addMessage('GT Sport Design', 'สวัสดีครับ! มีอะไรให้เราช่วยไหมครับ?', 'admin');
                        localStorage.setItem('chatWelcome', 'shown');
                    }, 1000);
                }
            }
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                addMessage('คุณ', message, 'user');
                input.value = '';

                // Auto reply
                setTimeout(() => {
                    const replies = [
                        'ขอบคุณสำหรับข้อความครับ เราจะติดต่อกลับไปเร็วๆ นี้',
                        'สำหรับข้อมูลเพิ่มเติม สามารถโทร ************ ได้เลยครับ',
                        'หรือสามารถ LINE มาที่ @gtsport ได้ครับ'
                    ];
                    const reply = replies[Math.floor(Math.random() * replies.length)];
                    addMessage('GT Sport Design', reply, 'admin');
                }, 1500);
            }
        }

        function addMessage(sender, text, type) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `mb-2 ${type === 'user' ? 'text-end' : ''}`;

            messageDiv.innerHTML = `
                <div class="d-inline-block p-2 rounded ${type === 'user' ? 'bg-primary text-white' : 'bg-light'}">
                    <small class="fw-bold">${sender}</small><br>
                    <span>${text}</span>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Auto-resize contact form
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });
        });
    </script>
