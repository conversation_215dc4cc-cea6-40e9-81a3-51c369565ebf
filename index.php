<?php
session_start();

// ข้อมูล fallback สำหรับ gallery และ reviews
$gallery_items = [];
$reviews = [];

// ลองเชื่อมต่อฐานข้อมูลเพื่อดึงข้อมูล
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';

        // ดึงข้อมูล gallery
        if (isset($pdo)) {
            $stmt = $pdo->query("SELECT * FROM gallery_images WHERE is_active = 1 ORDER BY sort_order LIMIT 8");
            $gallery_items = $stmt->fetchAll();
        }
    }
} catch (Exception $e) {
    // ไม่ต้องแสดงข้อผิดพลาด ใช้ข้อมูล fallback
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>GT Sport Design - ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬา</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง พร้อมเทคโนโลยีการออกแบบที่ทันสมัย">
    <meta name="keywords" content="เสื้อกีฬา, ออกแบบเสื้อ, เสื้อบริษัท, เสื้อกิจกรรม, GT Sport Design">
    
    <!-- Open Graph -->
    <meta property="og:title" content="GT Sport Design - ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬา">
    <meta property="og:description" content="ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง พร้อมเทคโนโลยีการออกแบบที่ทันสมัย">
    <meta property="og:image" content="https://gtsportdesign.com/assets/images/banner/products-and-services.jpg">
    <meta property="og:url" content="https://gtsportdesign.com/">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/banner.css" rel="stylesheet">
    
    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: #ee501b;
            --secondary-color: #ff6b35;
            --dark-color: #2c3e50;
            --light-gray: #f8f9fa;
            --gradient-primary: linear-gradient(135deg, #ee501b, #ff6b35);
            --gradient-dark: linear-gradient(135deg, #2c3e50, #34495e);
        }

        /* Hero Section */
        .hero-section {
            background: var(--gradient-primary);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        .hero-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,0 1000,300 1000,1000 0,700"/></svg>');
            background-size: cover;
        }

        .hero-title {
            font-size: 3.5rem;
            line-height: 1.2;
        }

        .text-gradient {
            background: linear-gradient(45deg, #fff, #f8f9fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-badge {
            animation: pulse 2s infinite;
        }

        .btn-hover-effect {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-hover-effect:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .floating-element {
            position: absolute;
            animation: float 6s ease-in-out infinite;
        }

        .floating-1 { top: 20%; right: 10%; animation-delay: 0s; }
        .floating-2 { top: 60%; right: 20%; animation-delay: 2s; }
        .floating-3 { top: 40%; right: 5%; animation-delay: 4s; }

        .feature-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Sections */
        .section-title {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-title h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 1rem;
        }

        .highlight {
            color: var(--primary-color);
        }

        /* Products Section */
        .products-section {
            padding: 80px 0;
            background: var(--light-gray);
        }

        .product-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .product-image {
            height: 250px;
            background-size: cover;
            background-position: center;
            position: relative;
        }

        .product-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            opacity: 0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-card:hover .product-overlay {
            opacity: 0.9;
        }

        /* Features Section */
        .features-section {
            padding: 80px 0;
            background: var(--gradient-dark);
            color: white;
        }

        .feature-item {
            text-align: center;
            padding: 2rem;
            border-radius: 15px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            height: 100%;
        }

        .feature-item:hover {
            transform: translateY(-5px);
            background: rgba(255,255,255,0.15);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }

        /* Gallery Section */
        .gallery-section {
            padding: 80px 0;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 3rem;
        }

        .gallery-item {
            position: relative;
            border-radius: 15px;
            overflow: hidden;
            aspect-ratio: 1;
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .gallery-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            opacity: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .gallery-item:hover .gallery-overlay {
            opacity: 0.9;
        }

        .gallery-item:hover img {
            transform: scale(1.1);
        }

        /* Reviews Section */
        .reviews-section {
            padding: 80px 0;
            background: var(--light-gray);
        }

        .review-card {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: 100%;
            transition: all 0.3s ease;
        }

        .review-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .review-stars {
            color: #ffc107;
            margin-bottom: 1rem;
        }

        .review-author {
            display: flex;
            align-items: center;
            margin-top: 1rem;
        }

        .review-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 1rem;
        }

        /* CTA Section */
        .cta-section {
            padding: 80px 0;
            background: var(--gradient-primary);
            color: white;
            text-align: center;
        }

        .btn-cta {
            background: white;
            color: var(--primary-color);
            padding: 15px 40px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-cta:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            color: var(--primary-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .section-title h2 {
                font-size: 2rem;
            }
            
            .floating-element {
                display: none;
            }
        }

        /* Scroll Indicator */
        .scroll-indicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            animation: bounce 2s infinite;
        }

        .scroll-arrow {
            color: white;
            font-size: 1.5rem;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
            40% { transform: translateX(-50%) translateY(-10px); }
            60% { transform: translateX(-50%) translateY(-5px); }
        }
    </style>
</head>

<body>
<?php include 'includes/header.php'; ?>

<!-- Hero Banner Section -->
<section class="hero-banner" data-aos="fade-in">
    <div class="hero-content">
        <div class="hero-badge mb-3" data-aos="fade-up" data-aos-delay="200">
            <span class="badge bg-light text-primary px-3 py-2 rounded-pill">
                <i class="fas fa-star me-1"></i>ผู้เชี่ยวชาญมากกว่า 10 ปี
            </span>
        </div>

        <h1 class="hero-title" data-aos="fade-up" data-aos-delay="400">
            GT SPORT DESIGN
        </h1>

        <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="600">
            ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง<br>
            ด้วยประสบการณ์กว่า 10 ปี พร้อมบริการครบวงจร
        </p>

        <!-- Stats -->
        <div class="row g-3 mb-4" data-aos="fade-up" data-aos-delay="800">
            <div class="col-4">
                <div class="text-center">
                    <h3 class="mb-0 counter" data-target="1000">0</h3>
                    <small>ลูกค้าพึงพอใจ</small>
                </div>
            </div>
            <div class="col-4">
                <div class="text-center">
                    <h3 class="mb-0 counter" data-target="50">0</h3>
                    <small>แบบเสื้อ</small>
                </div>
            </div>
            <div class="col-4">
                <div class="text-center">
                    <h3 class="mb-0">24/7</h3>
                    <small>บริการ</small>
                </div>
            </div>
        </div>

        <!-- Buttons -->
        <div class="hero-buttons mb-4" data-aos="fade-up" data-aos-delay="1000">
            <a href="/design" class="btn btn-light btn-lg me-3 btn-hover-effect">
                <i class="fas fa-paint-brush me-2"></i>เริ่มออกแบบฟรี
            </a>
            <a href="/products" class="btn btn-outline-light btn-lg btn-hover-effect">
                <i class="fas fa-shopping-cart me-2"></i>ดูสินค้า
            </a>
        </div>

        <!-- Features -->
        <div class="d-flex flex-wrap gap-3" data-aos="fade-up" data-aos-delay="1200">
            <div class="d-flex align-items-center">
                <i class="fas fa-shipping-fast me-2"></i>
                <small>จัดส่งฟรี</small>
            </div>
            <div class="d-flex align-items-center">
                <i class="fas fa-medal me-2"></i>
                <small>คุณภาพพรีเมียม</small>
            </div>
            <div class="d-flex align-items-center">
                <i class="fas fa-headset me-2"></i>
                <small>ปรึกษาฟรี 24/7</small>
            </div>
        </div>
    </div>
</section>

<!-- Products Section -->
<section class="products-section" id="products">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>ค้นพบเสื้อที่ใช่ <span class="highlight">สำหรับทุกไลฟ์สไตล์</span></h2>
            <p class="lead">ผลิตเสื้อด้วยผ้าคุณภาพพรีเมียม มีเนื้อผ้าให้เลือกหลากหลายชนิด<br>
            ใส่ใจในทุกรายละเอียด พร้อมจัดส่งทั่วประเทศ</p>
        </div>

        <div class="row g-4">
            <?php
            $products = [
                [
                    'title' => 'เสื้อบริษัท',
                    'desc' => 'เสื้อบริษัท สะท้อนความเป็นมืออาชีพ นำเสนอสไตล์ของคุณ พร้อมให้คุณดูดีในทุกวัน',
                    'img' => 'uploads/products/company-shirt.jpg',
                    'link' => 'products.php?category=company',
                    'icon' => 'fas fa-building'
                ],
                [
                    'title' => 'เสื้อกิจกรรม',
                    'desc' => 'พร้อมสำหรับทุกความสนุกด้วยเสื้อกิจกรรม ที่ให้คุณดูโดดเด่นและรู้สึกสบายทุกการเคลื่อนไหว',
                    'img' => 'uploads/products/activity-shirt.jpg',
                    'link' => 'products.php?category=activity',
                    'icon' => 'fas fa-users'
                ],
                [
                    'title' => 'เสื้อกีฬาสี โรงเรียน',
                    'desc' => 'โชว์พลังทีมด้วยเสื้อกีฬาสีที่สร้างสรรค์เพื่อความสนุกและความสบายในการแข่งขัน',
                    'img' => 'uploads/products/school-sport.jpg',
                    'link' => 'products.php?category=school',
                    'icon' => 'fas fa-graduation-cap'
                ],
                [
                    'title' => 'เสื้อกีฬา',
                    'desc' => 'เสื้อกีฬาที่ทำให้คุณรู้สึกสบาย มั่นใจในทุกการเคลื่อนไหว',
                    'img' => 'uploads/products/sport-shirt.jpg',
                    'link' => 'products.php?category=sport',
                    'icon' => 'fas fa-running'
                ],
                [
                    'title' => 'เสื้อหน่วยงานราชการ',
                    'desc' => 'เสื้อหน่วยงานราชการ บริการไว คุณภาพสูง ประทับใจทุกหน่วยงาน',
                    'img' => 'uploads/products/government.jpg',
                    'link' => 'products.php?category=government',
                    'icon' => 'fas fa-landmark'
                ],
                [
                    'title' => 'เสื้อดีไซน์พิเศษ',
                    'desc' => 'ออกแบบพิเศษเฉพาะคุณ กับดีไซน์ที่ไม่ซ้ำใคร',
                    'img' => 'uploads/products/custom-design.jpg',
                    'link' => 'shirt-design.php',
                    'icon' => 'fas fa-palette'
                ]
            ];

            foreach ($products as $index => $product): ?>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                    <div class="product-card">
                        <div class="product-image" style="background-image: url('<?php echo $product['img']; ?>'), url('https://via.placeholder.com/400x250/ee501b/ffffff?text=<?php echo urlencode($product['title']); ?>');">
                            <div class="product-overlay">
                                <a href="<?php echo $product['link']; ?>" class="btn btn-light btn-lg">
                                    <i class="<?php echo $product['icon']; ?> me-2"></i>ดูรายละเอียด
                                </a>
                            </div>
                        </div>
                        <div class="p-4">
                            <h4 class="mb-3"><?php echo $product['title']; ?></h4>
                            <p class="text-muted mb-3"><?php echo $product['desc']; ?></p>
                            <a href="<?php echo $product['link']; ?>" class="btn btn-primary">
                                <i class="<?php echo $product['icon']; ?> me-2"></i>เลือกแบบนี้
                            </a>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-5" data-aos="fade-up">
            <a href="/products" class="btn-cta">
                <i class="fas fa-th-large me-2"></i>ดูสินค้าทั้งหมด
            </a>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section" id="features">
    <div class="container">
        <div class="section-title text-white" data-aos="fade-up">
            <h2>ทำไมต้อง <span class="text-white">GT SPORT DESIGN</span></h2>
            <p class="lead">เหตุผลที่ลูกค้าเลือกใช้บริการของเรา</p>
        </div>

        <div class="row g-4">
            <?php
            $features = [
                ['icon' => 'fas fa-shipping-fast', 'title' => 'ส่งชิ้นผ้าให้ฟรี!', 'desc' => 'ส่งตัวอย่างผ้าให้ดูก่อนตัดสินใจ'],
                ['icon' => 'fas fa-comments', 'title' => 'ตอบแชทไว ภายใน 5 นาที', 'desc' => 'ทีมงานพร้อมให้คำปรึกษาตลอด 24 ชั่วโมง'],
                ['icon' => 'fas fa-tshirt', 'title' => 'ปรับแบบจนพอใจ', 'desc' => 'แก้ไขดีไซน์ได้ไม่จำกัดจนกว่าจะพอใจ'],
                ['icon' => 'fas fa-award', 'title' => 'ทุกโลโก้ราคาเดียว', 'desc' => 'ไม่ว่าจะกี่สี กี่โลโก้ ราคาเดียวกัน'],
                ['icon' => 'fas fa-file-invoice', 'title' => 'รวม VAT แล้วไม่บวกเพิ่ม', 'desc' => 'ราคาที่เห็นคือราคาจริง ไม่มีค่าใช้จ่ายแอบแฝง'],
                ['icon' => 'fas fa-shield-alt', 'title' => 'รับประกันสินค้า เคลมฟรีใน 7 วัน', 'desc' => 'มั่นใจในคุณภาพ รับประกันความพึงพอใจ']
            ];

            foreach ($features as $index => $feature): ?>
                <div class="col-lg-2 col-md-4 col-6" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="<?php echo $feature['icon']; ?>"></i>
                        </div>
                        <h5 class="mb-3"><?php echo $feature['title']; ?></h5>
                        <p class="small"><?php echo $feature['desc']; ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Gallery Section -->
<section class="gallery-section" id="gallery">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>รีวิวความไว้ใจจาก <span class="highlight">GT SPORT DESIGN</span></h2>
            <p class="lead">ผลงานการออกแบบและผลิตเสื้อกีฬาของเรา</p>
        </div>

        <div class="gallery-grid">
            <?php if (!empty($gallery_items)): ?>
                <?php foreach (array_slice($gallery_items, 0, 8) as $index => $item): ?>
                    <div class="gallery-item" data-aos="zoom-in" data-aos-delay="<?php echo $index * 100; ?>">
                        <img src="<?php echo htmlspecialchars($item['image_path']); ?>"
                             alt="<?php echo htmlspecialchars($item['title']); ?>">
                        <div class="gallery-overlay">
                            <span><?php echo htmlspecialchars($item['title']); ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Fallback gallery -->
                <?php
                $gallery_images = [
                    ['src' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_1.jpg', 'title' => 'เสื้อกีฬาทีมฟุตบอล'],
                    ['src' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_2.jpg', 'title' => 'เสื้อบริษัทสีน้ำเงิน'],
                    ['src' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_3755795ed32b03ea7.jpg', 'title' => 'เสื้อกิจกรรมโรงเรียน'],
                    ['src' => 'https://img5.pic.in.th/file/secure-sv1/LINE_ALBUM__241018_5.jpg', 'title' => 'เสื้อกีฬาสีแดง'],
                    ['src' => 'https://img5.pic.in.th/file/secure-sv1/644002183119016645_n.jpg', 'title' => 'เสื้อทีมวอลเลย์บอล'],
                    ['src' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_7.jpg', 'title' => 'เสื้อหน่วยงานราชการ'],
                    ['src' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_8.jpg', 'title' => 'เสื้อกิจกรรมสีเขียว'],
                    ['src' => 'https://img2.pic.in.th/pic/LINE_ALBUM__241018_10.jpg', 'title' => 'เสื้อดีไซน์พิเศษ']
                ];

                foreach ($gallery_images as $index => $image): ?>
                    <div class="gallery-item" data-aos="zoom-in" data-aos-delay="<?php echo $index * 100; ?>">
                        <img src="<?php echo $image['src']; ?>" alt="<?php echo $image['title']; ?>">
                        <div class="gallery-overlay">
                            <span><?php echo $image['title']; ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <div class="text-center mt-5" data-aos="fade-up">
            <a href="/gallery" class="btn-cta">
                <i class="fas fa-images me-2"></i>ดูผลงานเพิ่มเติม
            </a>
        </div>
    </div>
</section>

<!-- Reviews Section -->
<section class="reviews-section" id="reviews">
    <div class="container">
        <div class="section-title" data-aos="fade-up">
            <h2>ความคิดเห็นจาก<span class="highlight">ลูกค้า</span></h2>
            <p class="lead">รีวิวจากลูกค้าที่ใช้บริการจริง</p>
        </div>

        <div class="row g-4">
            <?php if (!empty($reviews)): ?>
                <?php foreach (array_slice($reviews, 0, 3) as $index => $review): ?>
                    <div class="col-lg-4" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="review-card">
                            <div class="review-stars">
                                <?php for ($i = 0; $i < $review['rating']; $i++): ?>
                                    <i class="fas fa-star"></i>
                                <?php endfor; ?>
                            </div>
                            <p class="review-text"><?php echo htmlspecialchars($review['comment']); ?></p>
                            <div class="review-author">
                                <div class="review-avatar">
                                    <?php echo strtoupper(substr($review['customer_name'] ?: 'A', 0, 1)); ?>
                                </div>
                                <div>
                                    <div class="review-name"><?php echo htmlspecialchars($review['customer_name'] ?: 'ลูกค้า'); ?></div>
                                    <small class="text-muted"><?php echo htmlspecialchars($review['product_name']); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <!-- Fallback reviews -->
                <?php
                $fallback_reviews = [
                    ['name' => 'สมชาย ใจดี', 'text' => 'ใส่สบาย ลายสวย คุณภาพดีมาก ประทับใจการบริการ จะสั่งอีกแน่นอน', 'product' => 'เสื้อกีฬาทีม', 'rating' => 5],
                    ['name' => 'นางสาว อรทัย', 'text' => 'เสื้อคุณภาพดีมาก ทีมงานให้คำปรึกษาดี ส่งตรงเวลา ราคาสมเหตุสมผล', 'product' => 'เสื้อบริษัท', 'rating' => 5],
                    ['name' => 'คุณบุญเลิศ', 'text' => 'ทีมงานบริการดีเยี่ยม ออกแบบตามที่ต้องการ คุณภาพเกินราคา แนะนำเลย', 'product' => 'เสื้อกิจกรรม', 'rating' => 5]
                ];

                foreach ($fallback_reviews as $index => $review): ?>
                    <div class="col-lg-4" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                        <div class="review-card">
                            <div class="review-stars">
                                <?php for ($i = 0; $i < $review['rating']; $i++): ?>
                                    <i class="fas fa-star"></i>
                                <?php endfor; ?>
                            </div>
                            <p class="review-text">"<?php echo $review['text']; ?>"</p>
                            <div class="review-author">
                                <div class="review-avatar">
                                    <?php echo strtoupper(substr($review['name'], 0, 1)); ?>
                                </div>
                                <div>
                                    <div class="review-name"><?php echo $review['name']; ?></div>
                                    <small class="text-muted"><?php echo $review['product']; ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container text-center" data-aos="fade-up">
        <h2 class="display-4 fw-bold mb-4">พร้อมเริ่มออกแบบเสื้อของคุณแล้วหรือยัง?</h2>
        <p class="lead mb-5">เริ่มต้นออกแบบเสื้อในแบบของคุณได้เลยวันนี้ ฟรี! ไม่มีค่าใช้จ่าย</p>
        <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="/design" class="btn-cta">
                <i class="fas fa-paint-brush me-2"></i>เริ่มออกแบบฟรี
            </a>
            <a href="/contact" class="btn btn-outline-light btn-lg">
                <i class="fas fa-phone me-2"></i>ปรึกษาฟรี
            </a>
        </div>

        <!-- Contact Info -->
        <div class="row g-4 mt-5">
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fas fa-phone fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-0">************</h5>
                        <small>โทรปรึกษาฟรี</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fab fa-line fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-0">@gtsport</h5>
                        <small>แชทผ่าน Line</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="fab fa-facebook fa-2x me-3"></i>
                    <div>
                        <h5 class="mb-0">GT Sport Design</h5>
                        <small>ติดตามเฟซบุ๊ก</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<script>
// Initialize AOS
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Navbar background on scroll
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        if (window.scrollY > 100) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }
    }
});

// Parallax effect for hero section
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelector('.hero-bg');
    if (parallax) {
        const speed = scrolled * 0.5;
        parallax.style.transform = `translateY(${speed}px)`;
    }
});

// Counter animation
function animateCounters() {
    const counters = document.querySelectorAll('.counter');
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target + '+';
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current) + '+';
            }
        }, 20);
    });
}

// Trigger counter animation when hero banner is visible
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            animateCounters();
            observer.unobserve(entry.target);
        }
    });
});

const heroBanner = document.querySelector('.hero-banner');
if (heroBanner) {
    observer.observe(heroBanner);
}
</script>

</body>
</html>
