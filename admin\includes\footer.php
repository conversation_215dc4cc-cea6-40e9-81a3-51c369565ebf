    </div>

    <!-- Admin Footer -->
    <footer class="admin-footer">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="footer-info">
                        <strong>GT Sport Design</strong> - ระบบจัดการหลังบ้าน
                        <div class="footer-links">
                            <a href="https://gtsportdesign.com" target="_blank">เว็บไซต์หลัก</a>
                            <span class="separator">|</span>
                            <a href="admin_guide.php">คู่มือการใช้งาน</a>
                            <span class="separator">|</span>
                            <a href="mailto:<EMAIL>">ติดต่อสนับสนุน</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="footer-version">
                        <small class="text-muted">
                            เวอร์ชัน 1.0.0 |
                            สร้างด้วย <i class="fas fa-heart text-danger"></i> โดย Augment Agent
                        </small>
                        <div class="footer-status">
                            <span class="status-indicator online"></span>
                            <small>ระบบออนไลน์</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Admin Scripts -->
    <script>
        // Global admin functions

        // Auto-save functionality
        function enableAutoSave(formId, interval = 30000) {
            const form = document.getElementById(formId);
            if (form) {
                setInterval(() => {
                    const formData = new FormData(form);
                    // Save to localStorage as backup
                    const data = {};
                    for (let [key, value] of formData.entries()) {
                        data[key] = value;
                    }
                    localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
                    console.log('Auto-saved form data');
                }, interval);
            }
        }

        // Notification system
        function showNotification(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        // Confirm delete actions
        function confirmDelete(message = 'ต้องการลบรายการนี้หรือไม่?') {
            return confirm(message);
        }

        // Format numbers
        function formatNumber(num) {
            return new Intl.NumberFormat('th-TH').format(num);
        }

        // Format currency
        function formatCurrency(amount) {
            return new Intl.NumberFormat('th-TH', {
                style: 'currency',
                currency: 'THB'
            }).format(amount);
        }

        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Auto-hide alerts after 5 seconds
            const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 5000);
            });
        });

        // Sidebar responsive behavior
        function initSidebarResponsive() {
            const sidebar = document.getElementById('adminSidebar');
            const navbar = document.querySelector('.admin-navbar');
            const mainContent = document.querySelector('.main-content');

            function updateLayout() {
                if (window.innerWidth <= 768) {
                    // Mobile layout
                    navbar.style.left = '0';
                    mainContent.style.marginLeft = '0';
                } else {
                    // Desktop layout
                    navbar.style.left = 'var(--sidebar-width)';
                    mainContent.style.marginLeft = 'var(--sidebar-width)';
                }
            }

            window.addEventListener('resize', updateLayout);
            updateLayout();
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initSidebarResponsive();

            // Add loading states to buttons
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังประมวลผล...';
                    }
                });
            });
        });
    </script>

    <style>
        .admin-footer {
            background: white;
            border-top: 1px solid #e9ecef;
            padding: 20px 0;
            margin-top: 40px;
            margin-left: var(--sidebar-width);
            transition: all 0.3s ease;
        }

        .footer-info {
            color: var(--dark-color);
        }

        .footer-links {
            margin-top: 5px;
        }

        .footer-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        .separator {
            margin: 0 8px;
            color: #6c757d;
        }

        .footer-version {
            text-align: right;
        }

        .footer-status {
            margin-top: 5px;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-indicator.online {
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .admin-footer {
                margin-left: 0;
            }

            .footer-version {
                text-align: left;
                margin-top: 10px;
            }
        }
    </style>
</body>
</html>