<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Ensure only POST requests are processed for create/update, GET for delete
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['action'])) {
    $action = $_POST['action'];
} elseif ($_SERVER["REQUEST_METHOD"] == "GET" && isset($_GET['action'])) {
    $action = $_GET['action'];
} else {
    $_SESSION['message'] = 'Invalid request method.';
    $_SESSION['message_type'] = 'error';
    header('Location: products.php');
    exit;
}

// --- SIMULATED DATABASE INTERACTIONS (Replace with actual DB operations) ---

if ($action === 'create') {
    // Sanitize and validate inputs
    $name = isset($_POST['name']) ? sanitize_input($_POST['name']) : '';
    $category_id = isset($_POST['category_id']) ? (int)$_POST['category_id'] : 0;
    $description = isset($_POST['description']) ? sanitize_input($_POST['description']) : '';
    $price = isset($_POST['price']) ? filter_var($_POST['price'], FILTER_VALIDATE_FLOAT) : 0;
    $stock_quantity = isset($_POST['stock_quantity']) ? (int)$_POST['stock_quantity'] : 0;
    $sku = isset($_POST['sku']) ? sanitize_input($_POST['sku']) : '';

    // Basic validation
    if (empty($name) || $category_id <= 0 || $price === false || $price < 0 || $stock_quantity < 0) {
        $_SESSION['form_error'] = 'กรอกข้อมูลจำเป็นให้ต้อง: ชื่อสินค้า, หมวดหมู่, ราคา และจำนวนในสต็อก';
        $_SESSION['form_data'] = $_POST; // Send back submitted data
        header('Location: product_form.php'); // Redirect back to form
        exit;
    }

    // Handle file upload (Conceptual - needs more robust handling)
    $image_path_for_db = null;
    if (isset($_FILES['product_image']) && $_FILES['product_image']['error'] == 0) {
        $target_dir = "../images/products/"; // Ensure this directory exists and is writable
        if (!is_dir($target_dir)) {
            mkdir($target_dir, 0755, true);
        }
        $image_filename = time() . '_' . basename($_FILES["product_image"]["name"]);
        $target_file = $target_dir . $image_filename;
        $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));

        // Basic validation for image
        $check = getimagesize($_FILES["product_image"]["tmp_name"]);
        if ($check !== false && in_array($imageFileType, ['jpg', 'jpeg', 'png', 'gif'])) {
            if (move_uploaded_file($_FILES["product_image"]["tmp_name"], $target_file)) {
                $image_path_for_db = "images/products/" . $image_filename; // Path to store in DB
            } else {
                $_SESSION['form_error'] = 'ข้อผิดพลาดในการอัปโหลดภาพ';
                 $_SESSION['form_data'] = $_POST;
                header('Location: product_form.php');
                exit;
            }
        } else {
            $_SESSION['form_error'] = 'ไฟล์อัปโหลดไม่ใช่ภาพ';
             $_SESSION['form_data'] = $_POST;
            header('Location: product_form.php');
            exit;
        }
    }

    // **Simulate Database Insert**
    // In real app: INSERT INTO products (name, category_id, ...) VALUES (?, ?, ...)
    $_SESSION['message'] = "สินค้า '{$name}' ถูกเพิ่มเข้าระบบแล้ว (จำลอง).";
    $_SESSION['message_type'] = 'success';

} elseif ($action === 'update') {
    $product_id = isset($_POST['product_id']) ? (int)$_POST['product_id'] : 0;
    $name = isset($_POST['name']) ? sanitize_input($_POST['name']) : '';
    // ... (sanitize other fields similarly)

    if ($product_id <= 0 || empty($name)) { // Basic validation
        $_SESSION['form_error'] = 'ข้อมูลไม่ถูกต้องในการอัปเดต';
        $_SESSION['form_data'] = $_POST;
        header('Location: product_form.php?id='.$product_id);
        exit;
    }

    // Handle file upload for update (similar to create, possibly delete old image)
    // ... (file upload logic here) ...

    // **Simulate Database Update**
    // In real app: UPDATE products SET name = ?, ... WHERE id = ?
    $_SESSION['message'] = "สินค้า ID {$product_id} (ชื่อ: '{$name}') ถูกอัปเดตแล้ว (จำลอง).";
    $_SESSION['message_type'] = 'success';

} elseif ($action === 'delete') {
    $product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if ($product_id <= 0) {
        $_SESSION['message'] = 'ID สินค้าไม่ถูกต้องในการลบ';
        $_SESSION['message_type'] = 'error';
        header('Location: products.php');
        exit;
    }

    // **Simulate Database Delete**
    // In real app: DELETE FROM products WHERE id = ?
    // Also consider deleting associated images from server
    $_SESSION['message'] = "สินค้า ID {$product_id} ถูกลบออกจากระบบแล้ว (จำลอง).";
    $_SESSION['message_type'] = 'success';

} else {
    $_SESSION['message'] = 'การดำเนินการไม่ถูกจัก';
    $_SESSION['message_type'] = 'error';
}

header('Location: products.php');
exit;
?>
