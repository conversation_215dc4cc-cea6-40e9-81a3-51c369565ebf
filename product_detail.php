<?php
session_start();
require_once './config/database.php';

$pdo = getDbConnection();
$product_id = intval($_GET['id'] ?? 0);

if (!$product_id) {
    header("Location: products.php");
    exit;
}

// ดึงข้อมูลสินค้า
try {
    $stmt = $pdo->prepare("
        SELECT p.*, pc.name as category_name,
               COUNT(r.id) as review_count,
               AVG(r.rating) as avg_rating
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        LEFT JOIN reviews r ON p.id = r.product_id
        WHERE p.id = ? AND p.status = 1
        GROUP BY p.id
    ");
    $stmt->execute([$product_id]);
    $product = $stmt->fetch();

    if (!$product) {
        header("Location: products.php");
        exit;
    }
} catch (Exception $e) {
    die("เกิดข้อผิดพลาด: " . $e->getMessage());
}

// อัพเดตจำนวนการดู
try {
    $stmt = $pdo->prepare("UPDATE products SET views = views + 1 WHERE id = ?");
    $stmt->execute([$product_id]);
} catch (Exception $e) {
    // ไม่ต้องทำอะไรถ้าอัพเดตไม่ได้
}

// ดึงรูปภาพสินค้า
try {
    $stmt = $pdo->prepare("SELECT * FROM product_images WHERE product_id = ? ORDER BY image_type = 'main' DESC, id ASC");
    $stmt->execute([$product_id]);
    $images = $stmt->fetchAll();
} catch (Exception $e) {
    $images = [];
}

// ดึงรีวิว
try {
    $stmt = $pdo->prepare("
        SELECT r.*, c.name as customer_name
        FROM reviews r
        LEFT JOIN customers c ON r.customer_id = c.id
        WHERE r.product_id = ?
        ORDER BY r.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$product_id]);
    $reviews = $stmt->fetchAll();
} catch (Exception $e) {
    $reviews = [];
}

// ดึงสินค้าที่เกี่ยวข้อง
try {
    $stmt = $pdo->prepare("
        SELECT p.*, pi.file_id as product_image
        FROM products p
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.image_type = 'main'
        WHERE p.category_id = ? AND p.id != ? AND p.status = 1
        ORDER BY RAND()
        LIMIT 4
    ");
    $stmt->execute([$product['category_id'], $product_id]);
    $related_products = $stmt->fetchAll();
} catch (Exception $e) {
    $related_products = [];
}

// คำนวณ rating distribution
$rating_distribution = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];
if ($reviews) {
    try {
        $stmt = $pdo->prepare("
            SELECT rating, COUNT(*) as count
            FROM reviews
            WHERE product_id = ?
            GROUP BY rating
        ");
        $stmt->execute([$product_id]);
        $ratings = $stmt->fetchAll();

        foreach ($ratings as $rating) {
            $rating_distribution[$rating['rating']] = $rating['count'];
        }
    } catch (Exception $e) {
        // ใช้ค่าเริ่มต้น
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($product['name']) ?> - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
        }

        .product-gallery {
            position: sticky;
            top: 20px;
        }

        .main-image {
            width: 100%;
            height: 500px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .main-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .main-image .placeholder {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #dee2e6;
            font-size: 4rem;
        }

        .thumbnail-images {
            display: flex;
            gap: 10px;
            overflow-x: auto;
        }

        .thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            opacity: 0.7;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .thumbnail:hover,
        .thumbnail.active {
            opacity: 1;
            transform: scale(1.05);
        }

        .thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .product-info {
            padding: 20px 0;
        }

        .product-category {
            color: #11be97;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        .product-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            line-height: 1.2;
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .rating-stars {
            color: #ffc107;
            font-size: 1.2rem;
        }

        .rating-text {
            color: #6c757d;
        }

        .product-price {
            font-size: 2.5rem;
            font-weight: 700;
            color: #11be97;
            margin-bottom: 20px;
        }

        .product-description {
            color: #6c757d;
            line-height: 1.8;
            margin-bottom: 30px;
            font-size: 1.1rem;
        }

        .product-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }

        .btn-design {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-design:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(17, 190, 151, 0.3);
            color: white;
        }

        .btn-favorite {
            width: 60px;
            height: 60px;
            border: 2px solid #e9ecef;
            background: white;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: #6c757d;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-favorite:hover,
        .btn-favorite.active {
            border-color: #11be97;
            color: #11be97;
            background: rgba(17, 190, 151, 0.1);
        }

        .product-features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .feature-item:last-child {
            margin-bottom: 0;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #11be97;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .reviews-section {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }

        .reviews-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 30px;
        }

        .reviews-summary {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .rating-overview {
            text-align: center;
            padding: 20px;
            border-right: 1px solid #e9ecef;
        }

        .overall-rating {
            font-size: 3rem;
            font-weight: 700;
            color: #11be97;
            margin-bottom: 10px;
        }

        .rating-distribution {
            flex: 1;
            padding: 20px;
        }

        .rating-bar {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .rating-label {
            width: 30px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .rating-progress {
            flex: 1;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            margin: 0 10px;
            overflow: hidden;
        }

        .rating-fill {
            height: 100%;
            background: #ffc107;
            transition: width 0.3s ease;
        }

        .rating-count {
            width: 30px;
            font-size: 0.9rem;
            color: #6c757d;
            text-align: right;
        }

        .review-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .review-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 15px;
        }

        .reviewer-info {
            display: flex;
            align-items: center;
        }

        .reviewer-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #11be97;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 15px;
        }

        .reviewer-details h6 {
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .review-date {
            color: #6c757d;
            font-size: 0.85rem;
        }

        .review-rating {
            color: #ffc107;
        }

        .review-content {
            color: #6c757d;
            line-height: 1.6;
        }

        .related-products {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }

        .related-product-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            height: 100%;
        }

        .related-product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .related-product-image {
            width: 100%;
            height: 200px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .related-product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .related-product-content {
            padding: 20px;
        }

        .breadcrumb {
            background: none;
            padding: 20px 0;
        }

        .breadcrumb-item a {
            color: #11be97;
            text-decoration: none;
        }

        @media (max-width: 768px) {
            .product-title {
                font-size: 1.8rem;
            }

            .product-price {
                font-size: 2rem;
            }

            .product-actions {
                flex-direction: column;
            }

            .btn-favorite {
                width: 100%;
                height: 50px;
            }

            .rating-overview {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
                margin-bottom: 20px;
            }

            .reviews-summary .row {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);">
        <div class="container">
            <a class="navbar-brand fw-bold" href="index.php">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">สินค้า</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="design.php">ออกแบบเอง</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">ติดต่อ</a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <?php if (isset($_SESSION['customer_logged_in'])): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-2"></i><?= htmlspecialchars($_SESSION['customer_name']) ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="customer_dashboard.php">
                                    <i class="fas fa-tachometer-alt me-2"></i>แดชบอร์ด
                                </a></li>
                                <li><a class="dropdown-item" href="customer_orders.php">
                                    <i class="fas fa-shopping-cart me-2"></i>คำสั่งซื้อ
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-2"></i>เข้าสู่ระบบ
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">หน้าแรก</a></li>
                <li class="breadcrumb-item"><a href="products.php">สินค้า</a></li>
                <?php if ($product['category_name']): ?>
                    <li class="breadcrumb-item">
                        <a href="products.php?category=<?= $product['category_id'] ?>">
                            <?= htmlspecialchars($product['category_name']) ?>
                        </a>
                    </li>
                <?php endif; ?>
                <li class="breadcrumb-item active"><?= htmlspecialchars($product['name']) ?></li>
            </ol>
        </nav>

        <div class="row">
            <!-- Product Gallery -->
            <div class="col-lg-6">
                <div class="product-gallery">
                    <div class="main-image" id="mainImage">
                        <?php if ($images && $images[0]['file_id']): ?>
                            <img src="../uploads/products/<?= htmlspecialchars($images[0]['file_id']) ?>"
                                 alt="<?= htmlspecialchars($product['name']) ?>" id="mainImg">
                        <?php else: ?>
                            <div class="placeholder">
                                <i class="fas fa-tshirt"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if (count($images) > 1): ?>
                        <div class="thumbnail-images">
                            <?php foreach ($images as $index => $image): ?>
                                <div class="thumbnail <?= $index === 0 ? 'active' : '' ?>"
                                     onclick="changeMainImage('<?= htmlspecialchars($image['file_id']) ?>', this)">
                                    <img src="../uploads/products/<?= htmlspecialchars($image['file_id']) ?>"
                                         alt="Product image">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Product Info -->
            <div class="col-lg-6">
                <div class="product-info">
                    <div class="product-category">
                        <?= htmlspecialchars($product['category_name'] ?: 'ไม่ระบุหมวดหมู่') ?>
                    </div>

                    <h1 class="product-title">
                        <?= htmlspecialchars($product['name']) ?>
                    </h1>

                    <?php if ($product['review_count'] > 0): ?>
                        <div class="product-rating">
                            <div class="rating-stars">
                                <?php
                                $rating = round($product['avg_rating'], 1);
                                for ($i = 1; $i <= 5; $i++) {
                                    if ($i <= $rating) {
                                        echo '<i class="fas fa-star"></i>';
                                    } elseif ($i - 0.5 <= $rating) {
                                        echo '<i class="fas fa-star-half-alt"></i>';
                                    } else {
                                        echo '<i class="far fa-star"></i>';
                                    }
                                }
                                ?>
                            </div>
                            <span class="rating-text">
                                <?= $rating ?>/5 (<?= $product['review_count'] ?> รีวิว)
                            </span>
                        </div>
                    <?php endif; ?>

                    <div class="product-price">
                        ฿<?= number_format($product['price']) ?>
                    </div>

                    <div class="product-description">
                        <?= nl2br(htmlspecialchars($product['description'] ?: 'ไม่มีรายละเอียดสินค้า')) ?>
                    </div>

                    <div class="product-actions">
                        <button class="btn-design" onclick="startDesign()">
                            <i class="fas fa-palette me-2"></i>เริ่มออกแบบ
                        </button>
                        <button class="btn-favorite" onclick="toggleFavorite()">
                            <i class="far fa-heart"></i>
                        </button>
                    </div>

                    <div class="product-features">
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-shipping-fast"></i>
                            </div>
                            <div>
                                <strong>จัดส่งฟรี</strong><br>
                                <small class="text-muted">สำหรับคำสั่งซื้อมากกว่า ฿1,000</small>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-palette"></i>
                            </div>
                            <div>
                                <strong>ออกแบบได้ตามต้องการ</strong><br>
                                <small class="text-muted">ระบบออกแบบออนไลน์แบบ Real-time</small>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-medal"></i>
                            </div>
                            <div>
                                <strong>คุณภาพสูง</strong><br>
                                <small class="text-muted">ผ้าคุณภาพพรีเมี่ยม ทนทาน</small>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">
                                <i class="fas fa-undo"></i>
                            </div>
                            <div>
                                <strong>รับประกันความพึงพอใจ</strong><br>
                                <small class="text-muted">คืนเงิน 100% หากไม่พอใจ</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <?php if ($product['review_count'] > 0): ?>
            <div class="reviews-section">
                <div class="reviews-header">
                    <h3>รีวิวจากลูกค้า</h3>
                </div>

                <!-- Reviews Summary -->
                <div class="reviews-summary">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="rating-overview">
                                <div class="overall-rating">
                                    <?= number_format($product['avg_rating'], 1) ?>
                                </div>
                                <div class="rating-stars mb-2">
                                    <?php
                                    $rating = round($product['avg_rating'], 1);
                                    for ($i = 1; $i <= 5; $i++) {
                                        if ($i <= $rating) {
                                            echo '<i class="fas fa-star"></i>';
                                        } elseif ($i - 0.5 <= $rating) {
                                            echo '<i class="fas fa-star-half-alt"></i>';
                                        } else {
                                            echo '<i class="far fa-star"></i>';
                                        }
                                    }
                                    ?>
                                </div>
                                <div class="text-muted">
                                    จาก <?= $product['review_count'] ?> รีวิว
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="rating-distribution">
                                <?php for ($i = 5; $i >= 1; $i--): ?>
                                    <div class="rating-bar">
                                        <div class="rating-label"><?= $i ?> ⭐</div>
                                        <div class="rating-progress">
                                            <div class="rating-fill" style="width: <?= $product['review_count'] > 0 ? ($rating_distribution[$i] / $product['review_count']) * 100 : 0 ?>%"></div>
                                        </div>
                                        <div class="rating-count"><?= $rating_distribution[$i] ?></div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Individual Reviews -->
                <?php foreach ($reviews as $review): ?>
                    <div class="review-item">
                        <div class="review-header">
                            <div class="reviewer-info">
                                <div class="reviewer-avatar">
                                    <?= strtoupper(substr($review['customer_name'] ?: 'ลูกค้า', 0, 1)) ?>
                                </div>
                                <div class="reviewer-details">
                                    <h6><?= htmlspecialchars($review['customer_name'] ?: 'ลูกค้า') ?></h6>
                                    <div class="review-date">
                                        <?= date('d/m/Y', strtotime($review['created_at'])) ?>
                                    </div>
                                </div>
                            </div>
                            <div class="review-rating">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <?php if ($i <= $review['rating']): ?>
                                        <i class="fas fa-star"></i>
                                    <?php else: ?>
                                        <i class="far fa-star"></i>
                                    <?php endif; ?>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <div class="review-content">
                            <?= nl2br(htmlspecialchars($review['comment'])) ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Related Products -->
        <?php if ($related_products): ?>
            <div class="related-products">
                <h3 class="mb-4">สินค้าที่เกี่ยวข้อง</h3>
                <div class="row">
                    <?php foreach ($related_products as $related): ?>
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="related-product-card" onclick="window.location.href='product_detail.php?id=<?= $related['id'] ?>'">
                                <div class="related-product-image">
                                    <?php if ($related['product_image']): ?>
                                        <img src="../uploads/products/<?= htmlspecialchars($related['product_image']) ?>"
                                             alt="<?= htmlspecialchars($related['name']) ?>">
                                    <?php else: ?>
                                        <i class="fas fa-tshirt text-muted" style="font-size: 3rem;"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="related-product-content">
                                    <h6><?= htmlspecialchars($related['name']) ?></h6>
                                    <div class="text-primary fw-bold">
                                        ฿<?= number_format($related['price']) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-5 py-4">
        <div class="container">
            <div class="text-center">
                <p>&copy; 2024 GT Sport Design. สงวนลิขสิทธิ์.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Image gallery functionality
        function changeMainImage(imageSrc, thumbnail) {
            document.getElementById('mainImg').src = `../uploads/products/${imageSrc}`;

            // Update active thumbnail
            document.querySelectorAll('.thumbnail').forEach(thumb => {
                thumb.classList.remove('active');
            });
            thumbnail.classList.add('active');
        }

        // Start design process
        function startDesign() {
            window.location.href = `design.php?product_id=<?= $product_id ?>`;
        }

        // Toggle favorite
        function toggleFavorite() {
            const btn = document.querySelector('.btn-favorite');
            const icon = btn.querySelector('i');

            if (icon.classList.contains('far')) {
                icon.classList.remove('far');
                icon.classList.add('fas');
                btn.classList.add('active');
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                btn.classList.remove('active');
            }

            // TODO: Send request to server to save favorite status
        }

        // Smooth scrolling for review link
        function scrollToReviews() {
            document.querySelector('.reviews-section').scrollIntoView({
                behavior: 'smooth'
            });
        }
    </script>
</body>
</html>
