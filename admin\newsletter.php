<?php
/**
 * GT-SportDesign Admin - Newsletter Management
 * Professional newsletter subscriber management
 * Version: 2.0 - Production Ready
 */

require_once 'includes/session.php';
require_once '../config/database.php';

// Require admin authentication
requireAdminAuth();

$db = getDbConnection();
$subscribers = [];
$total_subscribers = 0;
$active_subscribers = 0;

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $subscriber_id = intval($_POST['subscriber_id'] ?? 0);
    
    if ($action === 'toggle_status' && $subscriber_id > 0) {
        try {
            $stmt = $db->prepare("UPDATE newsletter_subscribers SET is_active = NOT is_active WHERE id = ?");
            $stmt->execute([$subscriber_id]);
            $success_message = 'อัปเดตสถานะเรียบร้อย';
        } catch (Exception $e) {
            $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        }
    }
    
    if ($action === 'delete' && $subscriber_id > 0) {
        try {
            $stmt = $db->prepare("DELETE FROM newsletter_subscribers WHERE id = ?");
            $stmt->execute([$subscriber_id]);
            $success_message = 'ลบผู้สมัครเรียบร้อย';
        } catch (Exception $e) {
            $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
        }
    }
    
    if ($action === 'export') {
        try {
            $stmt = $db->query("SELECT email, name, subscribed_at FROM newsletter_subscribers WHERE is_active = 1 ORDER BY subscribed_at DESC");
            $subscribers_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="newsletter_subscribers_' . date('Y-m-d') . '.csv"');
            
            $output = fopen('php://output', 'w');
            fputcsv($output, ['Email', 'Name', 'Subscribed Date']);
            
            foreach ($subscribers_data as $subscriber) {
                fputcsv($output, [
                    $subscriber['email'],
                    $subscriber['name'] ?: 'N/A',
                    date('Y-m-d H:i:s', strtotime($subscriber['subscribed_at']))
                ]);
            }
            
            fclose($output);
            exit;
        } catch (Exception $e) {
            $error_message = 'เกิดข้อผิดพลาดในการส่งออกข้อมูล: ' . $e->getMessage();
        }
    }
}

// Get filter parameters
$filter = $_GET['filter'] ?? 'all';
$search = trim($_GET['search'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build WHERE clause
$where_conditions = [];
$params = [];

if ($filter === 'active') {
    $where_conditions[] = 'is_active = 1';
} elseif ($filter === 'inactive') {
    $where_conditions[] = 'is_active = 0';
}

if (!empty($search)) {
    $where_conditions[] = '(email LIKE ? OR name LIKE ?)';
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    // Create table if not exists
    $db->exec("
        CREATE TABLE IF NOT EXISTS newsletter_subscribers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            email VARCHAR(255) NOT NULL UNIQUE,
            name VARCHAR(255),
            is_active TINYINT(1) DEFAULT 1,
            subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            unsubscribed_at TIMESTAMP NULL,
            INDEX idx_email (email),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Get total count
    $count_stmt = $db->prepare("SELECT COUNT(*) FROM newsletter_subscribers $where_clause");
    $count_stmt->execute($params);
    $total_subscribers = $count_stmt->fetchColumn();
    $total_pages = ceil($total_subscribers / $per_page);
    
    // Get active count
    $active_stmt = $db->query("SELECT COUNT(*) FROM newsletter_subscribers WHERE is_active = 1");
    $active_subscribers = $active_stmt->fetchColumn();
    
    // Get subscribers
    $stmt = $db->prepare("
        SELECT * FROM newsletter_subscribers 
        $where_clause 
        ORDER BY subscribed_at DESC 
        LIMIT $per_page OFFSET $offset
    ");
    $stmt->execute($params);
    $subscribers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = 'เกิดข้อผิดพลาดในการโหลดข้อมูล: ' . $e->getMessage();
}

function getStatusBadge($is_active) {
    return $is_active ? '<span class="badge bg-success">ใช้งาน</span>' : '<span class="badge bg-secondary">ไม่ใช้งาน</span>';
}

function formatDate($date) {
    return date('d/m/Y H:i', strtotime($date));
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการ Newsletter - GT-SportDesign Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
        }
        
        .stats-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: 700;
            color: #eb4e17;
        }
        
        .subscriber-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .subscriber-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .filter-tabs {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .btn-action {
            padding: 5px 10px;
            font-size: 0.8rem;
            border-radius: 5px;
            margin: 2px;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1><i class="fas fa-envelope-open me-2"></i>จัดการ Newsletter</h1>
                    <p class="mb-0">ผู้สมัครรับข่าวสารและการจัดการ</p>
                </div>
                <div class="col-md-6 text-end">
                    <a href="dashboard.php" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i>กลับหน้าหลัก
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container">
        <!-- Alerts -->
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <!-- Stats -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <div class="stats-number"><?php echo $total_subscribers; ?></div>
                    <div class="text-muted">ผู้สมัครทั้งหมด</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <div class="stats-number"><?php echo $active_subscribers; ?></div>
                    <div class="text-muted">ผู้สมัครที่ใช้งาน</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <div class="stats-number"><?php echo $total_subscribers - $active_subscribers; ?></div>
                    <div class="text-muted">ผู้สมัครที่ไม่ใช้งาน</div>
                </div>
            </div>
        </div>
        
        <!-- Filter and Search -->
        <div class="filter-tabs">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="btn-group" role="group">
                        <a href="?filter=all&search=<?php echo urlencode($search); ?>" 
                           class="btn <?php echo $filter === 'all' ? 'btn-primary' : 'btn-outline-primary'; ?>">
                            ทั้งหมด (<?php echo $total_subscribers; ?>)
                        </a>
                        <a href="?filter=active&search=<?php echo urlencode($search); ?>" 
                           class="btn <?php echo $filter === 'active' ? 'btn-success' : 'btn-outline-success'; ?>">
                            ใช้งาน (<?php echo $active_subscribers; ?>)
                        </a>
                        <a href="?filter=inactive&search=<?php echo urlencode($search); ?>" 
                           class="btn <?php echo $filter === 'inactive' ? 'btn-secondary' : 'btn-outline-secondary'; ?>">
                            ไม่ใช้งาน
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex gap-2">
                        <form method="GET" class="d-flex flex-grow-1">
                            <input type="hidden" name="filter" value="<?php echo $filter; ?>">
                            <input type="text" name="search" class="form-control me-2" 
                                   placeholder="ค้นหาอีเมลหรือชื่อ..." value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="export">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Subscribers List -->
        <?php if (!empty($subscribers)): ?>
        <?php foreach ($subscribers as $subscriber): ?>
        <div class="subscriber-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center mb-2">
                        <h6 class="mb-0 me-3"><?php echo htmlspecialchars($subscriber['email']); ?></h6>
                        <?php echo getStatusBadge($subscriber['is_active']); ?>
                    </div>
                    
                    <div class="text-muted small">
                        <?php if ($subscriber['name']): ?>
                        <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($subscriber['name']); ?>
                        <?php endif; ?>
                        <i class="fas fa-calendar ms-3 me-1"></i>สมัครเมื่อ: <?php echo formatDate($subscriber['subscribed_at']); ?>
                        <?php if (!$subscriber['is_active'] && $subscriber['unsubscribed_at']): ?>
                        <i class="fas fa-times-circle ms-3 me-1"></i>ยกเลิกเมื่อ: <?php echo formatDate($subscriber['unsubscribed_at']); ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-4 text-end">
                    <div class="btn-group">
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="toggle_status">
                            <input type="hidden" name="subscriber_id" value="<?php echo $subscriber['id']; ?>">
                            <button type="submit" class="btn <?php echo $subscriber['is_active'] ? 'btn-warning' : 'btn-success'; ?> btn-action">
                                <i class="fas fa-<?php echo $subscriber['is_active'] ? 'pause' : 'play'; ?> me-1"></i>
                                <?php echo $subscriber['is_active'] ? 'ปิดใช้งาน' : 'เปิดใช้งาน'; ?>
                            </button>
                        </form>
                        
                        <form method="POST" class="d-inline" onsubmit="return confirm('ต้องการลบผู้สมัครนี้หรือไม่?')">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="subscriber_id" value="<?php echo $subscriber['id']; ?>">
                            <button type="submit" class="btn btn-danger btn-action">
                                <i class="fas fa-trash me-1"></i>ลบ
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <nav aria-label="Subscribers pagination">
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page-1; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>">
                        <?php echo $i; ?>
                    </a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page+1; ?>&filter=<?php echo $filter; ?>&search=<?php echo urlencode($search); ?>">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        
        <?php else: ?>
        <!-- No Subscribers -->
        <div class="text-center py-5">
            <i class="fas fa-envelope-open fa-3x text-muted mb-3"></i>
            <h3>ไม่มีผู้สมัครรับข่าวสาร</h3>
            <p class="text-muted">ยังไม่มีผู้สมัครรับข่าวสาร</p>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
