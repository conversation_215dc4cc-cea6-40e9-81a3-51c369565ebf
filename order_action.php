<?php
session_start();
require_once './config/database.php';

header('Content-Type: application/json');

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['customer_logged_in'])) {
    echo json_encode(['success' => false, 'message' => 'กรุณาเข้าสู่ระบบ']);
    exit;
}

$pdo = getDbConnection();
$customer_id = $_SESSION['customer_id'];

try {
    // ดึงข้อมูลจาก request
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    $order_id = intval($input['order_id'] ?? 0);

    if (!$action || !$order_id) {
        throw new Exception('ข้อมูลไม่ครบถ้วน');
    }

    // ตรวจสอบว่าคำสั่งซื้อเป็นของลูกค้าคนนี้
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ? AND customer_id = ?");
    $stmt->execute([$order_id, $customer_id]);
    $order = $stmt->fetch();

    if (!$order) {
        throw new Exception('ไม่พบคำสั่งซื้อ หรือคุณไม่มีสิทธิ์เข้าถึง');
    }

    switch ($action) {
        case 'cancel':
            // ยกเลิกคำสั่งซื้อ (ได้เฉพาะสถานะ pending)
            if ($order['status'] !== 'pending') {
                throw new Exception('ไม่สามารถยกเลิกคำสั่งซื้อนี้ได้ เนื่องจากอยู่ในขั้นตอนการผลิตแล้ว');
            }

            $stmt = $pdo->prepare("
                UPDATE orders
                SET status = 'cancelled',
                    cancelled_at = NOW(),
                    updated_at = NOW()
                WHERE id = ? AND customer_id = ?
            ");
            $stmt->execute([$order_id, $customer_id]);

            // บันทึกประวัติการเปลี่ยนแปลงสถานะ
            $stmt = $pdo->prepare("
                INSERT INTO order_status_history (order_id, status, notes, created_by, created_at)
                VALUES (?, 'cancelled', 'ยกเลิกโดยลูกค้า', ?, NOW())
            ");
            $stmt->execute([$order_id, $customer_id]);

            echo json_encode(['success' => true, 'message' => 'ยกเลิกคำสั่งซื้อเรียบร้อยแล้ว']);
            break;

        case 'confirm_received':
            // ยืนยันได้รับสินค้า (ได้เฉพาะสถานะ shipped)
            if ($order['status'] !== 'shipped') {
                throw new Exception('ไม่สามารถยืนยันได้รับสินค้าได้ เนื่องจากสินค้ายังไม่ได้จัดส่ง');
            }

            $stmt = $pdo->prepare("
                UPDATE orders
                SET status = 'completed',
                    completed_at = NOW(),
                    updated_at = NOW()
                WHERE id = ? AND customer_id = ?
            ");
            $stmt->execute([$order_id, $customer_id]);

            // บันทึกประวัติการเปลี่ยนแปลงสถานะ
            $stmt = $pdo->prepare("
                INSERT INTO order_status_history (order_id, status, notes, created_by, created_at)
                VALUES (?, 'completed', 'ยืนยันได้รับสินค้าโดยลูกค้า', ?, NOW())
            ");
            $stmt->execute([$order_id, $customer_id]);

            echo json_encode(['success' => true, 'message' => 'ยืนยันได้รับสินค้าเรียบร้อยแล้ว']);
            break;

        default:
            throw new Exception('การดำเนินการไม่ถูกต้อง');
    }

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
