<?php
/**
 * GT-SportDesign Connection Test
 * ไฟล์ทดสอบการเชื่อมต่อฐานข้อมูลและตรวจสอบระบบ
 */

// แสดงข้อ้พลาด้้งหมด
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// ตรวจสอบว่าไฟล์ config ไม่
$config_exists = file_exists('config/database.php');
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการเชื่อมต่อ GT-SportDesign</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-item { margin-bottom: 15px; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">ทดสอบการเชื่อมต่อ GT-SportDesign</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>ข้อมูลระบบ</h5>
            </div>
            <div class="card-body">
                <div class="test-item <?php echo version_compare(PHP_VERSION, '7.4.0', '>=') ? 'success' : 'error'; ?>">
                    <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?>
                    <?php if (version_compare(PHP_VERSION, '7.4.0', '<')): ?>
                        <div class="mt-2 small">ต้องการ PHP 7.4ใหม่กว่า</div>
                    <?php endif; ?>
                </div>
                
                <div class="test-item <?php echo extension_loaded('pdo') && extension_loaded('pdo_mysql') ? 'success' : 'error'; ?>">
                    <strong>PDO Extension:</strong> 
                    <?php echo extension_loaded('pdo') && extension_loaded('pdo_mysql') ? 'ตั้งแล้ว' : 'ไม่ได้ตั้ง'; ?>
                </div>
                
                <div class="test-item <?php echo extension_loaded('gd') ? 'success' : 'warning'; ?>">
                    <strong>GD Extension:</strong> 
                    <?php echo extension_loaded('gd') ? 'ตั้งแล้ว' : 'ไม่ได้ตั้ง (จำเป็นภาพ)'; ?>
                </div>
                
                <div class="test-item <?php echo extension_loaded('mbstring') ? 'success' : 'error'; ?>">
                    <strong>Mbstring Extension:</strong> 
                    <?php echo extension_loaded('mbstring') ? 'ตั้งแล้ว' : 'ไม่ได้ตั้ง'; ?>
                </div>
                
                <div class="test-item <?php echo $config_exists ? 'success' : 'warning'; ?>">
                    <strong>Config File:</strong> 
                    <?php echo $config_exists ? 'อยู่แล้ว' : 'ไม่ (จำเป็นต้องตั้งระบบ)'; ?>
                </div>
            </div>
        </div>
        
        <?php if ($config_exists): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5>ทดสอบการเชื่อมต่อฐานข้อมูล</h5>
            </div>
            <div class="card-body">
                <?php
                try {
                    require_once 'config/database.php';
                    
                    if (isset($pdo)) {
                        echo '<div class="test-item success"><strong>การเชื่อมต่อฐานข้อมูล:</strong> สำเร็จ</div>';
                        
                        // ทดสอบการสร้างตาราง
                        $stmt = $pdo->query("SHOW TABLES");
                        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        
                        echo '<div class="test-item info"><strong>จำนวนตารางในฐานข้อมูล:</strong> ' . count($tables) . ' ตาราง</div>';
                        
                        if (count($tables) < 5) {
                            echo '<div class="test-item warning"><strong>คำเตือน:</strong> ตารางน้อยกว่าควรจะเป็น อาจต้องรันไฟล์ fix_database.php</div>';
                        }
                    } else {
                        echo '<div class="test-item error"><strong>การเชื่อมต่อฐานข้อมูล:</strong> ไม่สามารถเชื่อมต่อได้</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="test-item error"><strong>การเชื่อมต่อฐานข้อมูล:</strong> ' . $e->getMessage() . '</div>';
                }
                ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>ทดสอบการเขียนไฟล์</h5>
            </div>
            <div class="card-body">
                <?php
                $directories = [
                    '.' => 'โฟลเดอร์',
                    './config' => 'โฟลเดอร์ config',
                    './uploads' => 'โฟลเดอร์ uploads'
                ];
                
                foreach ($directories as $dir => $name) {
                    if (!file_exists($dir)) {
                        echo '<div class="test-item error"><strong>' . $name . ':</strong> ไม่พบโฟลเดอร์</div>';
                        continue;
                    }
                    
                    $is_writable = is_writable($dir);
                    $perms = substr(sprintf('%o', fileperms($dir)), -4);
                    
                    echo '<div class="test-item ' . ($is_writable ? 'success' : 'error') . '">';
                    echo '<strong>' . $name . ':</strong> ' . ($is_writable ? 'ได้' : 'ไม่ได้');
                    echo ' (์: ' . $perms . ')';
                    echo '</div>';
                }
                ?>
            </div>
        </div>
        
        <div class="d-flex justify-content-between">
            <a href="index.php" class="btn btn-secondary">หน้าแรก</a>
            <?php if (!$config_exists): ?>
            <a href="install.php" class="btn btn-primary">ไปหน้าตั้งค่า</a>
            <?php else: ?>
            <a href="fix_database.php" class="btn btn-warning">แก้ไขฐานข้อมูล</a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>


