<?php
/**
 * GT-SportDesign - Customer Reviews
 * Professional testimonials and reviews page
 * Version: 2.0 - Production Ready
 */

session_start();
require_once './config/database.php';

// Get database connection
$db = getDbConnection();

// Get reviews from database
$reviews = [];
$average_rating = 0;
$total_reviews = 0;

if ($db) {
    try {
        // Get reviews with pagination
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $per_page = 12;
        $offset = ($page - 1) * $per_page;

        // Count total reviews
        $count_stmt = $db->prepare("SELECT COUNT(*) FROM reviews WHERE is_approved = 1");
        $count_stmt->execute();
        $total_reviews = $count_stmt->fetchColumn();

        // Get reviews
        $stmt = $db->prepare("
            SELECT r.*, p.name as product_name
            FROM reviews r
            LEFT JOIN products p ON r.product_id = p.id
            WHERE r.is_approved = 1
            ORDER BY r.created_at DESC
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$per_page, $offset]);
        $reviews = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate average rating
        if ($total_reviews > 0) {
            $avg_stmt = $db->prepare("SELECT AVG(rating) FROM reviews WHERE is_approved = 1");
            $avg_stmt->execute();
            $average_rating = round($avg_stmt->fetchColumn(), 1);
        }

    } catch (PDOException $e) {
        error_log("Reviews page error: " . $e->getMessage());
    }
}

// If no reviews in database, use sample data
if (empty($reviews)) {
    $reviews = [
        [
            'id' => 1,
            'customer_name' => 'คุณสมชาย ใจดี',
            'rating' => 5,
            'review_text' => 'เสื้อมีคุณภาพดีมาก ผ้านุ่ม สีสวย ตรงตามที่สั่ง ประทับใจมากครับ',
            'product_name' => 'เสื้อกีฬาฟุตบอล',
            'created_at' => '2024-01-15',
            'customer_image' => 'https://via.placeholder.com/60x60?text=S',
            'images' => json_encode(['https://via.placeholder.com/300x300?text=Football+Jersey'])
        ],
        [
            'id' => 2,
            'customer_name' => 'คุณสุดา รักงาน',
            'rating' => 5,
            'review_text' => 'บริการดีเยี่ยม ส่งของรวดเร็ว เสื้อสวยมาก ทีมงานใส่แล้วดูดีมากค่ะ',
            'product_name' => 'เสื้อบริษัท',
            'created_at' => '2024-01-10',
            'customer_image' => 'https://via.placeholder.com/60x60?text=S',
            'images' => json_encode(['https://via.placeholder.com/300x300?text=Company+Shirt'])
        ],
        [
            'id' => 3,
            'customer_name' => 'คุณวิชัย กีฬาดี',
            'rating' => 4,
            'review_text' => 'เสื้อมีคุณภาพดี ราคาเหมาะสม แต่การส่งของช้าไปหน่อย โดยรวมพอใจครับ',
            'product_name' => 'เสื้อกีฬาบาสเกตบอล',
            'created_at' => '2024-01-05',
            'customer_image' => 'https://via.placeholder.com/60x60?text=W',
            'images' => json_encode(['https://via.placeholder.com/300x300?text=Basketball+Jersey'])
        ],
        [
            'id' => 4,
            'customer_name' => 'คุณมาลี สวยงาม',
            'rating' => 5,
            'review_text' => 'ประทับใจมากค่ะ เสื้อสวย คุณภาพดี ใส่สบาย ราคาดี จะสั่งอีกแน่นอน',
            'product_name' => 'เสื้อกิจกรรม',
            'created_at' => '2023-12-28',
            'customer_image' => 'https://via.placeholder.com/60x60?text=M',
            'images' => json_encode(['https://via.placeholder.com/300x300?text=Activity+Shirt'])
        ]
    ];

    $total_reviews = count($reviews);
    $average_rating = array_sum(array_column($reviews, 'rating')) / count($reviews);
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>รีวิวลูกค้า - Finix Sports</title>
    <meta name="description" content="ดูรีวิวและผลงานของ Finix Sports จากลูกค้าจริง เสื้อกีฬาคุณภาพสูง">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<?php include './includes/header.php'; ?>
<body>
 

    <!-- Hero Section -->
    <section class="hero-section py-5 bg-light">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="hero-title">รีวิวลูกค้า</h1>
                    <p class="hero-subtitle">ความพอใจของลูกค้าคือความภาคภูมิใจของเรา</p>
                </div>
                <div class="col-lg-4">
                    <div class="rating-summary">
                        <div class="rating-score">
                            <span class="score"><?php echo number_format($average_rating, 1); ?></span>
                            <div class="stars">
                                <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star <?php echo $i <= $average_rating ? 'text-warning' : 'text-muted'; ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <p class="total-reviews"><?php echo number_format($total_reviews); ?> รีวิว</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Reviews Section -->
    <section class="reviews-section py-5">
        <div class="container">
            <div class="row g-4">
                <?php foreach ($reviews as $review): ?>
                    <div class="col-lg-6">
                        <div class="review-card">
                            <div class="review-header">
                                <div class="customer-info">
                                    <img src="<?php echo htmlspecialchars($review['customer_image'] ?? 'https://via.placeholder.com/60x60?text=U'); ?>"
                                         alt="<?php echo htmlspecialchars($review['customer_name']); ?>"
                                         class="customer-avatar">
                                    <div class="customer-details">
                                        <h6 class="customer-name"><?php echo htmlspecialchars($review['customer_name']); ?></h6>
                                        <div class="rating">
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                                <i class="fas fa-star <?php echo $i <= $review['rating'] ? 'text-warning' : 'text-muted'; ?>"></i>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="review-date">
                                    <?php echo date('d/m/Y', strtotime($review['created_at'])); ?>
                                </div>
                            </div>

                            <div class="review-content">
                                <p class="review-text"><?php echo htmlspecialchars($review['review_text']); ?></p>

                                <?php if (!empty($review['product_name'])): ?>
                                    <div class="product-info">
                                        <small class="text-muted">สินค้า: <?php echo htmlspecialchars($review['product_name']); ?></small>
                                    </div>
                                <?php endif; ?>

                                <?php
                                $images = json_decode($review['images'] ?? '[]', true);
                                if (!empty($images)): ?>
                                    <div class="review-images">
                                        <?php foreach ($images as $image): ?>
                                            <img src="<?php echo htmlspecialchars($image); ?>"
                                                 alt="รูปภาพรีวิว"
                                                 class="review-image"
                                                 onclick="openImageModal('<?php echo htmlspecialchars($image); ?>')">
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section py-5 bg-primary text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h3>พร้อมสั่งผลิตเสื้อของคุณแล้วหรือยัง?</h3>
                    <p class="mb-0">เริ่มต้นสร้างเสื้อที่ใช่สำหรับทีมของคุณวันนี้</p>
                </div>
                <div class="col-lg-4 text-lg-end">
                    <a href="design.php" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-palette"></i> ออกแบบเอง
                    </a>
                    <a href="contact.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone"></i> ติดต่อเรา
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รูปภาพรีวิว</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="รูปภาพรีวิว" class="img-fluid">
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
   <?php include './includes/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/main.js"></script>

    <style>
    /* Reviews Page Styles */
    .rating-summary {
        background: white;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        text-align: center;
    }

    .rating-score .score {
        font-size: 3rem;
        font-weight: 700;
        color: var(--primary-color);
        display: block;
    }

    .rating-score .stars {
        margin: 10px 0;
    }

    .rating-score .stars i {
        font-size: 1.5rem;
        margin: 0 2px;
    }

    .total-reviews {
        color: var(--gray-medium);
        margin: 0;
        font-size: 1.1rem;
    }

    .review-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .review-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .review-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
    }

    .customer-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .customer-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid var(--border-color);
    }

    .customer-name {
        margin: 0 0 5px 0;
        font-weight: 600;
        color: var(--dark-color);
    }

    .rating i {
        font-size: 1rem;
        margin-right: 2px;
    }

    .review-date {
        color: var(--gray-medium);
        font-size: 0.9rem;
    }

    .review-content {
        flex: 1;
    }

    .review-text {
        color: var(--text-color);
        line-height: 1.6;
        margin-bottom: 15px;
    }

    .product-info {
        margin-bottom: 15px;
        padding-top: 15px;
        border-top: 1px solid var(--border-color);
    }

    .review-images {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 15px;
    }

    .review-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        cursor: pointer;
        transition: transform 0.3s ease;
        border: 2px solid var(--border-color);
    }

    .review-image:hover {
        transform: scale(1.05);
        border-color: var(--primary-color);
    }

    .footer-rating .stars i {
        font-size: 1.2rem;
        margin-right: 3px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .rating-summary {
            margin-top: 30px;
            padding: 20px;
        }

        .rating-score .score {
            font-size: 2.5rem;
        }

        .review-header {
            flex-direction: column;
            gap: 15px;
        }

        .customer-info {
            align-self: flex-start;
        }

        .review-date {
            align-self: flex-end;
        }
    }
    </style>

    <script>
    // Open image modal
    function openImageModal(imageSrc) {
        document.getElementById('modalImage').src = imageSrc;
        const modal = new bootstrap.Modal(document.getElementById('imageModal'));
        modal.show();
    }

    // Add loading animation for images
    document.addEventListener('DOMContentLoaded', function() {
        const images = document.querySelectorAll('.review-image, .customer-avatar');
        images.forEach(img => {
            img.addEventListener('load', function() {
                this.style.opacity = '1';
            });
            img.style.opacity = '0';
            img.style.transition = 'opacity 0.3s ease';
        });
    });
    </script>
</body>
</html>
