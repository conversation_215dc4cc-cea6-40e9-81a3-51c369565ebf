<?php
require_once __DIR__.'/config.php';

function getDB() {
    static $db;
    if ($db === null) {
        $dsn = 'mysql:host='.DB_HOST.';dbname='.DB_NAME.';charset=utf8mb4';
        try {
            $db = new PDO($dsn, DB_USER, DB_PASS);
            $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            die('Database Connection failed: '.$e->getMessage());
        }
    }
    return $db;
}
?>
