<?php
/**
 * GT-SportDesign - 404 Error Page
 * Professional error handling
 * Version: 2.0 - Production Ready
 */

http_response_code(404);
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ไม่พบหน้าที่ต้องการ - GT-SportDesign</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .error-container {
            text-align: center;
            max-width: 600px;
            padding: 40px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            color: #eb4e17;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .error-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: #6c757d;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(235, 78, 23, 0.4);
            color: white;
        }
        
        .search-box {
            margin: 30px 0;
        }
        
        .search-input {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 12px 20px;
            width: 100%;
            max-width: 400px;
        }
        
        .search-input:focus {
            border-color: #eb4e17;
            box-shadow: 0 0 0 0.2rem rgba(235, 78, 23, 0.25);
        }
        
        .quick-links {
            margin-top: 30px;
        }
        
        .quick-link {
            display: inline-block;
            margin: 5px 10px;
            padding: 8px 16px;
            background: #f8f9fa;
            color: #6c757d;
            text-decoration: none;
            border-radius: 20px;
            transition: all 0.3s ease;
        }
        
        .quick-link:hover {
            background: #eb4e17;
            color: white;
            text-decoration: none;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #eb4e17;
            margin-bottom: 20px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-search"></i>
        </div>
        
        <div class="error-code">404</div>
        
        <h1 class="error-title">ไม่พบหน้าที่ต้องการ</h1>
        
        <p class="error-message">
            ขออภัย หน้าที่คุณกำลังมองหาไม่มีอยู่ หรืออาจถูกย้ายไปแล้ว<br>
            กรุณาตรวจสอบ URL หรือใช้เมนูด้านล่างเพื่อไปยังหน้าอื่น
        </p>
        
        <div class="search-box">
            <form action="products.php" method="GET">
                <input type="text" name="search" class="form-control search-input mx-auto" 
                       placeholder="ค้นหาสินค้าหรือบริการ...">
            </form>
        </div>
        
        <a href="index.php" class="btn-home">
            <i class="fas fa-home me-2"></i>กลับหน้าแรก
        </a>
        
        <div class="quick-links">
            <h6 class="mb-3">หน้าที่น่าสนใจ:</h6>
            <a href="products.php" class="quick-link">
                <i class="fas fa-tshirt me-1"></i>สินค้า
            </a>
            <a href="design.php" class="quick-link">
                <i class="fas fa-palette me-1"></i>ออกแบบ
            </a>
            <a href="contact.php" class="quick-link">
                <i class="fas fa-phone me-1"></i>ติดต่อเรา
            </a>
            <a href="about.php" class="quick-link">
                <i class="fas fa-info-circle me-1"></i>เกี่ยวกับเรา
            </a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                หากปัญหายังคงอยู่ กรุณา
                <a href="contact.php" class="text-primary">ติดต่อเรา</a>
                เพื่อขอความช่วยเหลือ
            </small>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-focus search input
        document.querySelector('.search-input').focus();
        
        // Add animation
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.error-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
