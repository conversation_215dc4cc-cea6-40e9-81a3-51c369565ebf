:root {
    --primary-color: #ee501b;
    --secondary-color: #343a40;
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
}

body {
    font-family: 'Kanit', sans-serif;
    background-color: #f8f9fa;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    background: var(--secondary-color);
    min-height: 100vh;
    width: var(--sidebar-width);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    transition: all 0.3s;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-brand {
    padding: 15px 20px;
    color: white;
    font-size: 20px;
    font-weight: bold;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 15px;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    color: #adb5bd;
    text-decoration: none;
    padding: 10px 20px;
    display: block;
    transition: all 0.3s;
}

.sidebar-menu a:hover, 
.sidebar-menu a.active {
    color: #fff;
    background: rgba(255,255,255,0.1);
    border-left: 4px solid var(--primary-color);
}

.sidebar-menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Navbar */
.top-navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width);
    z-index: 99;
    height: 60px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s;
}

.sidebar.collapsed ~ .top-navbar {
    left: var(--sidebar-collapsed-width);
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    padding-top: 60px;
    width: calc(100% - var(--sidebar-width));
    transition: all 0.3s;
}

.main-content.expanded {
    margin-left: var(--sidebar-collapsed-width);
    width: calc(100% - var(--sidebar-collapsed-width));
}

/* Cards */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e3e6f0;
}

.border-left-primary {
    border-left: 4px solid #4e73df !important;
}

.border-left-success {
    border-left: 4px solid #1cc88a !important;
}

.border-left-info {
    border-left: 4px solid #36b9cc !important;
}

.border-left-warning {
    border-left: 4px solid #f6c23e !important;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -250px;
    }
    
    .sidebar.show {
        margin-left: 0;
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .top-navbar {
        left: 0;
    }
}

/* Tables */
.table-responsive {
    overflow-x: auto;
}

.dataTables_wrapper .dataTables_length, 
.dataTables_wrapper .dataTables_filter {
    margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_info, 
.dataTables_wrapper .dataTables_paginate {
    margin-top: 15px;
}

/* Forms */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(238, 80, 27, 0.25);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, 
.btn-primary:focus {
    background-color: #d04616;
    border-color: #d04616;
}

/* Status badges */
.badge-pending {
    background-color: #ffc107;
    color: #212529;
}

.badge-processing {
    background-color: #17a2b8;
    color: #fff;
}

.badge-shipped {
    background-color: #007bff;
    color: #fff;
}

.badge-completed {
    background-color: #28a745;
    color: #fff;
}

.badge-cancelled {
    background-color: #dc3545;
    color: #fff;
}

/* Image previews */
.img-preview {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

/* Custom file input */
.custom-file-input:lang(th) ~ .custom-file-label::after {
    content: "<|im_start|>ไฟล์";
}

/* Dashboard stats */
.stat-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

/* Activity timeline */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
}

.timeline-item:before {
    content: '';
    position: absolute;
    left: -34px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 2px solid #fff;
}

/* Tooltips */
.tooltip-inner {
    max-width: 200px;
    padding: 5px 10px;
    color: #fff;
    text-align: center;
    background-color: var(--secondary-color);
    border-radius: 4px;
}

