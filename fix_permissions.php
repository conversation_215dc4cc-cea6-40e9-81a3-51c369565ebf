<?php
// แสดงข้อ<lemmaพลาด<lemma้งหมดเพื่อการแก้ไข
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// รายการโฟลเดอร์<lemmaต้องตั้งค่า<lemma์
$directories = [
    'uploads',
    'uploads/products',
    'uploads/gallery',
    'uploads/designs',
    'uploads/profiles',
    'uploads/temp',
    'config'
];

echo "<h2><lemmaตรวจสอบและแก้ไข<lemma์ไฟล์...</h2>";

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "<p>สร้างโฟลเดอร์ <strong>$dir</strong> สำเร็จ</p>";
        } else {
            echo "<p style='color:red'>ไม่สามารถสร้างโฟลเดอร์ <strong>$dir</strong></p>";
        }
    } else {
        if (chmod($dir, 0755)) {
            echo "<p>ตั้งค่า<lemma์โฟลเดอร์ <strong>$dir</strong> เป็น 755 สำเร็จ</p>";
        } else {
            echo "<p style='color:red'>ไม่สามารถตั้งค่า<lemma์โฟลเดอร์ <strong>$dir</strong></p>";
        }
    }
    
    // สร้างไฟล์ index.php เ<|im_start|>่อป้อง<lemmaการเข้า<lemmaไดเรกทอ<lemma
    $index_file = "$dir/index.php";
    if (!file_exists($index_file)) {
        $content = "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n";
        if (file_put_contents($index_file, $content)) {
            echo "<p>สร้างไฟล์ป้อง<lemma <strong>$index_file</strong> สำเร็จ</p>";
        } else {
            echo "<p style='color:red'>ไม่สามารถสร้างไฟล์ป้อง<lemma <strong>$index_file</strong></p>";
        }
    }
}

// ตรวจสอบไฟล์ .htaccess
$htaccess_file = '.htaccess';
if (!file_exists($htaccess_file)) {
    $htaccess_content = "# GT-SportDesign Security Configuration
# Version: 1.0

# ป้อง<lemmaการเข้าไฟล์
<FilesMatch \"^(database\.php|config\.php|\.env|\.git|\.htaccess)\">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้อง<lemmaการแสดงรายการไฟล์
Options -Indexes

# ป้อง<lemmaการเข้าไฟล์ SQL
<FilesMatch \"\.(sql|bak|config|dist|fla|inc|ini|log|psd|sh|swp)$\">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้อง<lemmaการเข้าโฟลเดอร์ config
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteRule ^config/.*$ - [F,L]
  RewriteRule ^database/.*$ - [F,L]
</IfModule>
";
    
    if (file_put_contents($htaccess_file, $htaccess_content)) {
        echo "<p>สร้างไฟล์ <strong>.htaccess</strong> สำเร็จ</p>";
    } else {
        echo "<p style='color:red'>ไม่สามารถสร้างไฟล์ <strong>.htaccess</strong></p>";
    }
}

echo "<h3>เสร็จ<lemma้นการตรวจสอบและแก้ไข<lemma์ไฟล์</h3>";
?>

