<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$message = '';
$error = '';

// ดำเนินการเพิ่ม/แก้ไข/ลบลูกค้า
if ($_POST && isset($_POST['action'])) {
    $action = $_POST['action'];

    try {
        if ($action === 'add' || $action === 'edit') {
            $name = trim($_POST['name']);
            $email = trim($_POST['email']);
            $phone = trim($_POST['phone']);
            $address = trim($_POST['address']);
            $status = $_POST['status'] ?? 'active';

            if (empty($name) || empty($email)) {
                throw new Exception('กรุณากรอกชื่อและอีเมลลูกค้า');
            }

            if ($action === 'add') {
                // ตรวจสอบว่ามีอีเมลซ้ำ
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM customers WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception('อีเมลนี้มีอยู่ในระบบแล้ว');
                }

                // เพิ่มลูกค้าใหม่
                $stmt = $pdo->prepare("
                    INSERT INTO customers (name, email, phone, address, status, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ");
                $stmt->execute([$name, $email, $phone, $address, $status]);
                $message = 'เพิ่มลูกค้าเรียบร้อยแล้ว';
            } else {
                // แก้ไขลูกค้า
                $customer_id = (int)$_POST['customer_id'];
                $stmt = $pdo->prepare("
                    UPDATE customers
                    SET name = ?, email = ?, phone = ?, address = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$name, $email, $phone, $address, $status, $customer_id]);
                $message = 'แก้ไขข้อมูลลูกค้าเรียบร้อยแล้ว';
            }
        } elseif ($action === 'delete') {
            $customer_id = (int)$_POST['customer_id'];

            // ตรวจสอบว่ามีคำสั่งซื้อ
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE customer_id = ?");
            $stmt->execute([$customer_id]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception('ไม่สามารถลบลูกค้าได้เนื่องจากมีคำสั่งซื้อในระบบ');
            }

            $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
            $stmt->execute([$customer_id]);
            $message = 'ลบลูกค้าเรียบร้อยแล้ว';
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
// ตัวแปรค้นหาและแบ่งหน้า
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// สร้าง WHERE clause สำหรับการค้นหา
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// ดึงข้อมูลลูกค้า
$customers = [];
$total_customers = 0;
$total_pages = 1;
$stats = [
    'total_customers' => 0,
    'active_customers' => 0,
    'new_today' => 0,
    'ordered_today' => 0
];

try {
    // จำนวนลูกค้าทั้งหมด
    $count_sql = "SELECT COUNT(*) FROM customers $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_customers = $stmt->fetchColumn();
    $total_pages = ceil($total_customers / $limit);

    // ข้อมูลลูกค้า
    $sql = "
        SELECT c.*,
               (SELECT COUNT(*) FROM orders WHERE customer_id = c.id) as order_count,
               (SELECT SUM(total_amount) FROM orders WHERE customer_id = c.id) as total_spent,
               (SELECT MAX(created_at) FROM orders WHERE customer_id = c.id) as last_order_date
        FROM customers c
        $where_clause
        ORDER BY c.created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $customers = $stmt->fetchAll();

    // สถิติลูกค้า
    $stats_sql = "
        SELECT
            COUNT(*) as total_customers,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_customers,
            COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as new_today,
            (SELECT COUNT(DISTINCT customer_id) FROM orders WHERE DATE(created_at) = CURDATE()) as ordered_today
        FROM customers
    ";
    $stats = $pdo->query($stats_sql)->fetch();

} catch (Exception $e) {
    $error = 'เกิดข้อผิดพลาดในการดึงข้อมูล: ' . $e->getMessage();

    // ใช้ข้อมูลจำลอง
    $customers = [
        [
            'id' => 1,
            'name' => 'นาย ก ใจดี',
            'email' => '<EMAIL>',
            'phone' => '************',
            'address' => '123 ถนนสุขุมวิท กรุงเทพฯ',
            'status' => 'active',
            'order_count' => 3,
            'total_spent' => 1500.00,
            'created_at' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 2,
            'name' => 'นางสาว ข สวยงาม',
            'email' => '<EMAIL>',
            'phone' => '************',
            'address' => '456 ถนนรัชดาภิเษก กรุงเทพฯ',
            'status' => 'active',
            'order_count' => 1,
            'total_spent' => 680.00,
            'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ]
    ];

    $total_customers = count($customers);
    $total_pages = 1;

    $stats = [
        'total_customers' => 2,
        'active_customers' => 2,
        'new_today' => 1,
        'ordered_today' => 1
    ];
}
?><!DOCTYPE html>
<html lang="th">
    <head>
    <meta charset="UTF-8">    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ลูกค้า - GT Sport Design Admin</title>    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>        body {
            font-family: 'Kanit', sans-serif;            background-color: #f8f9fa;
        }        
        .sidebar {            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;            color: white;
            width: 280px;            position: fixed;
            left: 0;            top: 0;
            z-index: 1000;        }
                .sidebar-brand {
            padding: 20px;            border-bottom: 1px solid rgba(255,255,255,0.1);
        }        
        .sidebar-menu {            list-style: none;
            padding: 0;            margin: 0;
        }        
        .sidebar-menu li a {            display: block;
            padding: 15px 20px;            color: rgba(255,255,255,0.8);
            text-decoration: none;            transition: all 0.3s;
            border-left: 3px solid transparent;        }
                .sidebar-menu li a:hover, .sidebar-menu li a.active {
            background: rgba(255,255,255,0.1);            color: white;
            border-left-color: #11be97;        }
                .sidebar-menu li a i {
            margin-right: 10px;            width: 20px;
            text-align: center;        }
                .main-content {
            margin-left: 280px;            padding: 20px;
        }        
        .card {            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);            margin-bottom: 20px;
        }        
        .card-header {            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);            padding: 15px 20px;
        }        
        .customer-avatar {            width: 40px;
            height: 40px;            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);            color: white;
            display: flex;            align-items: center;
            justify-content: center;            font-weight: 600;
        }        
        .status-badge {            padding: 5px 10px;
            border-radius: 20px;            font-size: 12px;
            font-weight: 500;        }
                .status-active {
            background-color: #e1f8f5;            color: #11be97;
        }        
        .status-inactive {            background-color: #f1f1f1;
            color: #6c757d;        }
                .action-btn {
            padding: 5px 10px;            font-size: 12px;
            border-radius: 4px;            margin-right: 5px;
        }        
        .btn-primary {            background-color: #11be97;
            border-color: #11be97;        }
                .btn-primary:hover {
            background-color: #0ea080;            border-color: #0ea080;
        }        
        .pagination {            margin-bottom: 0;
        }        
        .form-control:focus {            border-color: #11be97;
            box-shadow: 0 0 0 0.25rem rgba(17, 190, 151, 0.25);        }
    </style></head>
<body>    <!-- Sidebar -->
    <div class="sidebar">        <div class="sidebar-brand">
            <h4>GT Sport Design</h4>            <small>Admin Panel</small>
        </div>        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้า</a></li>            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>สินค้า</a></li>            <li><a href="customers.php" class="active"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>        </ul>
    </div>
    <!-- Main Content -->    <div class="main-content">
        <div class="d-flex justify-content-between align-items-center mb-4">            <h2><i class="fas fa-users me-2"></i>ลูกค้า</h2>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#customerModal">                <i class="fas fa-plus me-2"></i>เพิ่มลูกค้าใหม่
            </button>        </div>
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show">                <i class="fas fa-check-circle me-2"></i> <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>            </div>
        <?php endif; ?>
        <?php if ($error): ?>            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i> <?= htmlspecialchars($error) ?>                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>        <?php endif; ?>
        <!-- สถิติลูกค้า -->
        <div class="row mb-4">            <div class="col-md-3">
                <div class="card">                    <div class="card-body text-center">
                        <div class="display-4 text-primary mb-2"><?= number_format($stats['total_customers']) ?></div>                        <div class="text-muted">ลูกค้าทั้งหมด</div>
                    </div>                </div>
            </div>            <div class="col-md-3">
                <div class="card">                    <div class="card-body text-center">
                        <div class="display-4 text-success mb-2"><?= number_format($stats['active_customers']) ?></div>                        <div class="text-muted">ลูกค้าใช้งาน</div>
                    </div>                </div>
            </div>            <div class="col-md-3">
                <div class="card">                    <div class="card-body text-center">
                        <div class="display-4 text-info mb-2"><?= number_format($stats['new_today']) ?></div>                        <div class="text-muted">ลูกค้าใหม่วันนี้</div>
                    </div>                </div>
            </div>            <div class="col-md-3">
                <div class="card">                    <div class="card-body text-center">
                        <div class="display-4 text-warning mb-2"><?= number_format($stats['ordered_today']) ?></div>                        <div class="text-muted">คำสั่งซื้อวันนี้</div>
                    </div>                </div>
            </div>        </div>
        <!-- ค้นหาลูกค้า -->
        <div class="card mb-4">            <div class="card-body">
                <form method="GET" action="customers.php" class="row g-3">                    <div class="col-md-6">
                        <div class="input-group">                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" name="search" placeholder="ค้นหาชื่อ, อีเมล, เบอร์โทร..." value="<?= htmlspecialchars($search) ?>">                        </div>
                    </div>                    <div class="col-md-3">
                        <select name="status" class="form-select">                            <option value="">- สถานะทั้งหมด -</option>
                            <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>ใช้งาน</option>                            <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>ไม่ใช้งาน</option>
                        </select>                    </div>
                    <div class="col-md-3">                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>ค้นหา                        </button>
                    </div>                </form>
            </div>        </div>
        <!-- ตารางลูกค้า -->
        <div class="card">            <div class="card-body p-0">
                <div class="table-responsive">                    <table class="table table-hover mb-0">
                        <thead>                            <tr>
                                <th width="60">ID</th>                                <th>ลูกค้า</th>
                                <th>ติดต่อ</th>                                <th>คำสั่งซื้อ</th>
                                <th>ยอดใช้จ่าย</th>                                <th>สถานะ</th>
                                <th>สร้างเมื่อ</th>                                <th width="150"></th>
                            </tr>                        </thead>
                        <tbody>                            <?php if (empty($customers)): ?>
                                <tr>                                    <td colspan="8" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-2x mb-2"></i><br>                                        ไม่พบข้อมูลลูกค้า
                                    </td>                                </tr>
                            <?php else: ?>                                <?php foreach ($customers as $customer): ?>
                                    <tr>                                        <td><?= $customer['id'] ?></td>
                                        <td>                                            <div class="d-flex align-items-center">
                                                <div class="customer-avatar me-3">                                                    <?= strtoupper(substr($customer['name'], 0, 1)) ?>
                                                </div>                                                <div>
                                                    <strong><?= htmlspecialchars($customer['name']) ?></strong>                                                </div>
                                            </div>                                        </td>
                                        <td>                                            <div><?= htmlspecialchars($customer['email']) ?></div>
                                            <?php if ($customer['phone']): ?>                                                <div class="text-muted"><?= htmlspecialchars($customer['phone']) ?></div>
                                            <?php endif; ?>                                        </td>
                                        <td>                                            <span class="badge bg-info"><?= number_format($customer['order_count']) ?> คำสั่งซื้อ</span>
                                        </td>                                        <td>
                                            <strong>฿<?= number_format($customer['total_spent'] ?? 0) ?></strong>                                        </td>
                                        <td>                                            <span class="status-badge <?= $customer['status'] === 'active' ? 'status-active' : 'status-inactive' ?>">
                                                <?= $customer['status'] === 'active' ? 'ใช้งาน' : 'ไม่ใช้งาน' ?>                                            </span>
                                        </td>                                        <td>
                                            <?= date('d/m/Y', strtotime($customer['created_at'])) ?>                                        </td>
                                        <td>                                            <div class="btn-group btn-group-sm">
                                                <a href="customers_detail.php?id=<?= $customer['id'] ?>" class="btn btn-outline-primary">                                                    <i class="fas fa-eye"></i>
                                                </a>                                                <button class="btn btn-outline-warning btn-edit" 
                                                        data-customer='<?= json_encode($customer) ?>'                                                        data-bs-toggle="modal" data-bs-target="#customerModal">
                                                    <i class="fas fa-edit"></i>                                                </button>
                                                <button class="btn btn-outline-danger btn-delete"                                                        data-id="<?= $customer['id'] ?>"
                                                        data-name="<?= htmlspecialchars($customer['name']) ?>">                                                    <i class="fas fa-trash"></i>
                                                </button>                                            </div>
                                        </td>                                    </tr>
                                <?php endforeach; ?>                            <?php endif; ?>
                        </tbody>                    </table>
                </div>            </div>
                        <!-- Pagination -->
            <?php if ($total_pages > 1): ?>                <div class="card-footer">
                    <nav>                        <ul class="pagination justify-content-center mb-0">
                            <li class="page-item <?= $page <= 1 ? 'disabled' : '' ?>">                                <a class="page-link" href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>">
                                    <i class="fas fa-chevron-left"></i>                                </a>
                            </li>                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>">                                        <?= $i ?>
                                    </a>                                </li>
                            <?php endfor; ?>                            
                            <li class="page-item <?= $page >= $total_pages ? 'disabled' : '' ?>">                                <a class="page-link" href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>">
                                    <i class="fas fa-chevron-right"></i>                                </a>
                            </li>                        </ul>
                    </nav>                </div>
            <?php endif; ?>        </div>
    </div>
    <!-- Modal เพิ่ม/แก้ไขลูกค้า -->    <div class="modal fade" id="customerModal" tabindex="-1">
        <div class="modal-dialog">            <div class="modal-content">
                <div class="modal-header">                    <h5 class="modal-title" id="modalTitle">เพิ่มลูกค้าใหม่</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>                </div>
                <div class="modal-body">                    <form id="customerForm" method="POST" action="customers.php">
                        <input type="hidden" name="action" id="formAction" value="add">                        <input type="hidden" name="customer_id" id="customerId" value="">
                        <div class="mb-3">
                            <label for="name" class="form-label">ชื่อลูกค้า <span class="text-danger">*</span></label>                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">                            <label for="email" class="form-label">อีเมล <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">เบอร์โทรศัพท์</label>                            <input type="text" class="form-control" id="phone" name="phone">
                        </div>
                        <div class="mb-3">                            <label for="address" class="form-label">ที่อยู่</label>
                            <textarea class="form-control" id="address" name="address" rows="3"></textarea>                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">สถานะ</label>                            <select class="form-select" id="status" name="status">
                                <option value="active">ใช้งาน</option>                                <option value="inactive">ไม่ใช้งาน</option>
                            </select>                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>                            <button type="submit" class="btn btn-primary">บันทึก</button>
                        </div>                    </form>
                </div>            </div>
        </div>    </div>
    <!-- Modal ยืนยันการลบ -->
    <div class="modal fade" id="deleteModal" tabindex="-1">        <div class="modal-dialog">
            <div class="modal-content">                <div class="modal-header">
                    <h5 class="modal-title">ยืนยันการลบ</h5>                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>                <div class="modal-body">
                    <p>ต้องการลบลูกค้า <strong id="deleteCustomerName"></strong> ใช่หรือไม่?</p>                    <p class="text-danger">หมายเหตุ: ลูกค้าที่มีคำสั่งซื้อในระบบจะไม่สามารถลบได้</p>
                    <form id="deleteForm" method="POST" action="customers.php">                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="customer_id" id="deleteCustomerId">                    </form>
                </div>                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>                    <button type="button" class="btn btn-danger" onclick="document.getElementById('deleteForm').submit()">ลบ</button>
                </div>            </div>
        </div>    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>        // แสดงข้อมูลลูกค้าในฟอร์มแก้ไข
        document.querySelectorAll('.btn-edit').forEach(btn => {            btn.addEventListener('click', function() {
                const customer = JSON.parse(this.dataset.customer);                document.getElementById('modalTitle').textContent = 'แก้ไขข้อมูลลูกค้า';
                document.getElementById('formAction').value = 'edit';                document.getElementById('customerId').value = customer.id;
                document.getElementById('name').value = customer.name;                document.getElementById('email').value = customer.email;
                document.getElementById('phone').value = customer.phone || '';                document.getElementById('address').value = customer.address || '';
                document.getElementById('status').value = customer.status;            });
        });
        // แสดงข้อมูลลูกค้าในฟอร์มลบ        document.querySelectorAll('.btn-delete').forEach(btn => {
            btn.addEventListener('click', function() {                document.getElementById('deleteCustomerId').value = this.dataset.id;
                document.getElementById('deleteCustomerName').textContent = this.dataset.name;                new bootstrap.Modal(document.getElementById('deleteModal')).show();
            });        });
        // รีเซ็ตฟอร์มเมื่อปิด Modal
        document.getElementById('customerModal').addEventListener('hidden.bs.modal', function() {            document.getElementById('modalTitle').textContent = 'เพิ่มลูกค้าใหม่';
            document.getElementById('formAction').value = 'add';            document.getElementById('customerForm').reset();
            document.getElementById('customerId').value = '';        });
    </script>
</body>
</html>









































































































































































































































































































