<?php
/**
 * GT-SportDesign System Fix Tool
 * เครื่องมือแก้ไขปัญหาระบบ
 */

// แสดงข้อผิดพลาดทั้งหมด
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$action = $_GET['action'] ?? '';
$message = '';
$error = '';

// ฟังก์ชันตรวจสอบระบบ
function checkSystemStatus() {
    $status = [
        'php_version' => [
            'name' => 'PHP Version',
            'status' => version_compare(PHP_VERSION, '7.4.0', '>='),
            'value' => PHP_VERSION,
            'required' => '7.4.0+'
        ],
        'mysql_extension' => [
            'name' => 'MySQL Extension',
            'status' => extension_loaded('pdo_mysql'),
            'value' => extension_loaded('pdo_mysql') ? 'ติดตั้งแล้ว' : 'ไม่ได้ติดตั้ง',
            'required' => 'จำเป็น'
        ],
        'gd_extension' => [
            'name' => 'GD Extension',
            'status' => extension_loaded('gd'),
            'value' => extension_loaded('gd') ? 'ติดตั้งแล้ว' : 'ไม่ได้ติดตั้ง',
            'required' => 'จำเป็น'
        ],
        'mbstring_extension' => [
            'name' => 'mbstring Extension',
            'status' => extension_loaded('mbstring'),
            'value' => extension_loaded('mbstring') ? 'ติดตั้งแล้ว' : 'ไม่ได้ติดตั้ง',
            'required' => 'จำเป็น'
        ],
        'uploads_dir' => [
            'name' => 'Uploads Directory',
            'status' => is_dir('uploads') && is_writable('uploads'),
            'value' => is_dir('uploads') ? (is_writable('uploads') ? 'พร้อมใช้งาน' : 'ไม่สามารถเขียนได้') : 'ไม่พบโฟลเดอร์',
            'required' => 'จำเป็น'
        ],
        'config_file' => [
            'name' => 'Database Config',
            'status' => file_exists('config/database.php'),
            'value' => file_exists('config/database.php') ? 'พบไฟล์' : 'ไม่พบไฟล์',
            'required' => 'จำเป็น'
        ]
    ];
    
    // ตรวจสอบการเชื่อมต่อฐานข้อมูล
    try {
        if (file_exists('config/database.php')) {
            require_once 'config/database.php';
            if (isset($pdo)) {
                $stmt = $pdo->query("SELECT 1");
                $status['database_connection'] = [
                    'name' => 'Database Connection',
                    'status' => true,
                    'value' => 'เชื่อมต่อสำเร็จ',
                    'required' => 'จำเป็น'
                ];
            } else {
                $status['database_connection'] = [
                    'name' => 'Database Connection',
                    'status' => false,
                    'value' => 'ไม่สามารถเชื่อมต่อได้',
                    'required' => 'จำเป็น'
                ];
            }
        }
    } catch (Exception $e) {
        $status['database_connection'] = [
            'name' => 'Database Connection',
            'status' => false,
            'value' => 'ข้อผิดพลาด: ' . $e->getMessage(),
            'required' => 'จำเป็น'
        ];
    }
    
    return $status;
}

// ฟังก์ชันสร้างโฟลเดอร์
function createDirectories() {
    $directories = [
        'uploads',
        'uploads/products',
        'uploads/gallery',
        'uploads/designs',
        'uploads/profiles',
        'uploads/temp'
    ];
    
    $created = [];
    $errors = [];
    
    foreach ($directories as $dir) {
        if (!file_exists($dir)) {
            if (mkdir($dir, 0755, true)) {
                $created[] = $dir;
                
                // สร้างไฟล์ index.php เพื่อป้องกัน
                $index_file = $dir . '/index.php';
                file_put_contents($index_file, "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n");
                
                // สร้าง .htaccess
                $htaccess_file = $dir . '/.htaccess';
                file_put_contents($htaccess_file, "Options -Indexes\nDeny from all\n");
            } else {
                $errors[] = $dir;
            }
        }
    }
    
    return ['created' => $created, 'errors' => $errors];
}

// ฟังก์ชันแก้ไขไฟล์ php.ini
function getPhpIniRecommendations() {
    $recommendations = [];
    
    if (!extension_loaded('gd')) {
        $recommendations[] = [
            'setting' => 'extension=gd',
            'description' => 'เปิดใช้งาน GD Extension สำหรับการประมวลผลรูปภาพ',
            'file' => 'php.ini'
        ];
    }
    
    if (!extension_loaded('mbstring')) {
        $recommendations[] = [
            'setting' => 'extension=mbstring',
            'description' => 'เปิดใช้งาน mbstring Extension สำหรับการจัดการข้อความ',
            'file' => 'php.ini'
        ];
    }
    
    $upload_max = ini_get('upload_max_filesize');
    if (intval($upload_max) < 10) {
        $recommendations[] = [
            'setting' => 'upload_max_filesize = 10M',
            'description' => 'เพิ่มขนาดไฟล์สูงสุดที่อัปโหลดได้',
            'file' => 'php.ini'
        ];
    }
    
    $post_max = ini_get('post_max_size');
    if (intval($post_max) < 10) {
        $recommendations[] = [
            'setting' => 'post_max_size = 10M',
            'description' => 'เพิ่มขนาด POST สูงสุด',
            'file' => 'php.ini'
        ];
    }
    
    return $recommendations;
}

// ประมวลผลการดำเนินการ
if ($action === 'create_dirs') {
    $result = createDirectories();
    if (!empty($result['created'])) {
        $message = 'สร้างโฟลเดอร์สำเร็จ: ' . implode(', ', $result['created']);
    }
    if (!empty($result['errors'])) {
        $error = 'ไม่สามารถสร้างโฟลเดอร์: ' . implode(', ', $result['errors']);
    }
    if (empty($result['created']) && empty($result['errors'])) {
        $message = 'โฟลเดอร์ทั้งหมดมีอยู่แล้ว';
    }
}

$system_status = checkSystemStatus();
$php_recommendations = getPhpIniRecommendations();
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GT-SportDesign System Fix Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .fix-container { max-width: 1200px; margin: 20px auto; }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-item { padding: 15px; margin: 5px 0; border-radius: 8px; background: white; border-left: 4px solid #dee2e6; }
        .status-item.ok { border-left-color: #28a745; }
        .status-item.error { border-left-color: #dc3545; }
        .header-section { background: linear-gradient(135deg, #eb4e17, #ff6b35); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .recommendation-item { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container fix-container">
        <div class="header-section text-center">
            <h1><i class="fas fa-tools"></i> GT-SportDesign System Fix Tool</h1>
            <p class="mb-0">เครื่องมือตรวจสอบและแก้ไขปัญหาระบบ</p>
        </div>
        
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle"></i> <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <div class="row">
            <!-- System Status -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-clipboard-check"></i> สถานะระบบ</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($system_status as $check): ?>
                        <div class="status-item <?php echo $check['status'] ? 'ok' : 'error'; ?>">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo $check['name']; ?></strong>
                                    <br><small class="text-muted">ต้องการ: <?php echo $check['required']; ?></small>
                                </div>
                                <div class="text-end">
                                    <span class="<?php echo $check['status'] ? 'status-ok' : 'status-error'; ?>">
                                        <i class="fas fa-<?php echo $check['status'] ? 'check-circle' : 'times-circle'; ?>"></i>
                                        <?php echo $check['value']; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <!-- PHP.ini Recommendations -->
                <?php if (!empty($php_recommendations)): ?>
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-cog"></i> คำแนะนำการตั้งค่า PHP</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">แก้ไขไฟล์ <code>php.ini</code> และรีสตาร์ท Apache:</p>
                        <?php foreach ($php_recommendations as $rec): ?>
                        <div class="recommendation-item">
                            <code><?php echo htmlspecialchars($rec['setting']); ?></code>
                            <br><small><?php echo $rec['description']; ?></small>
                        </div>
                        <?php endforeach; ?>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-info-circle"></i> วิธีแก้ไข:</h6>
                            <ol>
                                <li>เปิดไฟล์ <code>C:\xampp\php\php.ini</code></li>
                                <li>ค้นหาและแก้ไขการตั้งค่าตามที่แนะนำ</li>
                                <li>เอา semicolon (;) ออกหน้า extension ที่ต้องการ</li>
                                <li>บันทึกไฟล์และรีสตาร์ท Apache ใน XAMPP Control Panel</li>
                            </ol>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
            
            <!-- Quick Actions -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-magic"></i> เครื่องมือแก้ไข</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="?action=create_dirs" class="btn btn-primary">
                                <i class="fas fa-folder-plus"></i> สร้างโฟลเดอร์
                            </a>
                            <a href="test_connection.php" class="btn btn-info">
                                <i class="fas fa-database"></i> ทดสอบฐานข้อมูล
                            </a>
                            <a href="install.php" class="btn btn-success">
                                <i class="fas fa-play"></i> ติดตั้งระบบ
                            </a>
                            <a href="deploy.php" class="btn btn-warning">
                                <i class="fas fa-rocket"></i> แดชบอร์ดติดตั้ง
                            </a>
                        </div>
                        
                        <hr>
                        
                        <h6>ลิงก์ด่วน</h6>
                        <div class="d-grid gap-1">
                            <a href="index.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-home"></i> หน้าแรก
                            </a>
                            <a href="admin/login.php" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-user-shield"></i> แอดมิน
                            </a>
                            <a href="login.php" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-sign-in-alt"></i> เข้าสู่ระบบ
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- MySQL Instructions -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-database"></i> แก้ไข MySQL</h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <strong>ถ้า MySQL ไม่ทำงาน:</strong><br>
                            1. เปิด XAMPP Control Panel<br>
                            2. คลิก "Start" ที่ MySQL<br>
                            3. ตรวจสอบ Port 3306<br>
                            4. ถ้ายังไม่ได้ ให้รีสตาร์ท XAMPP
                        </small>
                    </div>
                </div>
                
                <!-- System Info -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-info-circle"></i> ข้อมูลระบบ</h6>
                    </div>
                    <div class="card-body">
                        <small>
                            <strong>PHP:</strong> <?php echo PHP_VERSION; ?><br>
                            <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?><br>
                            <strong>OS:</strong> <?php echo PHP_OS; ?><br>
                            <strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?><br>
                            <strong>Upload Max:</strong> <?php echo ini_get('upload_max_filesize'); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Instructions -->
        <div class="alert alert-warning mt-4">
            <h6><i class="fas fa-exclamation-triangle"></i> คำแนะนำสำคัญ</h6>
            <ul class="mb-0">
                <li>ตรวจสอบให้แน่ใจว่า XAMPP ทำงานอยู่</li>
                <li>MySQL และ Apache ต้องเริ่มทำงานใน XAMPP Control Panel</li>
                <li>แก้ไขไฟล์ php.ini ตามคำแนะนำด้านบน</li>
                <li>รีสตาร์ท Apache หลังแก้ไข php.ini</li>
                <li>ลบไฟล์นี้หลังแก้ไขปัญหาเสร็จ</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
