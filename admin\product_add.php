<?php
$page_title = "สร้างระบบค้าใหม่";
$page_icon = "plus";
require_once 'includes/header.php';
require_once 'includes/navbar.php';
require_once 'includes/sidebar.php';

// ข้อมูลหมวด: ใช้งาน
$categories = [];
try {
    $db = getDB();
    $stmt = $db->query("SELECT * FROM categories ORDER BY name");
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    $_SESSION['error'] = "ไม่สามารถข้อมูลหมวด: ใช้งานได้";
}

// ตรวจสอบการส่งฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        // ข้อมูลระบบค้า
        $name = $_POST['name'] ?? '';
        $sku = $_POST['sku'] ?? '';
        $category_id = $_POST['category_id'] ?? null;
        $description = $_POST['description'] ?? '';
        $price = $_POST['price'] ?? 0;
        $sale_price = $_POST['sale_price'] ?? null;
        $stock_quantity = $_POST['stock_quantity'] ?? 0;
        $weight = $_POST['weight'] ?? null;
        $dimensions = $_POST['dimensions'] ?? null;
        $features = $_POST['features'] ?? null;
        $status = $_POST['status'] ?? 'active';
        
        // ตรวจสอบข้อมูล
        if (empty($name)) {
            throw new Exception("ณาระรสื่อระบบค้า");
        }
        
        // โหลดระบบค้า
        $image = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
            $allowed = ['jpg', 'jpeg', 'png', 'gif'];
            $filename = $_FILES['image']['name'];
            $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            
            if (!in_array($ext, $allowed)) {
                throw new Exception("ระบบค้าต้องเป็นไฟล์ JPG, PNG, GIF เท่าจากรูป");
            }
            
            $image = time() . '_' . $filename;
            $upload_path = '../uploads/products/' . $image;
            
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                throw new Exception("ไม่สามารถปโหลดระบบค้าได้");
            }
        }
        
        // ข้อมูลระบบค้า
        $stmt = $db->prepare("INSERT INTO products (name, sku, category_id, description, price, sale_price, stock_quantity, weight, dimensions, features, image, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$name, $sku, $category_id, $description, $price, $sale_price, $stock_quantity, $weight, $dimensions, $features, $image, $status]);
        
        $_SESSION['success'] = "ระบบค้าสร้างสำเร็จ";
        header('Location: products.php');
        exit;
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}
?>



