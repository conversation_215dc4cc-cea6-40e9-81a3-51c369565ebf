<?php
// แสดงข้อพลาดหมดเพื่อการตั้ง
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

define('INSTALLATION_MODE', true);

// ตรวจสอบว่าเป็นการส่งฟอร์ม:ไม่
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';

// ตรวจสอบ PHP version
$php_version_ok = version_compare(PHP_VERSION, '7.4.0', '>=');
// ตรวจสอบ extensions
$extensions = [
    'pdo' => extension_loaded('pdo'),
    'pdo_mysql' => extension_loaded('pdo_mysql'),
    'gd' => extension_loaded('gd'),
    'mbstring' => extension_loaded('mbstring')
];

// ตรวจสอบโฟลเดอร์:ต้องได้
$writable_dirs = [
    'uploads' => is_writable('uploads') || (!file_exists('uploads') && is_writable('.')),
    'uploads/products' => is_writable('uploads/products') || (!file_exists('uploads/products') && (is_writable('uploads') || is_writable('.'))),
    'uploads/temp' => is_writable('uploads/temp') || (!file_exists('uploads/temp') && (is_writable('uploads') || is_writable('.'))),
    'config' => is_writable('config')
];

// ถ้าส่งฟอร์มการตั้งค่าฐานข้อมูล
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $step === 2) {
    $db_host = trim($_POST['db_host'] ?? '');
    $db_name = trim($_POST['db_name'] ?? '');
    $db_user = trim($_POST['db_user'] ?? '');
    $db_pass = $_POST['db_pass'] ?? '';
    
    if (!$db_host || !$db_name || !$db_user) {
        $error = 'กรอกข้อมูลฐานข้อมูลให้ครบถ้วน';
    } else {
        try {
            // ทดสอบการเชื่อมต่อ
            $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // ตรวจสอบว่าฐานข้อมูลอยู่:ไม่
            $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
            $stmt->execute([$db_name]);
            $db_exists = $stmt->fetch();
            
            if (!$db_exists) {
                // สร้างฐานข้อมูล
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            }
            
            // เชื่อมต่อกับฐานข้อมูล:สร้าง
            $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // สร้างไฟล์ config
            $config_content = "<?php
/**
 * GT-SportDesign Database Configuration
 * Auto-generated on " . date('Y-m-d H:i:s') . "
 */

function getDbConnection() {
    \$host = '$db_host';
    \$dbname = '$db_name';
    \$username = '$db_user';
    \$password = '$db_pass';
    
    try {
        \$pdo = new PDO(\"mysql:host=\$host;dbname=\$dbname;charset=utf8mb4\", \$username, \$password);
        \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        \$pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return \$pdo;
    } catch (PDOException \$e) {
        die('Database connection failed: ' . \$e->getMessage());
    }
}

// Global database connection
function getDB() {
    return getDbConnection();
}

// Backward compatibility
\$pdo = getDbConnection();
";
            
            // ไฟล์ config
            if (file_put_contents('config/database.php', $config_content)) {
                // สร้างตารางฐานข้อมูล
                $sql = file_get_contents('database/db.sql');
                $statements = explode(';', $sql);
                
                foreach ($statements as $statement) {
                    $statement = trim($statement);
                    if (!empty($statement)) {
                        $pdo->exec($statement);
                    }
                }
                
                // สร้างโฟลเดอร์ uploads ถ้าไม่
                $upload_dirs = [
                    'uploads',
                    'uploads/products',
                    'uploads/gallery',
                    'uploads/designs',
                    'uploads/profiles',
                    'uploads/temp'
                ];
                
                foreach ($upload_dirs as $dir) {
                    if (!is_dir($dir)) {
                        mkdir($dir, 0755, true);
                    }
                    
                    // สร้าง .htaccess
                    $htaccess_file = $dir . '/.htaccess';
                    if (!file_exists($htaccess_file)) {
                        file_put_contents($htaccess_file, "Options -Indexes\n");
                    }
                }
                
                // สร้าง index.php ในโฟลเดอร์ uploads
                $index_file = 'uploads/index.php';
                if (!file_exists($index_file)) {
                    file_put_contents($index_file, "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n");
                }
                
                $success = 'ตั้งระบบสำเร็จ!';
                $step = 3;
            } else {
                $error = 'ไม่สามารถสร้างไฟล์ config/database.php ได้';
            }
        } catch (PDOException $e) {
            $error = 'ไม่สามารถเชื่อมต่อฐานข้อมูลได้: ' . $e->getMessage();
        }
    }
}

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตั้ง GT-SportDesign</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .install-container { max-width: 800px; margin: 50px auto; }
        .step { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .step-header { border-bottom: 1px solid #eee; margin-bottom: 20px; padding-bottom: 10px; }
        .check-item { margin-bottom: 10px; }
        .check-status { font-weight: bold; }
        .status-ok { color: green; }
        .status-error { color: red; }
    </style>
</head>
<body>
    <div class="container install-container">
        <div class="text-center mb-4">
            <h1>ตั้ง GT-SportDesign</h1>
            <p class="text-muted">ระบบร้านค้าออนไลน์</p>
        </div>
        
        <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
        <div class="alert alert-success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <?php if ($step === 1): ?>
        <!-- ขั้นตอน 1: ตรวจสอบระบบ -->
        <div class="step">
            <div class="step-header">
                <h3>ขั้นตอน 1: ตรวจสอบระบบ</h3>
            </div>
            
            <h5>เวอร์ชั่น PHP</h5>
            <div class="check-item">
                <span>PHP <?php echo PHP_VERSION; ?></span>
                <span class="check-status float-end <?php echo $php_version_ok ? 'status-ok' : 'status-error'; ?>">
                    <?php echo $php_version_ok ? 'ผ่าน' : 'ไม่ผ่าน (ต้องการ PHP 7.4 มากกว่า)'; ?>
                </span>
            </div>
            
            <h5>PHP Extensions</h5>
            <?php foreach ($extensions as $ext => $loaded): ?>
            <div class="check-item">
                <span><?php echo $ext; ?></span>
                <span class="check-status float-end <?php echo $loaded ? 'status-ok' : 'status-error'; ?>">
                    <?php echo $loaded ? 'ผ่าน' : 'ไม่ผ่าน'; ?>
                </span>
            </div>
            <?php endforeach; ?>
            
            <h5>การเขียนไฟล์</h5>
            <?php foreach ($writable_dirs as $dir => $writable): ?>
            <div class="check-item">
                <span><?php echo $dir; ?></span>
                <span class="check-status float-end <?php echo $writable ? 'status-ok' : 'status-error'; ?>">
                    <?php echo $writable ? 'ได้' : 'ไม่ได้'; ?>
                </span>
            </div>
            <?php endforeach; ?>
            
            <div class="mt-4 text-end">
                <?php
                $can_proceed = $php_version_ok && !in_array(false, $extensions) && !in_array(false, $writable_dirs);
                if ($can_proceed):
                ?>
                <a href="?step=2" class="btn btn-primary">ต่อ</a>
                <?php else: ?>
                <div class="alert alert-warning">แก้ไขข้างต้นก่อนต่อ</div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if ($step === 2): ?>
        <!-- ขั้นตอน 2: ตั้งค่าฐานข้อมูล -->
        <div class="step">
            <div class="step-header">
                <h3>ขั้นตอน 2: ตั้งค่าฐานข้อมูล</h3>
            </div>
            
            <form method="post">
                <div class="mb-3">
                    <label for="db_host" class="form-label">Database Host</label>
                    <input type="text" class="form-control" id="db_host" name="db_host" value="localhost" required>
                </div>
                
                <div class="mb-3">
                    <label for="db_name" class="form-label">Database Name</label>
                    <input type="text" class="form-control" id="db_name" name="db_name" required>
                </div>
                
                <div class="mb-3">
                    <label for="db_user" class="form-label">Database Username</label>
                    <input type="text" class="form-control" id="db_user" name="db_user" required>
                </div>
                
                <div class="mb-3">
                    <label for="db_pass" class="form-label">Database Password</label>
                    <input type="password" class="form-control" id="db_pass" name="db_pass">
                </div>
                
                <div class="mt-4 text-end">
                    <a href="?step=1" class="btn btn-secondary me-2">ย้อน</a>
                    <button type="submit" class="btn btn-primary">ตั้ง</button>
                </div>
            </form>
        </div>
        <?php endif; ?>
        
        <?php if ($step === 3): ?>
        <!-- ขั้นตอน 3: เสร็จสิ้น -->
        <div class="step">
            <div class="step-header">
                <h3>ขั้นตอน 3: ตั้งค่าเสร็จสิ้น</h3>
            </div>
            
            <div class="alert alert-success">
                <h4>ตั้งค่าระบบสำเร็จ!</h4>
                <p>เข้าสู่ระบบได้ด้วยข้อมูลต่อไปนี้:</p>
                <ul>
                    <li>Username: <strong>admin</strong></li>
                    <li>Password: <strong>admin123</strong></li>
                </ul>
                <p><strong>คำแนะนำ:</strong> เปลี่ยนรหัสผ่านเมื่อเข้าสู่ระบบครั้งแรก</p>
            </div>
            
            <div class="alert alert-warning">
                <h5>ขั้นตอนต่อไป:</h5>
                <ol>
                    <li>ลบไฟล์ <code>install.php</code> ออกจากเซิร์ฟเวอร์เพื่อป้องกันการเข้าถึง</li>
                    <li>เข้าสู่ระบบและตั้งค่าระบบ</li>
                    <li>เปลี่ยนรหัสผ่านของระบบ</li>
                </ol>
            </div>
            
            <div class="mt-4 text-center">
                <a href="admin/login.php" class="btn btn-primary">เข้าสู่ระบบ</a>
                <a href="index.php" class="btn btn-secondary ms-2">ไปหน้าแรก</a>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>



