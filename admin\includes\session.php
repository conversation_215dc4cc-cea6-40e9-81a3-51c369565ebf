<?php
/**
 * GT-SportDesign Admin Session Management
 * Professional session handling for production use
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    // Secure session configuration
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');

    session_start();
}

require_once '../config/database.php';

/**
 * Check if user is authenticated as admin
 * @return bool
 */
function isAdminLoggedIn() {
    return isset($_SESSION['admin_logged_in']) &&
           $_SESSION['admin_logged_in'] === true &&
           isset($_SESSION['admin_id']) &&
           isset($_SESSION['admin_email']);
}

/**
 * Require admin authentication
 * Redirects to login if not authenticated
 */
function requireAdminAuth() {
    if (!isAdminLoggedIn()) {
        // Clear invalid session
        session_destroy();
        session_start();

        // Store intended URL for redirect after login
        $_SESSION['intended_url'] = $_SERVER['REQUEST_URI'];

        header('Location: login.php');
        exit;
    }

    // Update last activity
    $_SESSION['last_activity'] = time();
}

/**
 * Get current admin user info
 * @return array|null
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }

    return [
        'id' => $_SESSION['admin_id'] ?? null,
        'name' => $_SESSION['admin_name'] ?? 'Admin',
        'email' => $_SESSION['admin_email'] ?? '',
        'role' => $_SESSION['admin_role'] ?? 'admin'
    ];
}

/**
 * Login admin user
 */
function loginAdmin($email, $password) {
    try {
        $db = getDbConnection();
        if (!$db) {
            return ['success' => false, 'message' => 'Database connection failed'];
        }

        // Get user from database
        $stmt = $db->prepare("SELECT * FROM users WHERE email = ? AND role = 'admin' AND is_active = 1");
        $stmt->execute([$email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            logLoginAttempt($email, false, 'User not found');
            return ['success' => false, 'message' => 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'];
        }

        // Verify password
        if (!password_verify($password, $user['password'])) {
            logLoginAttempt($email, false, 'Invalid password');
            return ['success' => false, 'message' => 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'];
        }

        // Create admin session
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_user_id'] = $user['id'];
        $_SESSION['admin_email'] = $user['email'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_full_name'] = $user['full_name'];
        $_SESSION['admin_role'] = $user['role'];
        $_SESSION['login_time'] = time();
        $_SESSION['last_activity'] = time();

        // Update last login
        $stmt = $db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);

        logLoginAttempt($email, true, 'Successful login');
        logAdminActivity('login', 'Admin logged in');

        return ['success' => true, 'message' => 'เข้าสู่ระบบสำเร็จ'];

    } catch (Exception $e) {
        error_log("Admin login error: " . $e->getMessage());
        return ['success' => false, 'message' => 'เกิดข้อผิดพลาดในระบบ'];
    }
}

/**
 * Logout admin user
 */
function adminLogout() {
    // Log logout
    if (isset($_SESSION['admin_email'])) {
        logAdminActivity('logout', 'Admin logged out');
        error_log("Admin logout: " . $_SESSION['admin_email'] . " from IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown'));
    }

    // Clear all session data
    $_SESSION = array();

    // Delete session cookie
    if (isset($_COOKIE[session_name()])) {
        setcookie(session_name(), '', time() - 3600, '/');
    }

    // Destroy session
    session_destroy();

    // Start new session for flash messages
    session_start();
    $_SESSION['logout_message'] = 'ออกจากระบบเรียบร้อยแล้ว';
}

/**
 * Log login attempts
 */
function logLoginAttempt($email, $success, $message = '') {
    try {
        $logEntry = sprintf(
            "[%s] Login attempt: %s - Success: %s - Message: %s - IP: %s",
            date('Y-m-d H:i:s'),
            $email,
            $success ? 'Yes' : 'No',
            $message,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        );
        error_log($logEntry);
    } catch (Exception $e) {
        error_log("Failed to log login attempt: " . $e->getMessage());
    }
}

/**
 * Generate CSRF token
 * @return string
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * @param string $token
 * @return bool
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Security headers for admin pages
 */
function setSecurityHeaders() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
}

/**
 * Log admin activity
 * @param string $action
 * @param string $details
 */
function logAdminActivity($action, $details = '') {
    try {
        if (isAdminLoggedIn()) {
            $admin = getCurrentAdmin();
            $logEntry = sprintf(
                "[%s] Admin: %s (%s) - Action: %s - Details: %s - IP: %s",
                date('Y-m-d H:i:s'),
                $admin['name'],
                $admin['email'],
                $action,
                $details,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            );
            error_log($logEntry);
        }
    } catch (Exception $e) {
        error_log("Failed to log admin activity: " . $e->getMessage());
    }
}

/**
 * Check if user has specific permission
 */
function hasPermission($permission) {
    if (!isAdminLoggedIn()) {
        return false;
    }

    $role = $_SESSION['admin_role'];

    // Admin has all permissions
    if ($role === 'admin') {
        return true;
    }

    // Define role permissions
    $permissions = [
        'staff' => ['view_orders', 'view_products', 'view_customers', 'reply_messages'],
        'manager' => ['view_orders', 'edit_orders', 'view_products', 'edit_products', 'view_customers', 'view_reports', 'reply_messages']
    ];

    return isset($permissions[$role]) && in_array($permission, $permissions[$role]);
}

// Auto-check session timeout on every request
if (isAdminLoggedIn() && isset($_SESSION['login_time'])) {
    $sessionAge = time() - $_SESSION['login_time'];
    if ($sessionAge > 28800) { // 8 hours
        adminLogout();
    }
}
?>
