<?php
session_start();

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['admin_logged_in'])) {
    http_response_code(403);
    exit('Unauthorized');
}

require_once '../config/database.php';
$pdo = getDbConnection();

$design_id = (int)($_GET['id'] ?? 0);

if (!$design_id) {
    exit('Invalid design ID');
}

try {
    // ดึงข้อมูลการออกแบบ
    $stmt = $pdo->prepare("
        SELECT
            d.*,
            c.name as customer_name,
            c.email as customer_email,
            c.phone as customer_phone,
            p.name as product_name,
            p.base_price,
            p.description as product_description
        FROM designs d
        LEFT JOIN customers c ON d.customer_id = c.id
        LEFT JOIN products p ON d.product_id = p.id
        WHERE d.id = ?
    ");
    $stmt->execute([$design_id]);
    $design = $stmt->fetch();

    if (!$design) {
        exit('Design not found');
    }

    // ดึงข้อมูลการออกแบบ (JSON)
    $design_data = json_decode($design['design_data'], true);

} catch (Exception $e) {
    exit('Error: ' . $e->getMessage());
}
?>

<div class="row">
    <!-- ข้อมูลพื้นฐาน -->
    <div class="col-md-6">
        <h6 class="mb-3"><i class="fas fa-info-circle me-2"></i>ข้อมูลพื้นฐาน</h6>

        <table class="table table-sm">
            <tr>
                <td><strong>ชื่อการออกแบบ:</strong></td>
                <td><?= htmlspecialchars($design['design_name']) ?></td>
            </tr>
            <tr>
                <td><strong>ลูกค้า:</strong></td>
                <td>
                    <?= htmlspecialchars($design['customer_name'] ?? 'ไม่ระบุ') ?>
                    <?php if ($design['customer_email']): ?>
                        <br><small class="text-muted"><?= htmlspecialchars($design['customer_email']) ?></small>
                    <?php endif; ?>
                    <?php if ($design['customer_phone']): ?>
                        <br><small class="text-muted"><?= htmlspecialchars($design['customer_phone']) ?></small>
                    <?php endif; ?>
                </td>
            </tr>
            <tr>
                <td><strong>สินค้า:</strong></td>
                <td>
                    <?= htmlspecialchars($design['product_name']) ?>
                    <br><small class="text-muted">ราคาฐาน: <?= number_format($design['base_price']) ?> บาท</small>
                </td>
            </tr>
            <tr>
                <td><strong>สถานะ:</strong></td>
                <td>
                    <?php
                    $status_text = [
                        'pending' => '<span class="badge bg-warning">รอตรวจสอบ</span>',
                        'approved' => '<span class="badge bg-success">อนุมัติแล้ว</span>',
                        'rejected' => '<span class="badge bg-danger">ถูกปฏิเสธ</span>',
                        'changes_requested' => '<span class="badge bg-secondary">ขอแก้ไข</span>',
                        'in_production' => '<span class="badge bg-info">กำลังผลิต</span>'
                    ];
                    echo $status_text[$design['status']] ?? $design['status'];
                    ?>
                </td>
            </tr>
            <tr>
                <td><strong>วันที่สร้าง:</strong></td>
                <td><?= date('d/m/Y H:i:s', strtotime($design['created_at'])) ?></td>
            </tr>
            <tr>
                <td><strong>อัพเดตล่าสุด:</strong></td>
                <td><?= date('d/m/Y H:i:s', strtotime($design['updated_at'])) ?></td>
            </tr>
        </table>

        <?php if ($design['admin_notes']): ?>
            <div class="alert alert-info">
                <strong>หมายเหตุจาก Admin:</strong><br>
                <?= nl2br(htmlspecialchars($design['admin_notes'])) ?>
            </div>
        <?php endif; ?>

        <?php if ($design['customer_notes']): ?>
            <div class="alert alert-light">
                <strong>คำขอจากลูกค้า:</strong><br>
                <?= nl2br(htmlspecialchars($design['customer_notes'])) ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- ตัวอย่างการออกแบบ -->
    <div class="col-md-6">
        <h6 class="mb-3"><i class="fas fa-palette me-2"></i>ตัวอย่างการออกแบบ</h6>

        <?php if ($design['preview_image']): ?>
            <div class="text-center mb-3">
                <img src="<?= htmlspecialchars($design['preview_image']) ?>"
                     alt="Design Preview" class="img-fluid rounded"
                     style="max-height: 300px;">
            </div>
        <?php else: ?>
            <div class="bg-light rounded p-4 text-center mb-3" style="height: 300px;">
                <i class="fas fa-image fa-3x text-muted mb-2"></i>
                <p class="text-muted">ไม่มีรูปตัวอย่าง</p>
            </div>
        <?php endif; ?>

        <!-- ข้อมูลการออกแบบ -->
        <?php if ($design_data): ?>
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">รายละเอียดการออกแบบ</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($design_data['selectedProduct'])): ?>
                        <p><strong>ประเภทสินค้า:</strong> <?= htmlspecialchars($design_data['selectedProduct']) ?></p>
                    <?php endif; ?>

                    <?php if (isset($design_data['selectedColor'])): ?>
                        <p><strong>สีที่เลือก:</strong> <?= htmlspecialchars($design_data['selectedColor']) ?></p>
                    <?php endif; ?>

                    <?php if (isset($design_data['selectedSize'])): ?>
                        <p><strong>ขนาดที่เลือก:</strong> <?= htmlspecialchars($design_data['selectedSize']) ?></p>
                    <?php endif; ?>

                    <?php if (isset($design_data['quantity'])): ?>
                        <p><strong>จำนวน:</strong> <?= (int)$design_data['quantity'] ?> ตัว</p>
                    <?php endif; ?>

                    <?php if (isset($design_data['elements']) && is_array($design_data['elements'])): ?>
                        <div class="mt-3">
                            <strong>องค์ประกอบการออกแบบ:</strong>
                            <ul class="list-unstyled mt-2">
                                <?php foreach ($design_data['elements'] as $index => $element): ?>
                                    <li class="mb-2">
                                        <div class="card card-body py-2">
                                            <?php if (isset($element['type'])): ?>
                                                <strong>ประเภท:</strong> <?= htmlspecialchars($element['type']) ?><br>
                                            <?php endif; ?>

                                            <?php if (isset($element['text'])): ?>
                                                <strong>ข้อความ:</strong> "<?= htmlspecialchars($element['text']) ?>"<br>
                                            <?php endif; ?>

                                            <?php if (isset($element['fontSize'])): ?>
                                                <strong>ขนาดฟอนต์:</strong> <?= (int)$element['fontSize'] ?>px<br>
                                            <?php endif; ?>

                                            <?php if (isset($element['color'])): ?>
                                                <strong>สี:</strong> <span style="background: <?= htmlspecialchars($element['color']) ?>; padding: 2px 8px; border-radius: 3px; color: white;"><?= htmlspecialchars($element['color']) ?></span><br>
                                            <?php endif; ?>

                                            <?php if (isset($element['x']) && isset($element['y'])): ?>
                                                <strong>ตำแหน่ง:</strong> X: <?= (int)$element['x'] ?>, Y: <?= (int)$element['y'] ?>
                                            <?php endif; ?>
                                        </div>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- การดำเนินการ -->
<div class="row mt-4">
    <div class="col-12">
        <h6 class="mb-3"><i class="fas fa-cogs me-2"></i>การดำเนินการ</h6>

        <div class="d-flex gap-2 flex-wrap">
            <?php if ($design['status'] === 'pending'): ?>
                <button class="btn btn-success btn-sm" onclick="updateStatusInline(<?= $design['id'] ?>, 'approve')">
                    <i class="fas fa-check me-1"></i>อนุมัติ
                </button>
                <button class="btn btn-warning btn-sm" onclick="updateStatusInline(<?= $design['id'] ?>, 'request_changes')">
                    <i class="fas fa-edit me-1"></i>ขอแก้ไข
                </button>
                <button class="btn btn-danger btn-sm" onclick="updateStatusInline(<?= $design['id'] ?>, 'reject')">
                    <i class="fas fa-times me-1"></i>ปฏิเสธ
                </button>
            <?php elseif ($design['status'] === 'approved'): ?>
                <button class="btn btn-info btn-sm" onclick="updateStatusInline(<?= $design['id'] ?>, 'in_production')">
                    <i class="fas fa-cog me-1"></i>เริ่มผลิต
                </button>
            <?php endif; ?>

            <button class="btn btn-outline-secondary btn-sm" onclick="printDesign()">
                <i class="fas fa-print me-1"></i>พิมพ์รายละเอียด
            </button>

            <?php if ($design['customer_email']): ?>
                <a href="mailto:<?= htmlspecialchars($design['customer_email']) ?>" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-envelope me-1"></i>ส่งอีเมล
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function updateStatusInline(designId, action) {
    if (confirm('คุณต้องการดำเนินการนี้หรือไม่?')) {
        // ซ่อน modal ปัจจุบันและเปิด status modal
        bootstrap.Modal.getInstance(document.getElementById('designDetailModal')).hide();

        setTimeout(() => {
            updateStatus(designId, action);
        }, 300);
    }
}

function printDesign() {
    const printContent = document.getElementById('designDetailContent').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>รายละเอียดการออกแบบ - <?= htmlspecialchars($design['design_name']) ?></title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <style>
                body { font-family: 'Sarabun', sans-serif; }
                @media print {
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="container mt-4">
                <h3 class="mb-4">รายละเอียดการออกแบบ</h3>
                ${printContent}
            </div>
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
}
</script>
