<?php
session_start();
require_once './config/database.php';

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['customer_logged_in'])) {
    header("Location: login.php");
    exit;
}

$pdo = getDbConnection();
$customer_id = $_SESSION['customer_id'];
$customer_name = $_SESSION['customer_name'];

// ตัวแปรสำหรับการค้นหาและกรอง
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// สร้าง SQL สำหรับการค้นหาและกรอง
$where_conditions = ["o.customer_id = ?"];
$params = [$customer_id];

if ($search) {
    $where_conditions[] = "(o.order_number LIKE ? OR p.name LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "o.status = ?";
    $params[] = $status_filter;
}

if ($date_from) {
    $where_conditions[] = "DATE(o.created_at) >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "DATE(o.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = implode(' AND ', $where_conditions);

// ดึงข้อมูลคำสั่งซื้อ
try {
    // นับจำนวนทั้งหมด
    $count_sql = "
        SELECT COUNT(*)
        FROM orders o
        LEFT JOIN products p ON o.product_id = p.id
        WHERE $where_clause
    ";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_records = $stmt->fetchColumn();
    $total_pages = ceil($total_records / $limit);

    // ดึงข้อมูลสำหรับแสดงผล
    $sql = "
        SELECT o.*, p.name as product_name, p.price as product_price,
               pi.file_id as product_image
        FROM orders o
        LEFT JOIN products p ON o.product_id = p.id
        LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.image_type = 'main'
        WHERE $where_clause
        ORDER BY o.created_at DESC
        LIMIT $limit OFFSET $offset
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();

} catch (Exception $e) {
    $orders = [];
    $total_records = 0;
    $total_pages = 0;
    $error = "เกิดข้อผิดพลาด: " . $e->getMessage();
}

// ฟังก์ชันแสดงสถานะ
function getStatusInfo($status) {
    $statuses = [
        'pending' => ['text' => 'รอดำเนินการ', 'class' => 'warning', 'icon' => 'clock'],
        'processing' => ['text' => 'กำลังผลิต', 'class' => 'info', 'icon' => 'cogs'],
        'completed' => ['text' => 'เสร็จสิ้น', 'class' => 'success', 'icon' => 'check-circle'],
        'cancelled' => ['text' => 'ยกเลิก', 'class' => 'danger', 'icon' => 'times-circle'],
        'shipped' => ['text' => 'จัดส่งแล้ว', 'class' => 'primary', 'icon' => 'shipping-fast']
    ];
    return $statuses[$status] ?? ['text' => 'ไม่ระบุ', 'class' => 'secondary', 'icon' => 'question'];
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>คำสั่งซื้อของฉัน - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.8rem;
            color: white !important;
        }

        .sidebar {
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 15px;
            padding: 30px;
            height: fit-content;
            position: sticky;
            top: 100px;
        }

        .sidebar .nav-link {
            color: #6c757d;
            font-weight: 500;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            color: white;
            transform: translateX(5px);
        }

        .main-content {
            padding: 30px;
        }

        .page-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .page-subtitle {
            color: #6c757d;
            margin-bottom: 0;
        }

        .filter-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .filter-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #11be97;
            box-shadow: 0 0 0 0.2rem rgba(17, 190, 151, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 600;
        }

        .btn-outline-secondary {
            border-radius: 10px;
            padding: 12px 25px;
            font-weight: 500;
        }

        .order-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .order-header {
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .order-number {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .order-date {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .product-info {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .product-image {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            object-fit: cover;
            margin-right: 15px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .product-details h6 {
            margin-bottom: 5px;
            color: #2c3e50;
        }

        .product-details p {
            margin-bottom: 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .order-footer {
            display: flex;
            justify-content: between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .order-total {
            font-size: 1.3rem;
            font-weight: 700;
            color: #11be97;
        }

        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            display: inline-flex;
            align-items: center;
        }

        .status-badge i {
            margin-right: 5px;
        }

        .pagination {
            justify-content: center;
            margin-top: 30px;
        }

        .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #11be97;
        }

        .page-link:hover {
            background: #11be97;
            color: white;
        }

        .page-item.active .page-link {
            background: #11be97;
            border-color: #11be97;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .empty-state i {
            font-size: 4rem;
            color: #dee2e6;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-bottom: 20px;
            }

            .main-content {
                padding: 15px;
            }

            .order-footer {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .product-info {
                flex-direction: column;
                text-align: center;
            }

            .product-image {
                margin-right: 0;
                margin-bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i><?= htmlspecialchars($customer_name) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="customer_profile.php">
                                <i class="fas fa-user-edit me-2"></i>แก้ไขโปรไฟล์
                            </a></li>
                            <li><a class="dropdown-item" href="customer_settings.php">
                                <i class="fas fa-cog me-2"></i>ตั้งค่า
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-lg-3">
                <div class="sidebar">
                    <nav class="nav flex-column">
                        <a class="nav-link" href="customer_dashboard.php">
                            <i class="fas fa-tachometer-alt me-3"></i>แดชบอร์ด
                        </a>
                        <a class="nav-link active" href="customer_orders.php">
                            <i class="fas fa-shopping-cart me-3"></i>คำสั่งซื้อของฉัน
                        </a>
                        <a class="nav-link" href="customer_designs.php">
                            <i class="fas fa-palette me-3"></i>การออกแบบของฉัน
                        </a>
                        <a class="nav-link" href="customer_profile.php">
                            <i class="fas fa-user-edit me-3"></i>ข้อมูลส่วนตัว
                        </a>
                        <a class="nav-link" href="customer_settings.php">
                            <i class="fas fa-cog me-3"></i>ตั้งค่า
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="main-content">
                    <!-- Page Header -->
                    <div class="page-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h1 class="page-title">คำสั่งซื้อของฉัน</h1>
                                <p class="page-subtitle">จัดการและติดตามคำสั่งซื้อทั้งหมดของคุณ</p>
                            </div>
                            <div>
                                <a href="index.php" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>สั่งซื้อใหม่
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="filter-card">
                        <h5 class="filter-title">ค้นหาและกรองข้อมูล</h5>
                        <form method="GET" action="">
                            <div class="row">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" name="search"
                                           placeholder="ค้นหาเลขที่คำสั่งซื้อ/สินค้า"
                                           value="<?= htmlspecialchars($search) ?>">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" name="status">
                                        <option value="">สถานะทั้งหมด</option>
                                        <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>รอดำเนินการ</option>
                                        <option value="processing" <?= $status_filter === 'processing' ? 'selected' : '' ?>>กำลังผลิต</option>
                                        <option value="completed" <?= $status_filter === 'completed' ? 'selected' : '' ?>>เสร็จสิ้น</option>
                                        <option value="shipped" <?= $status_filter === 'shipped' ? 'selected' : '' ?>>จัดส่งแล้ว</option>
                                        <option value="cancelled" <?= $status_filter === 'cancelled' ? 'selected' : '' ?>>ยกเลิก</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input type="date" class="form-control" name="date_from"
                                           value="<?= htmlspecialchars($date_from) ?>" placeholder="จากวันที่">
                                </div>
                                <div class="col-md-2">
                                    <input type="date" class="form-control" name="date_to"
                                           value="<?= htmlspecialchars($date_to) ?>" placeholder="ถึงวันที่">
                                </div>
                                <div class="col-md-3">
                                    <div class="d-flex gap-2">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-2"></i>ค้นหา
                                        </button>
                                        <a href="customer_orders.php" class="btn btn-outline-secondary">
                                            <i class="fas fa-times me-2"></i>ล้าง
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- Orders List -->
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($orders): ?>
                        <?php foreach ($orders as $order): ?>
                            <?php $status_info = getStatusInfo($order['status']); ?>
                            <div class="order-card">
                                <div class="order-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="order-number">
                                                #<?= htmlspecialchars($order['order_number'] ?? 'ORD-' . $order['id']) ?>
                                            </div>
                                            <div class="order-date">
                                                สั่งซื้อเมื่อ <?= date('d/m/Y H:i น.', strtotime($order['created_at'])) ?>
                                            </div>
                                        </div>
                                        <span class="status-badge bg-<?= $status_info['class'] ?> text-white">
                                            <i class="fas fa-<?= $status_info['icon'] ?>"></i>
                                            <?= $status_info['text'] ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="product-info">
                                    <div class="product-image">
                                        <?php if ($order['product_image']): ?>
                                            <img src="../uploads/products/<?= htmlspecialchars($order['product_image']) ?>"
                                                 alt="Product" class="w-100 h-100">
                                        <?php else: ?>
                                            <i class="fas fa-tshirt text-muted" style="font-size: 2rem;"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="product-details flex-grow-1">
                                        <h6><?= htmlspecialchars($order['product_name'] ?? 'สินค้าที่ลบแล้ว') ?></h6>
                                        <p>จำนวน: <?= number_format($order['quantity']) ?> ชิ้น</p>
                                        <?php if ($order['notes']): ?>
                                            <p><strong>หมายเหตุ:</strong> <?= htmlspecialchars($order['notes']) ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="order-footer">
                                    <div class="order-total">
                                        ยอดรวม: ฿<?= number_format($order['total_amount']) ?>
                                    </div>
                                    <div class="d-flex gap-2">
                                        <a href="order_detail.php?id=<?= $order['id'] ?>" class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                                        </a>
                                        <?php if ($order['status'] === 'pending'): ?>
                                            <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder(<?= $order['id'] ?>)">
                                                <i class="fas fa-times me-2"></i>ยกเลิก
                                            </button>
                                        <?php endif; ?>
                                        <?php if ($order['status'] === 'shipped'): ?>
                                            <button class="btn btn-success btn-sm" onclick="confirmReceived(<?= $order['id'] ?>)">
                                                <i class="fas fa-check me-2"></i>ยืนยันได้รับสินค้า
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Orders pagination">
                                <ul class="pagination">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $date_from ? '&date_from=' . urlencode($date_from) : '' ?><?= $date_to ? '&date_to=' . urlencode($date_to) : '' ?>">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>

                                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                            <a class="page-link" href="?page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $date_from ? '&date_from=' . urlencode($date_from) : '' ?><?= $date_to ? '&date_to=' . urlencode($date_to) : '' ?>">
                                                <?= $i ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>

                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $date_from ? '&date_from=' . urlencode($date_from) : '' ?><?= $date_to ? '&date_to=' . urlencode($date_to) : '' ?>">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>

                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-shopping-cart"></i>
                            <h4>ไม่พบคำสั่งซื้อ</h4>
                            <p class="text-muted">คุณยังไม่มีคำสั่งซื้อ หรือไม่พบคำสั่งซื้อที่ตรงกับการค้นหา</p>
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เริ่มสั่งซื้อ
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // ฟังก์ชันยกเลิกคำสั่งซื้อ
        function cancelOrder(orderId) {
            Swal.fire({
                title: 'ยืนยันการยกเลิก',
                text: 'คุณต้องการยกเลิกคำสั่งซื้อนี้หรือไม่?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'ยกเลิก',
                cancelButtonText: 'ไม่ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    // ส่งคำขอยกเลิกคำสั่งซื้อ
                    fetch('order_action.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'cancel',
                            order_id: orderId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('สำเร็จ!', 'ยกเลิกคำสั่งซื้อเรียบร้อยแล้ว', 'success')
                                .then(() => location.reload());
                        } else {
                            Swal.fire('เกิดข้อผิดพลาด!', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        Swal.fire('เกิดข้อผิดพลาด!', 'ไม่สามารถดำเนินการได้', 'error');
                    });
                }
            });
        }

        // ฟังก์ชันยืนยันได้รับสินค้า
        function confirmReceived(orderId) {
            Swal.fire({
                title: 'ยืนยันได้รับสินค้า',
                text: 'คุณได้รับสินค้าเรียบร้อยแล้วหรือไม่?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'ยืนยัน',
                cancelButtonText: 'ยังไม่ได้รับ'
            }).then((result) => {
                if (result.isConfirmed) {
                    // ส่งคำขอยืนยันได้รับสินค้า
                    fetch('order_action.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'confirm_received',
                            order_id: orderId
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire('สำเร็จ!', 'ยืนยันได้รับสินค้าเรียบร้อยแล้ว', 'success')
                                .then(() => location.reload());
                        } else {
                            Swal.fire('เกิดข้อผิดพลาด!', data.message, 'error');
                        }
                    })
                    .catch(error => {
                        Swal.fire('เกิดข้อผิดพลาด!', 'ไม่สามารถดำเนินการได้', 'error');
                    });
                }
            });
        }
    </script>
</body>
</html>
