<?php
// ไฟล์ header สำหรับเว็บไซต์ GT Sport Design
// ไม่ต้องมี DOCTYPE เพราะจะถูกเรียกใช้ใน index.php ที่มี DOCTYPE แล้ว
?>

<!-- Top Header -->
<div class="top-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <i class="fas fa-phone me-2"></i>
                    <span>โทร: ************</span>
                    <span class="mx-3">|</span>
                    <i class="fas fa-envelope me-2"></i>
                    <span><EMAIL></span>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <div class="contact-icons">
                    <a href="tel:0855599164" class="phone" title="โทรหาเรา">
                        <i class="fas fa-phone"></i>
                    </a>
                    <a href="https://line.me/ti/p/@gtsport" class="line" title="Line: @gtsport">
                        <i class="fab fa-line"></i>
                    </a>
                    <a href="https://www.facebook.com/GTSportDesign.1" class="facebook" title="Facebook">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Navigation -->

<style>
    :root {
        --primary-color: #ee501b;
        --secondary-color: #ff6b35;
        --dark-color: #303136;
        --light-gray: #f1f0f0;
    }

    body {
        font-family: 'Kanit', sans-serif;
        line-height: 1.6;
    }

    /* Header Styles */
    .top-header {
        background: var(--dark-color);
        color: white;
        padding: 8px 0;
        font-size: 14px;
    }

    .top-header a {
        color: white;
        text-decoration: none;
    }

    .main-navbar {
        background: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 15px 0;
    }

    .navbar-brand {
        color: var(--primary-color) !important;
        font-weight: 700;
        font-size: 2rem;
    }

    .navbar-nav .nav-link {
        color: var(--dark-color) !important;
        font-weight: 500;
        margin: 0 10px;
        transition: color 0.3s ease;
    }

    .navbar-nav .nav-link:hover {
        color: var(--primary-color) !important;
    }

    .contact-icons {
        display: flex;
        gap: 15px;
        align-items: center;
    }

    .contact-icons a {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        text-decoration: none;
        transition: transform 0.3s ease;
    }

    .contact-icons a:hover {
        transform: scale(1.1);
    }

    .contact-icons .phone {
        background: #25d366;
    }

    .contact-icons .line {
        background: #00c300;
    }

    .contact-icons .facebook {
        background: #1877f2;
    }

    /* Hero Section - คล้าย Finix Sports */
    .hero-section {
        /* background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%); */
        color: white;
        min-height: 600px;
        position: relative;
        overflow: hidden;
    }

    /* .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 50%;
            height: 100%;
            background: url('https://ext.same-assets.com/4182383019/921381138.jpeg') no-repeat center;
            background-size: cover;
            opacity: 0.3;
        } */

    .hero-content {
        position: relative;
        z-index: 2;
        padding: 100px 0;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
        font-size: 1.3rem;
        margin-bottom: 30px;
        opacity: 0.9;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        margin: 30px 0;
    }

    .feature-list li {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        font-size: 1.1rem;
    }

    .feature-list li i {
        margin-right: 15px;
        width: 30px;
        height: 30px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-hero {
        background: white;
        color: var(--primary-color);
        border: none;
        padding: 15px 40px;
        font-size: 1.2rem;
        font-weight: 600;
        border-radius: 50px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-hero:hover {
        background: var(--light-gray);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    /* Features Section */
    .features-section {
        padding: 50px 0;
        background: white;
    }

    .feature-card {
        text-align: center;
        padding: 40px 20px;
        border-radius: 15px;
        transition: all 0.3s ease;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: white;
        font-size: 2rem;
    }

    .feature-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: var(--dark-color);
    }

    /* Products Section */
    .products-section {
        padding: 80px 0;
        background: var(--light-gray);
    }

    .section-title {
        text-align: center;
        margin-bottom: 60px;
    }

    .section-title h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--dark-color);
        margin-bottom: 15px;
    }

    .section-title .highlight {
        color: #ffffff;
        ;
    }

    .product-card {

        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;

    }

    .product-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    }

    .product-image {
        height: 415px;
        background: #eb4e17;
        position: relative;
        overflow: hidden;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image img {
        transform: scale(1.1);
    }

    .product-image .placeholder {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #999;
        font-size: 3rem;
    }

    .product-info {
        padding: 50px;
    }

    .product-category {
        color: var(--primary-color);
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .product-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin: 10px 0;
        color: var(--dark-color);
    }

    .product-price {
        font-size: 1.4rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    /* Gallery Section */
    .gallery-section {
        padding: 80px 0;
        background: white;
    }

    .gallery-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 40px;
    }

    .gallery-item {
        position: relative;
        border-radius: 15px;
        overflow: hidden;
        height: 285px;
        background: #eb4e17;
    }

    .gallery-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .gallery-item:hover img {
        transform: scale(1.1);
    }

    .gallery-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(238, 80, 27, 0.8), rgba(255, 107, 53, 0.8));
        opacity: 0;
        transition: opacity 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
    }

    .gallery-item:hover .gallery-overlay {
        opacity: 1;
    }

    /* Reviews Section */
    .reviews-section {
        padding: 80px 0;
        background: var(--light-gray);
    }

    .review-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        height: 100%;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }

    .review-card:hover {
        transform: translateY(-5px);
    }

    .review-stars {
        color: #ffc107;
        margin-bottom: 15px;
    }

    .review-text {
        font-style: italic;
        color: #666;
        margin-bottom: 20px;
    }

    .review-author {
        display: flex;
        align-items: center;
    }

    .review-avatar {
        width: 50px;
        height: 50px;
        background: var(--primary-color);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        margin-right: 15px;
    }

    .review-name {
        font-weight: 600;
        color: var(--dark-color);
    }

    /* Footer */
    .footer {
        background: var(--dark-color);
        color: white;
        padding: 50px 0 20px;
    }

    .footer h5 {
        color: var(--primary-color);
        margin-bottom: 20px;
    }

    .footer a {
        color: #ccc;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .footer a:hover {
        color: var(--primary-color);
    }

    .footer-bottom {
        border-top: 1px solid #444;
        padding-top: 20px;
        margin-top: 30px;
        text-align: center;
        color: #999;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .contact-icons {
            margin-top: 10px;
        }

        .section-title h2 {
            font-size: 2rem;
        }
    }

    /* Chat Widget */
    .chat-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
    }

    .chat-button {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(238, 80, 27, 0.4);
        transition: all 0.3s ease;
        animation: pulse 2s infinite;
    }

    .chat-button:hover {
        transform: scale(1.1);
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 4px 15px rgba(238, 80, 27, 0.4);
        }

        50% {
            box-shadow: 0 4px 25px rgba(238, 80, 27, 0.6);
        }

        100% {
            box-shadow: 0 4px 15px rgba(238, 80, 27, 0.4);
        }
    }
</style>
<style>
    body {
        font-family: 'Kanit', sans-serif;
    }

    /* .hero-section {
            background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
            color: white;
            padding: 120px 0 80px;
            min-height: 100vh;
            display: flex;
            align-items: center;
        } */

    .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.5rem;
        font-weight: 400;
        margin-bottom: 2rem;
        opacity: 0.9;
    }

    .btn-hero {
        padding: 15px 30px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 50px;
        transition: all 0.3s ease;
    }

    .btn-hero:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }

    .feature-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .feature-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: white;
        font-size: 2rem;
    }

    .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: rgb(240, 241, 243);
        margin-bottom: 1rem;
    }

    .section-subtitle {
        font-size: 1.2rem;
        color: #6c757d;
        margin-bottom: 3rem;
    }

    .navbar {
        background: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .navbar-brand {
        font-weight: 700;
        color: #eb4e17 !important;
        font-size: 1.5rem;
    }

    .nav-link {
        font-weight: 500;
        color: #2c3e50 !important;
        transition: color 0.3s ease;
    }

    .nav-link:hover {
        color: #eb4e17 !important;
    }

    .footer {
        background: #2c3e50;
        color: white;
        padding: 50px 0 20px;
    }

    .footer h5 {
        color: #eb4e17;
        margin-bottom: 20px;
    }

    .footer a {
        color: #bdc3c7;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .footer a:hover {
        color: #eb4e17;
    }
</style>
<link rel="stylesheet" id="hello-elementor-css"
    href="https://www.finixsports.com/wp-content/themes/hello-elementor/style.min.css?ver=3.4.3" media="all">
<link rel="stylesheet" id="hello-elementor-theme-style-css"
    href="https://www.finixsports.com/wp-content/themes/hello-elementor/theme.min.css?ver=3.4.3" media="all">
<link rel="stylesheet" id="hello-elementor-header-footer-css"
    href="https://www.finixsports.com/wp-content/themes/hello-elementor/header-footer.min.css?ver=3.4.3" media="all">
<link rel="stylesheet"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/css/conditionals/dialog.min.css?ver=3.29.2">
<link rel="stylesheet" id="elementor-frontend-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/custom-frontend.min.css?ver=1749131441"
    media="all">
<link rel="stylesheet" id="elementor-post-7-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/post-7.css?ver=1749131441" media="all">
<link rel="stylesheet" id="e-animation-fadeInRight-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/lib/animations/styles/fadeInRight.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-icon-list-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/custom-widget-icon-list.min.css?ver=1749131441"
    media="all">
<link rel="stylesheet" id="e-animation-fadeInUp-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/lib/animations/styles/fadeInUp.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-image-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-nav-menu-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/custom-pro-widget-nav-menu.min.css?ver=1749131441"
    media="all">
<link rel="stylesheet" id="e-animation-grow-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/lib/animations/styles/e-animation-grow.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-icon-box-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/custom-widget-icon-box.min.css?ver=1749131441"
    media="all">
<link rel="stylesheet" id="e-sticky-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor-pro/assets/css/modules/sticky.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-heading-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-spacer-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/css/widget-spacer.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="e-animation-fadeIn-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/lib/animations/styles/fadeIn.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="e-animation-float-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/lib/animations/styles/e-animation-float.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="swiper-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/lib/swiper/v8/css/swiper.min.css?ver=8.4.5"
    media="all">
<link rel="stylesheet" id="e-swiper-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/css/conditionals/e-swiper.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-image-carousel-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/css/widget-image-carousel.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-gallery-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor-pro/assets/css/widget-gallery.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="elementor-gallery-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/lib/e-gallery/css/e-gallery.min.css?ver=1.2.0"
    media="all">
<link rel="stylesheet" id="e-transitions-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor-pro/assets/css/conditionals/transitions.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-rating-css"
    href="https://www.finixsports.com/wp-content/plugins/elementor/assets/css/widget-rating.min.css?ver=3.29.2"
    media="all">
<link rel="stylesheet" id="widget-nested-tabs-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/custom-widget-nested-tabs.min.css?ver=1749131441"
    media="all">
<link rel="stylesheet" id="elementor-post-234-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/post-234.css?ver=1749131677" media="all">
<link rel="stylesheet" id="elementor-post-16-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/post-16.css?ver=1749131444" media="all">
<link rel="stylesheet" id="elementor-post-29-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/post-29.css?ver=1749131444" media="all">
<link rel="stylesheet" id="eael-general-css"
    href="https://www.finixsports.com/wp-content/plugins/essential-addons-for-elementor-lite/assets/front-end/css/view/general.min.css?ver=6.1.18"
    media="all">
<link rel="stylesheet" id="elementor-post-190-css" href="uploads/elementor/css/post-190.css" media="all">
<link rel='stylesheet' id='elementor-post-222-css' href='uploads/elementor/css/post-222.css' media='all' />
<link rel="stylesheet" id="elementor-post-16-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/post-16.css?ver=1749131444" media="all">
<link rel="stylesheet" id="elementor-post-29-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/css/post-29.css?ver=1749131444" media="all">
<link rel="stylesheet" id="eael-general-css"
    href="https://www.finixsports.com/wp-content/plugins/essential-addons-for-elementor-lite/assets/front-end/css/view/general.min.css?ver=6.1.18"
    media="all">
<link rel="stylesheet" id="elementor-gf-local-kanit-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/google-fonts/css/kanit.css?ver=1742219180"
    media="all">
<link rel="stylesheet" id="elementor-gf-local-rubik-css"
    href="https://www.finixsports.com/wp-content/uploads/elementor/google-fonts/css/rubik.css?ver=1742219192"
    media="all">


<link rel="icon" href="assets/images/icon/ic-logo.png" sizes="32x32">
<link rel="icon" href="assets/images/icon/ic-logo.png" sizes="192x192">
<link rel="apple-touch-icon" href="assets/images/icon/ic-logo.png">
<meta name="msapplication-TileImage" content="assets/images/icon/ic-logo.png">
<style id="wp-custom-css">
    b {
        font-weight: 500;
    }
</style>
</head>

<body
    class="wp-singular page-template page-template-elementor_header_footer page page-id-190 wp-custom-logo wp-embed-responsive wp-theme-hello-elementor hello-elementor-default elementor-default elementor-template-full-width elementor-kit-7 elementor-page elementor-page-190 e--ua-blink e--ua-chrome e--ua-webkit"
    data-elementor-device-mode="tablet">


    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top">
        <style>
            .elementor-widget-container {
                text-align: center;
                padding: 8px;
            }

            .elementor-container-menubar img {
                max-width: 55%;
                height: auto;
            }

            @media (max-width: 768px) {
                .elementor-widget-container {
                    padding: 5px;
                }
            }
        </style>
        <div class="elementor-container-menubar img">
            <a href="https://gtsportdesign.com/">
                <img src="/assets/images/logo/Gt_Logo.png" alt="GT Sport Logo">
            </a>
        </div>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-home me-1"></i>หน้าแรก
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="products-and-services.php">
                        <i class="fas fa-tshirt me-1"></i>สินค้าและบริการ
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="shirt-design.php">
                        <i class="fas fa-tshirt me-1"></i>ผลงานออกแบบ
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="customer-review.php">
                        <i class="fas fa-tshirt me-1"></i>รีวิวจากลูกค้า
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="design.php">
                        <i class="fas fa-palette me-1"></i>ออกแบบ
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="contact.php">
                        <i class="fas fa-phone me-1"></i>ติดต่อเรา
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="track_order.php">
                        <i class="fas fa-shipping-fast me-1"></i>ติดตามคำสั่งซื้อ
                    </a>
                </li>
            </ul>

            <ul class="navbar-nav">
                <?php if (isset($_SESSION['user_logged_in']) && $_SESSION['user_logged_in']): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
                            data-bs-toggle="dropdown">
                            <i
                                class="fas fa-user me-1"></i><?php echo htmlspecialchars($_SESSION['user_name'] ?? 'ผู้ใช้'); ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="auth/profile.php"><i class="fas fa-user me-2"></i>โปรไฟล์</a>
                            </li>
                            <li>
                                <hr class="dropdown-divider">
                            </li>
                            <li><a class="dropdown-item" href="auth/logout.php"><i
                                        class="fas fa-sign-out-alt me-2"></i>ออกจากระบบ</a></li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="auth/login.php">
                            <i class="fas fa-sign-in-alt me-1"></i>เข้าสู่ระบบ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="auth/register.php">
                            <i class="fas fa-user-plus me-1"></i>สมัครสมาชิก
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
        </div>
    </nav>

    <!-- Add padding to body to account for fixed navbar -->
    <style>
        body {
            padding-top: 76px;
        }

        .navbar-brand {
            font-size: 1.5rem;
            color: #eb4e17 !important;
        }

        .nav-link {
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #eb4e17 !important;
        }

        .navbar {
            border-bottom: 1px solid #e9ecef;
        }
    </style>