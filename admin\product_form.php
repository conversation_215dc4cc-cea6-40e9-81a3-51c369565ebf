<?php
$admin_page_title = "เพิ่ม/แก้ไข ค้า";
include 'includes/header.php'; // Protects page
require_once '../includes/config.php';
require_once '../includes/db_connect.php';
require_once '../includes/functions.php';

// Determine if it's an edit or add new form
$product_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$is_edit_mode = ($product_id > 0);

$product_data = [
    'id' => $product_id,
    'name' => '',
    'category_id' => '', // Will be ID from categories table
    'description' => '',
    'price' => '',
    'stock_quantity' => 0,
    'sku' => '',
    'image_url' => ''
];

// Simulated categories data (replace with database query)
$categories = [
    ['id' => 1, 'name' => 'เสื้อ'],
    ['id' => 2, 'name' => 'เสื้อ=<'],
    ['id' => 3, 'name' => 'เสื้อโปโล'],
    ['id' => 4, 'name' => 'เสื้อ=<'],
    ['id' => 5, 'name' => 'ฟอร์ม']
];

if ($is_edit_mode) {
    // In a real app, fetch product data from database by $product_id
    // For simulation, let's assume we find a product if ID is 1, 2 or 3
    $simulated_products_db = [
        1 => ['id' => 1, 'name' => 'เสื้อ=< V.1', 'category_id' => 1, 'description' => 'เสื้อ=<', 'price' => 790.00, 'stock_quantity' => 50, 'sku' => 'FBT001', 'image_url' => 'images/placeholder_product.png'],
        2 => ['id' => 2, 'name' => 'เสื้อ=<แขนยาว ProRun', 'category_id' => 2, 'description' => 'เสื้อ=<แขนยาว', 'price' => 650.00, 'stock_quantity' => 30, 'sku' => 'RUN002', 'image_url' => 'images/placeholder_product.png'],
    ];
    if (isset($simulated_products_db[$product_id])) {
        $product_data = $simulated_products_db[$product_id];
    } else {
        // Product not found, redirect or show error
        $_SESSION['message'] = "ไม่พบค้าที่ต้องการแก้ไข (ID: {$product_id})";
        $_SESSION['message_type'] = 'error';
        header('Location: products.php');
        exit;
    }
}

// Handle messages from product_process.php if redirected back due to error
$form_error_message = isset($_SESSION['form_error']) ? $_SESSION['form_error'] : '';
if(!empty($form_error_message)) {
    // If there was a form submission error, repopulate form with submitted data
    if(isset($_SESSION['form_data'])) {
        $product_data = array_merge($product_data, $_SESSION['form_data']);
    }
    unset($_SESSION['form_error']);
    unset($_SESSION['form_data']);
}

?>
<div class="breadcrumbs">
    <a href="dashboard.php">แผงควบคุม</a> &raquo;
    <a href="products.php">ค้า</a> &raquo;
    <span><?php echo $is_edit_mode ? 'แก้ไขค้า' : 'เพิ่มค้าใหม่'; ?></span>
</div>

<?php if (!empty($form_error_message)): ?>
<div class="error-message" style="margin-bottom: 15px;">
    <?php echo htmlspecialchars($form_error_message); ?>
</div>
<?php endif; ?>

<form action="product_process.php" method="POST" class="admin-form" enctype="multipart/form-data">
    <input type="hidden" name="product_id" value="<?php echo $product_data['id']; ?>">
    <input type="hidden" name="action" value="<?php echo $is_edit_mode ? 'update' : 'create'; ?>">

    <div class="input-group">
        <label for="name">ชื้อค้า:</label>
        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($product_data['name']); ?>" required>
    </div>

    <div class="input-group">
        <label for="category_id">หมวดหมู่:</label>
        <select id="category_id" name="category_id" required>
            <option value="">-- เลือกหมวดหมู่ --</option>
            <?php foreach ($categories as $category): ?>
                <option value="<?php echo $category['id']; ?>" <?php echo ($product_data['category_id'] == $category['id']) ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($category['name']); ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>

    <div class="input-group">
        <label for="description">รายละเอียดค้า:</label>
        <textarea id="description" name="description" rows="5"><?php echo htmlspecialchars($product_data['description']); ?></textarea>
    </div>

    <div class="input-group">
        <label for="price">ราคา (บาท):</label>
        <input type="number" id="price" name="price" step="0.01" value="<?php echo htmlspecialchars($product_data['price']); ?>" required>
    </div>

    <div class="input-group">
        <label for="stock_quantity">จำนวนในสต็อก:</label>
        <input type="number" id="stock_quantity" name="stock_quantity" value="<?php echo htmlspecialchars($product_data['stock_quantity']); ?>" required>
    </div>

    <div class="input-group">
        <label for="sku">SKU (Stock Keeping Unit):</label>
        <input type="text" id="sku" name="sku" value="<?php echo htmlspecialchars($product_data['sku']); ?>">
    </div>

    <div class="input-group">
        <label for="image">ภาพค้า:</label>
        <input type="file" id="image" name="product_image" accept="image/jpeg, image/png, image/gif">
        <?php if ($is_edit_mode && !empty($product_data['image_url'])): ?>
            <p style="margin-top: 5px;">: <img src="../<?php echo htmlspecialchars($product_data['image_url']); ?>" alt="Product Image" style="max-height: 80px; vertical-align: middle;"> (หากป้อนใหม่ ค่จะแทนที่)</p>
        <?php endif; ?>
    </div>

    <!-- Placeholder for multiple image uploads if needed later -->

    <div class="input-group">
        <button type="submit" class="btn-primary"><?php echo $is_edit_mode ? 'การเปลี่ยนแปลง' : 'เพิ่มค้า'; ?></button>
        <a href="products.php" class="btn-secondary" style="margin-left:10px;">ยกเลิก</a>
    </div>
</form>

<?php include 'includes/footer.php'; ?>
