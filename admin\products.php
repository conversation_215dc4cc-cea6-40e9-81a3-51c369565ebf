<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ตรวจสอบการลบสินค้า
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    try {
        $product_id = $_GET['delete'];

        // ตรวจสอบว่าใช้งานในคำสั่งซื้อหรือไม่
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM order_items WHERE product_id = ?");
        $stmt->execute([$product_id]);
        $used_in_orders = $stmt->fetchColumn() > 0;

        if ($used_in_orders) {
            // ถ้าใช้งาน ให้เปลี่ยนสถานะเป็น inactive แทนการลบ
            $stmt = $pdo->prepare("UPDATE products SET status = 'inactive', updated_at = NOW() WHERE id = ?");
            $stmt->execute([$product_id]);
            $success = "เปลี่ยนสถานะสินค้าเป็น 'ไม่ใช้งาน' เนื่องจากใช้งานในคำสั่งซื้อ";
        } else {
            // ถ้าไม่ใช้งาน สามารถลบได้
            $stmt = $pdo->prepare("DELETE FROM products WHERE id = ?");
            $stmt->execute([$product_id]);
            $success = "ลบสินค้าเรียบร้อยแล้ว";
        }
    } catch (Exception $e) {
        $error = "ข้อผิดพลาด: " . $e->getMessage();
    }

    header('Location: products.php?msg=' . urlencode($success ?: $error) . '&type=' . ($success ? 'success' : 'error'));
    exit;
}

// แสดงข้อความ
if (isset($_GET['msg'])) {
    $message = $_GET['msg'];
    $message_type = $_GET['type'] ?? 'info';
    if ($message_type === 'success') {
        $success = $message;
    } else {
        $error = $message;
    }
}

// การแบ่งหน้า
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// การค้นหา
$search = isset($_GET['search']) ? $_GET['search'] : '';
$category = isset($_GET['category']) ? $_GET['category'] : '';
$status = isset($_GET['status']) ? $_GET['status'] : '';

// สร้าง WHERE clause
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(p.name LIKE ? OR p.sku LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($category)) {
    $where_conditions[] = "p.category_id = ?";
    $params[] = $category;
}

if (!empty($status)) {
    $where_conditions[] = "p.status = ?";
    $params[] = $status;
}

$where_clause = !empty($where_conditions) ? " WHERE " . implode(" AND ", $where_conditions) : "";

// ดึงข้อมูล
$categories = [];
$products = [];
$total_products = 0;
$total_pages = 1;

try {
    // ดึงข้อมูลหมวดหมู่สินค้า
    try {
        $stmt = $pdo->query("SELECT * FROM product_categories WHERE status = 'active' ORDER BY sort_order, name");
        $categories = $stmt->fetchAll();
    } catch (Exception $e) {
        // ถ้าไม่มีตาราง product_categories ลองใช้ categories
        try {
            $stmt = $pdo->query("SELECT * FROM categories ORDER BY name");
            $categories = $stmt->fetchAll();
        } catch (Exception $e) {
            $categories = [
                ['id' => 1, 'name' => 'เสื้อกีฬา'],
                ['id' => 2, 'name' => 'กางเกงกีฬา'],
                ['id' => 3, 'name' => 'ชุดทีม'],
                ['id' => 4, 'name' => 'อุปกรณ์เสริม']
            ];
        }
    }

    // นับจำนวนสินค้า
    $count_sql = "SELECT COUNT(*) FROM products p $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_products = $stmt->fetchColumn();

    $total_pages = ceil($total_products / $limit);

    // ดึงข้อมูลสินค้า
    $sql = "
        SELECT p.*, pc.name as category_name
        FROM products p
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        $where_clause
        ORDER BY p.created_at DESC
        LIMIT $limit OFFSET $offset
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll();

    // ถ้าไม่มีข้อมูล ลองใช้ตาราง categories แทน
    if (empty($products) && empty($where_clause)) {
        $sql = "
            SELECT p.*, c.name as category_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY p.created_at DESC
            LIMIT $limit OFFSET $offset
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $products = $stmt->fetchAll();
    }

} catch (Exception $e) {
    $error = "ข้อผิดพลาดในการดึงข้อมูล: " . $e->getMessage();

    // ใช้ข้อมูลจำลอง
    $products = [
        [
            'id' => 1,
            'name' => 'เสื้อกีฬา Syntex โปโล',
            'sku' => 'GT-001',
            'category_name' => 'เสื้อกีฬา',
            'price' => 210.00,
            'sale_price' => null,
            'status' => 'active',
            'image' => null,
            'created_at' => date('Y-m-d H:i:s')
        ],
        [
            'id' => 2,
            'name' => 'เสื้อกีฬา Dri-FIT',
            'sku' => 'GT-002',
            'category_name' => 'เสื้อกีฬา',
            'price' => 250.00,
            'sale_price' => 220.00,
            'status' => 'active',
            'image' => null,
            'created_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ]
    ];
    $total_products = count($products);
    $total_pages = 1;
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการสินค้า - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php" class="active"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">จัดการสินค้า</h4>
                <small class="text-muted">เพิ่ม แก้ไข และจัดการสินค้าในระบบ</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item active">จัดการสินค้า</li>
                </ol>
            </nav>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5>รายการสินค้า (<?php echo $total_products; ?> รายการ)</h5>
                <div class="btn-group">
                    <a href="products_add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>เพิ่มสินค้าใหม่
                    </a>
                    <a href="products_categories.php" class="btn btn-outline-primary">
                        <i class="fas fa-tags me-1"></i>หมวดหมู่สินค้า
                    </a>
                    <a href="products_stock.php" class="btn btn-outline-warning">
                        <i class="fas fa-warehouse me-1"></i>จัดการสต็อก
                    </a>
                </div>
            </div>

            <!-- ฟิลเตอร์และการค้นหา -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">ค้นหาและกรองสินค้า</h6>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">ค้นหา</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?php echo htmlspecialchars($search); ?>" placeholder="ชื่อสินค้า, รหัสสินค้า">
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">หมวดหมู่สินค้า</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">ทุกหมวดหมู่</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo $cat['id']; ?>" <?php echo $category == $cat['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($cat['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">สถานะ</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">ทุกสถานะ</option>
                                <option value="active" <?php echo $status == 'active' ? 'selected' : ''; ?>>ใช้งาน</option>
                                <option value="inactive" <?php echo $status == 'inactive' ? 'selected' : ''; ?>>ไม่ใช้งาน</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search me-1"></i>ค้นหา
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- ตารางสินค้า -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">รายการสินค้า</h6>
                    <span class="badge bg-primary">พบ <?php echo $total_products; ?> รายการ</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="80">ภาพ</th>
                                    <th>ชื่อสินค้า</th>
                                    <th>หมวดหมู่</th>
                                    <th>ราคา</th>
                                    <th>สถานะ</th>
                                    <th width="150">จัดการ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($products)): ?>
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <i class="fas fa-box fa-2x text-muted mb-2"></i>
                                        <p class="text-muted mb-0">ไม่พบข้อมูลสินค้า</p>
                                        <a href="products_add.php" class="btn btn-primary btn-sm mt-2">
                                            <i class="fas fa-plus me-1"></i>เพิ่มสินค้าแรก
                                        </a>
                                    </td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($products as $product): ?>
                                    <tr>
                                        <td class="text-center">
                                            <?php if (!empty($product['image'])): ?>
                                            <img src="../uploads/products/<?php echo $product['image']; ?>"
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                 class="img-thumbnail" width="50" height="50">
                                            <?php else: ?>
                                            <div class="bg-light d-flex align-items-center justify-content-center"
                                                 style="width: 50px; height: 50px; border-radius: 8px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                            <?php if (!empty($product['sku'])): ?>
                                            <br><small class="text-muted">SKU: <?php echo htmlspecialchars($product['sku']); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($product['category_name'] ?? 'ไม่ระบุ'); ?></td>
                                        <td>
                                            <strong>฿<?php echo number_format($product['price'], 2); ?></strong>
                                            <?php if (!empty($product['sale_price'])): ?>
                                            <br><small class="text-danger">ลดเหลือ ฿<?php echo number_format($product['sale_price'], 2); ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($product['status'] == 'active'): ?>
                                            <span class="badge bg-success">ใช้งาน</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">ไม่ใช้งาน</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="products_add.php?edit=<?php echo $product['id']; ?>"
                                                   class="btn btn-outline-primary" title="แก้ไข">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-outline-danger delete-product"
                                                        data-id="<?php echo $product['id']; ?>"
                                                        data-name="<?php echo htmlspecialchars($product['name']); ?>"
                                                        title="ลบ">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- การแบ่งหน้า -->
                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page-1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            <?php endif; ?>

                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            if ($start_page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=1&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>">1</a>
                                </li>
                                <?php if ($start_page > 2): ?>
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($end_page < $total_pages): ?>
                                <?php if ($end_page < $total_pages - 1): ?>
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                <?php endif; ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $total_pages; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>"><?php echo $total_pages; ?></a>
                                </li>
                            <?php endif; ?>

                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page+1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo $category; ?>&status=<?php echo $status; ?>">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // การลบสินค้า
    document.querySelectorAll('.delete-product').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');

            Swal.fire({
                title: 'ยืนยันการลบ?',
                html: `ต้องการลบสินค้า <strong>${name}</strong> หรือไม่?`,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'ใช่, ลบเลย!',
                cancelButtonText: 'ยกเลิก'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = `products.php?delete=${id}`;
                }
            });
        });
    });
    </script>
</body>
</html>
