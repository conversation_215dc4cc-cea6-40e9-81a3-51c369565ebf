<?php
// แสดงข้อความแสดงสถานะ
$message = '';
$error = '';
$success = '';

// ถ้ากดปุ่มตั้ง
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    // ใช้ข้อมูลจากฟอร์มหรือค่าเริ่มต้น
    $db_host = $_POST['db_host'] ?: $db_host;
    $db_name = $_POST['db_name'] ?: $db_name;
    $db_user = $_POST['db_user'] ?: $db_user;
    $db_pass = $_POST['db_pass'] ?: $db_pass;
    
    try {
        // 1. ทดสอบการเชื่อมต่อ
        $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 2. สร้างฐานข้อมูลถ้าไม่
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // 3. เชื่อมต่อกับฐานข้อมูลที่สร้าง
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 4. สร้างโฟลเดอร์จำเป็น
        $directories = [
            'uploads',
            'uploads/products',
            'uploads/gallery',
            'uploads/designs',
            'uploads/profiles',
            'uploads/temp',
            'config',
            'admin',
            'includes',
            'assets',
            'assets/images',
            'assets/css',
            'assets/js',
            'assets/video'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            
            // สร้างไฟล์ index.php ป้องกันไม่ให้สามารถเข้าได้ในโฟลเดอร์
            $index_file = "$dir/index.php";
            if (!file_exists($index_file)) {
                $content = "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n";
                file_put_contents($index_file, $content);
            }
        }
        
        // 5. สร้างไฟล์ config/database.php
        $config_content = "<?php
/**
 * GT-SportDesign Database Configuration
 * Auto-generated on " . date('Y-m-d H:i:s') . "
 */

function getDbConnection() {
    \$host = '$db_host';
    \$dbname = '$db_name';
    \$username = '$db_user';
    \$password = '$db_pass';
    
    try {
        \$pdo = new PDO(\"mysql:host=\$host;dbname=\$dbname;charset=utf8mb4\", \$username, \$password);
        \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        \$pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return \$pdo;
    } catch (PDOException \$e) {
        die('Database connection failed: ' . \$e->getMessage());
    }
}

// Global database connection
function getDB() {
    return getDbConnection();
}

// Backward compatibility
\$pdo = getDbConnection();
";
        file_put_contents('config/database.php', $config_content);
        
        // 6. สร้างตารางฐานข้อมูล
        $sql = "
        -- สร้างตาราง admins
        CREATE TABLE IF NOT EXISTS `admins` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `username` varchar(50) NOT NULL,
          `password` varchar(255) NOT NULL,
          `email` varchar(100) NOT NULL,
          `fullname` varchar(100) NOT NULL,
          `role` enum('admin','manager','staff') NOT NULL DEFAULT 'staff',
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `last_login` datetime DEFAULT NULL,
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `username` (`username`),
          UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง users
        CREATE TABLE IF NOT EXISTS `users` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `username` varchar(50) NOT NULL,
          `password` varchar(255) NOT NULL,
          `email` varchar(100) NOT NULL,
          `fullname` varchar(100) NOT NULL,
          `phone` varchar(20) DEFAULT NULL,
          `address` text DEFAULT NULL,
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `last_login` datetime DEFAULT NULL,
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `username` (`username`),
          UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง product_categories
        CREATE TABLE IF NOT EXISTS `product_categories` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL,
          `description` text DEFAULT NULL,
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง products
        CREATE TABLE IF NOT EXISTS `products` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `category_id` int(11) DEFAULT NULL,
          `name` varchar(100) NOT NULL,
          `description` text DEFAULT NULL,
          `price` decimal(10,2) NOT NULL,
          `stock` int(11) DEFAULT 0,
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `category_id` (`category_id`),
          CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง product_images
        CREATE TABLE IF NOT EXISTS `product_images` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `product_id` int(11) NOT NULL,
          `file_id` varchar(100) NOT NULL,
          `image_type` enum('main','gallery') NOT NULL DEFAULT 'gallery',
          `created_at` datetime NOT NULL,
          PRIMARY KEY (`id`),
          KEY `product_id` (`product_id`),
          CONSTRAINT `product_images_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง designs
        CREATE TABLE IF NOT EXISTS `designs` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `customer_id` int(11) DEFAULT NULL,
          `product_id` int(11) DEFAULT NULL,
          `design_data` longtext NOT NULL,
          `base_price` decimal(10,2) NOT NULL,
          `status` enum('draft','pending','approved','rejected','ordered') NOT NULL DEFAULT 'draft',
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `customer_id` (`customer_id`),
          KEY `product_id` (`product_id`),
          CONSTRAINT `designs_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
          CONSTRAINT `designs_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง orders
        CREATE TABLE IF NOT EXISTS `orders` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `order_number` varchar(20) NOT NULL,
          `customer_id` int(11) DEFAULT NULL,
          `design_id` int(11) DEFAULT NULL,
          `total_amount` decimal(10,2) NOT NULL,
          `status` enum('pending','paid','processing','shipped','completed','cancelled') NOT NULL DEFAULT 'pending',
          `payment_method` enum('bank_transfer','promptpay','credit_card','cash') NOT NULL,
          `payment_status` enum('pending','paid','refunded') NOT NULL DEFAULT 'pending',
          `shipping_address` text DEFAULT NULL,
          `shipping_method` varchar(50) DEFAULT NULL,
          `shipping_cost` decimal(10,2) DEFAULT 0.00,
          `notes` text DEFAULT NULL,
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `order_number` (`order_number`),
          KEY `customer_id` (`customer_id`),
          KEY `design_id` (`design_id`),
          CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
          CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`design_id`) REFERENCES `designs` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง system_settings
        CREATE TABLE IF NOT EXISTS `system_settings` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `setting_key` varchar(50) NOT NULL,
          `setting_value` text NOT NULL,
          `setting_type` enum('text','number','boolean','json','html') NOT NULL DEFAULT 'text',
          `description` varchar(255) DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        
        $statements = explode(';', $sql);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // 7. สร้างข้อมูลเริ่มต้น
        $pdo->exec("
            INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
            ('site_name', 'GT-SportDesign', 'text', 'ชื่อเว็บไซต์'),
            ('site_description', 'เชี่ยวชาญด้านการออกแบบและเสื้อ', 'text', 'คำอธิบายเว็บไซต์'),
            ('contact_email', '<EMAIL>', 'text', 'อีเมลติดต่อ'),
            ('contact_phone', '************', 'text', 'เบอร์โทรศัพท์'),
            ('contact_line', '@gtsport', 'text', 'LINE ID'),
            ('facebook_page', 'https://www.facebook.com/GTSportDesign.1', 'text', 'Facebook Page'),
            ('min_order_amount', '500', 'number', 'ยอดสั่งซื้อขั้นต่ำ'),
            ('shipping_cost', '50', 'number', 'ค่าส่งมาตรฐาน'),
            ('free_shipping_amount', '1000', 'number', 'ยอดสั่งซื้อขั้นต่ำสำหรับการส่งฟรี'),
            ('promptpay_id', '0855599164', 'text', 'เบอร์ PromptPay'),
            ('primary_color', '#ee501b', 'text', 'สีหลักของเว็บไซต์'),
            ('secondary_color', '#ff6b35', 'text', 'สีรองของเว็บไซต์')
        ");
        
        // 8. สร้างผู้ดูแลระบบเริ่มต้น
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("
            INSERT INTO admins (username, password, email, fullname, role, status, created_at)
            VALUES ('admin', '$admin_password', '<EMAIL>', 'ผู้ดูแลระบบ', 'admin', 'active', NOW())
        ");
        
        // 9. สร้างไฟล์ admin/login.php
        $admin_login_content = '<?php
session_start();
require_once "../config/database.php";

$error = "";

// ถ้าส่งฟอร์มล็อกอิน
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST["username"] ?? "";
    $password = $_POST["password"] ?? "";
    
    if (empty($username) || empty($password)) {
        $error = "กรอกชื่อผู้ใช้และรหัสผ่าน";
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = \'active\'");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();
            
            if ($admin && password_verify($password, $admin["password"])) {
                // ล็อกอินสำเร็จ
                $_SESSION["admin_id"] = $admin["id"];
                $_SESSION["admin_username"] = $admin["username"];
                $_SESSION["admin_fullname"] = $admin["fullname"];
                $_SESSION["admin_role"] = $admin["role"];
                $_SESSION["admin_logged_in"] = true;
                
                // บันทึกเวลาล็อกอินล่าสุด
                $update = $pdo->prepare("UPDATE admins SET last_login = NOW() WHERE id = ?");
                $update->execute([$admin["id"]]);
                
                // ไปหน้าแดชบอร์ด
                header("Location: dashboard.php");
                exit;
            } else {
                $error = "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง";
            }
        } catch (PDOException $e) {
            $error = "ข้อผิดพลาด: " . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: \'Kanit\', sans-serif;
            background: linear-gradient(135deg, #eb4e17 0%, #ff6b35 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
            max-width: 450px;
        }
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-logo img {
            max-width: 150px;
        }
        .login-title {
            text-align: center;
            font-weight: 600;
            margin-bottom: 30px;
            color: #333;
        }
        .form-control {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .btn-login {
            background: linear-gradient(135deg, #eb4e17 0%, #ff6b35 100%);
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-weight: 500;
            width: 100%;
            margin-top: 10px;
        }
        .btn-login:hover {
            background: linear-gradient(135deg, #d04416 0%, #e55f2e 100%);
        }
        .login-footer {
            text-align: center;
            margin-top: 30px;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-logo">
            <img src="../assets/images/Gt_Logo.png" alt="GT Sport Design">
        </div>
        <h4 class="login-title">เข้าสู่ระบบ</h4>
        
        <?php if ($error): ?>
            <div class="alert alert-danger"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <form method="post" action="">
            <div class="mb-3">
                <label for="username" class="form-label">ชื่อผู้ใช้</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                    <input type="text" class="form-control" id="username" name="username" placeholder="ชื่อผู้ใช้" required>
                </div>
            </div>
            <div class="mb-3">
                <label for="password" class="form-label">รหัสผ่าน</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                    <input type="password" class="form-control" id="password" name="password" placeholder="รหัสผ่าน" required>
                </div>
            </div>
            <button type="submit" class="btn btn-primary btn-login">เข้าสู่ระบบ</button>
        </form>
        
        <div class="login-footer">
            &copy; <?php echo date("Y"); ?> GT Sport Design. All rights reserved.
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
        file_put_contents('admin/login.php', $admin_login_content);
        
        // 10. สร้างไฟล์ index.php
        $index_content = '<?php
require_once \'config/database.php\';

// ข้อมูลการตั้งค่าเว็บไซต์
$settings = [];
try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
    while ($row = $stmt->fetch()) {
        $settings[$row[\'setting_key\']] = $row[\'setting_value\'];
    }
} catch (Exception $e) {
    // fallback หากไม่มีข้อมูล
    $settings = [
        \'site_name\' => \'GT Sport Design\',
        \'contact_phone\' => \'************\',
        \'contact_line\' => \'@gtsport\',
        \'facebook_page\' => \'https://www.facebook.com/GTSportDesign.1\'
    ];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($settings[\'site_name\'] ?? \'GT Sport Design\'); ?> | เอ้อ เอื้อ ออกแบบเอง</title>
    <meta name="description" content="เสื้อ เอื้อ ออกแบบตามภาพ ราคาโรงงาน ส่งไทย - GT Sport Design">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #ee501b;
            --secondary-color: #ff6b35;
        }
        body {
            font-family: \'Kanit\', sans-serif;
            line-height: 1.6;
        }
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 15px 0;
        }
        .navbar-brand img {
            height: 40px;
        }
        .navbar-nav .nav-link {
            color: #333;
            font-weight: 500;
            padding: 10px 15px;
        }
        .navbar-nav .nav-link:hover {
            color: var(--primary-color);
        }
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        .hero-section {
            position: relative;
            height: 80vh;
            overflow: hidden;
        }
        .video-desktop, .video-mobile {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .video-mobile {
            display: none;
        }
        .hero-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: white;
            z-index: 1;
        }
        .hero-content img {
            max-width: 200px;
            margin-bottom: 20px;
        }
        .hero-subtitle {
            font-size: 24px;
            margin-bottom: 30px;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
        }
        .hero-btn {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 12px 30px;
            border-radius: 30px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s;
        }
        .hero-btn:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .features-section {
            padding: 80px 0;
            background-color: #f8f9fa;
        }
        .feature-card {
            text-align: center;
            padding: 30px 15px;
            border-radius: 10px;
            background-color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: all 0.3s;
            height: 100%;
        }
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        .feature-icon {
            font-size: 40px;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
        .feature-title {
            font-weight: 600;
            margin-bottom: 15px;
        }
        .footer {
            background-color: #333;
            color: white;
            padding: 60px 0 30px;
        }
        .footer-logo {
            max-width: 150px;
            margin-bottom: 20px;
        }
        .footer-contact {
            margin-bottom: 30px;
        }
        .footer-contact i {
            margin-right: 10px;
            color: var(--primary-color);
        }
        .footer-links h5 {
            font-weight: 600;
            margin-bottom: 20px;
            color: white;
        }
        .footer-links ul {
            list-style: none;
            padding: 0;
        }
        .footer-links li {
            margin-bottom: 10px;
        }
        .footer-links a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
        }
        .footer-links a:hover {
            color: var(--primary-color);
        }
        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.1);
            padding-top: 20px;
            margin-top: 40px;
            text-align: center;
            font-size: 14px;
            color: rgba(255,255,255,0.6);
        }
        @media (max-width: 768px) {
            .video-desktop {
                display: none;
            }
            .video-mobile {
                display: block;
            }
            .hero-content img {
                max-width: 150px;
            }
            .hero-subtitle {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="assets/images/Gt_Logo.png" alt="GT Sport Design">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">ค้า</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="gallery.php">ผลงาน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="design.php">ออกแบบเสื้อ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">ต่อเรา</a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary ms-lg-3" href="design.php">
                            <i class="fas fa-tshirt me-1"></i> ออกแบบเลย
                        </a>
                    </li>
                
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <!-- Desktop -->
        <video autoplay muted loop playsinline class="video-desktop">
            <source src="assets/video/gt-banner.mp4" type="video/mp4">
        </video>

        <!-- Mobile -->
        <video autoplay muted loop playsinline class="video-mobile">
            <source src="assets/video/gt-mobile.mp4" type="video/mp4">
        </video>

        <!-- เนื้อหา -->
        <div class="hero-content">
            <img src="assets/images/Gt_Logo_white.png" alt="GT Logo">
            <p class="hero-subtitle">ออกแบบและเสื้อ</p>
            <a href="design.php" class="hero-btn">เริ่มออกแบบเลย</a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
        <div class="container">
            <div class="row mb-5">
                <div class="col-md-12 text-center">
                    <h2 class="mb-4">ของเรา</h2>
                    <p class="lead">ครบวงจรด้านเสื้อและเสื้อ</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-tshirt"></i>
                        </div>
                        <h4 class="feature-title">เสื้อ</h4>
                        <p>เสื้อประเภท เอ้อ บาสเกตบอล วอลเลย์บอล และอื่นๆ ภาพ ราคาโรงงาน</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <h4 class="feature-title">ออกแบบเสื้อ</h4>
                        <p>ออกแบบเสื้อตามความต้องการ ด้วยเครื่องมือออกแบบออนไลน์ ให้เราช่วยออกแบบให้</p>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-print"></i>
                        </div>
                        <h4 class="feature-title">สีและปัก</h4>
                        <p>สีและปักโลโก้ ชื่อ เบอร์ ด้วย คอมพิวเตอร์ คม สดทน ไม่ลอก ไม่แตก</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <img src="assets/images/Gt_Logo_white.png" alt="GT Sport Design" class="footer-logo">
                    <div class="footer-contact">
                        <p><i class="fas fa-phone"></i> <?php echo htmlspecialchars($settings[\'contact_phone\'] ?? \'************\'); ?></p>
                        <p><i class="fab fa-line"></i> <?php echo htmlspecialchars($settings[\'contact_line\'] ?? \'@gtsport\'); ?></p>
                        <p><i class="fas fa-map-marker-alt"></i> 339/7 ม.4 ต.บ้าน  อ.เมือง จ.สุราษฎร์ธานี</p>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 mb-4">
                    <div class="footer-links">
                        <h5>เมนูหลัก</h5>
                        <ul>
                            <li><a href="index.php">หน้าแรก</a></li>
                            <li><a href="products.php">ค้า</a></li>
                            <li><a href="gallery.php">ผลงาน</a></li>
                            <li><a href="design.php">ออกแบบเสื้อ</a></li>
                            <li><a href="contact.php">ต่อเรา</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 mb-4">
                    <div class="footer-links">
                        <h5>ประเภทเสื้อ</h5>
                        <ul>
                            <li><a href="#">เสื้อ</a></li>
                            <li><a href="#">เสื้อบาสเกตบอล</a></li>
                            <li><a href="#">เสื้อวอลเลย์บอล</a></li>
                            <li><a href="#">เสื้อโปโล</a></li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-4 mb-4">
                    <div class="footer-links">
                        <h5>เกี่ยวกับเรา</h5>
                        <div class="social-links">
                            <a href="<?php echo htmlspecialchars($settings[\'facebook_page\'] ?? \'https://www.facebook.com/GTSportDesign.1\'); ?>" class="btn btn-outline-light me-2" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-light me-2">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="#" class="btn btn-outline-light me-2">
                                <i class="fab fa-line"></i>
                            </a>
                            <a href="#" class="btn btn-outline-light">
                                <i class="fab fa-tiktok"></i>
                            </a>
                        </div>
                        <div class="mt-4">
                            <h5>ข่าวสารและโปรโมชั่น</h5>
                            <form class="mt-3">
                                <div class="input-group">
                                    <input type="email" class="form-control" placeholder="อีเมล">
                                    <button class="btn btn-primary" type="submit">สมัคร</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <?php echo date("Y"); ?> GT Sport Design. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
        file_put_contents('index.php', $index_content);
        
        // 11. สร้างไฟล์ .htaccess
        $htaccess_content = '# GT-SportDesign Security Configuration
# Version: 1.0

# ป้องกันการเข้าไฟล์
<FilesMatch "^(database\.php|config\.php|\.env|\.git|\.htaccess)">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้องกันการแสดงรายการไฟล์
Options -Indexes

# ป้องกันการเข้าไฟล์ SQL
<FilesMatch "\.(sql|bak|config|dist|fla|inc|ini|log|psd|sh|swp)$">
  Order Allow,Deny
  Deny from all
</FilesMatch>

# ป้องกันการเข้าโฟลเดอร์ config
<IfModule mod_rewrite.c>
  RewriteEngine On
  RewriteRule ^config/.*$ - [F,L]
  RewriteRule ^database/.*$ - [F,L]
</IfModule>

# ป้องกันด้วย HTTP headers
<IfModule mod_headers.c>
  Header set X-Content-Type-Options "nosniff"
  Header set X-XSS-Protection "1; mode=block"
  Header set X-Frame-Options "SAMEORIGIN"
  Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
';
        file_put_contents('.htaccess', $htaccess_content);
        
        // 12. สร้างไฟล์ admin/dashboard.php
        $admin_dashboard_content = '<?php
session_start();
require_once "../config/database.php";

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION["admin_logged_in"]) || $_SESSION["admin_logged_in"] !== true) {
    header("Location: login.php");
    exit;
}

$admin_name = $_SESSION["admin_fullname"] ?? $_SESSION["admin_username"] ?? "Admin";
$admin_role = $_SESSION["admin_role"] ?? "admin";

// ข้อมูล
try {
    // จำนวนคำสั่งซื้อวันนี้
    $today = date("Y-m-d");
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE DATE(created_at) = ?");
    $stmt->execute([$today]);
    $orders_today = $stmt->fetchColumn();
    
    // ยอดขายวันนี้
    $stmt = $pdo->prepare("SELECT SUM(total_amount) FROM orders WHERE DATE(created_at) = ?");
    $stmt->execute([$today]);
    $sales_today = $stmt->fetchColumn() ?: 0;
    
    // จำนวนลูกค้าใหม่วันนี้
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE DATE(created_at) = ?");
    $stmt->execute([$today]);
    $new_customers = $stmt->fetchColumn();
    
    // คำสั่งซื้อล่าสุด
    $stmt = $pdo->prepare("
        SELECT o.id, o.order_number, o.total_amount, o.status, o.created_at, 
               u.fullname as customer_name
        FROM orders o
        LEFT JOIN users u ON o.customer_id = u.id
        ORDER BY o.created_at DESC
        LIMIT 5
    ");
    $stmt->execute();
    $recent_orders = $stmt->fetchAll();
    
} catch (PDOException $e) {
    // ข้อผิดพลาด ให้ใช้ข้อมูลอย่าง
    $orders_today = 0;
    $sales_today = 0;
    $new_customers = 0;
    $recent_orders = [];
}

// แปลงสถานะเป็นภาษาไทย
function getStatusThai($status) {
    $status_map = [
        "pending" => "รอดำเนินการ",
        "paid" => "ชำระแล้ว",
        "processing" => "กำลังดำเนินการ",
        "shipped" => "ส่งแล้ว",
        "completed" => "เสร็จสิ้น",
        "cancelled" => "ยกเลิก"
    ];
    return $status_map[$status] ?? $status;
}

// แปลงสถานะเป็น Bootstrap
function getStatusClass($status) {
    $class_map = [
        "pending" => "warning",
        "paid" => "info",
        "processing" => "primary",
        "shipped" => "secondary",
        "completed" => "success",
        "cancelled" => "danger"
    ];
    return $class_map[$status] ?? "secondary";
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แดชบอร์ด - GT Sport Design Admin</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: "Kanit", sans-serif;
            background-color: #f8f9fa;
        }
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background: linear-gradient(135deg, #eb4e17 0%, #ff6b35 100%);
            color: white;
            padding-top: 20px;
            z-index: 1000;
        }
        .sidebar-logo {
            text-align: center;
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }
        .sidebar-logo img {
            max-width: 120px;
        }
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
        }
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.1);
            color: white;
            border-left-color: white;
        }
        .sidebar-menu i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 15px 20px;
            margin-bottom: 20px;
            border-radius: 10px;
        }
        .navbar-brand {
            font-weight: 600;
            color: #333;
        }
        .navbar-user {
            display: flex;
            align-items: center;
        }
        .navbar-user img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .navbar-user-info {
            line-height: 1.2;
        }
        .navbar-user-name {
            font-weight: 500;
            color: #333;
        }
        .navbar-user-role {
            font-size: 12px;
            color: #6c757d;
        }
        .dashboard-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .dashboard-card-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 15px;
            color: white;
        }
        .dashboard-card-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        .dashboard-card-value {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 5px;
        }
        .dashboard-card-subtitle {
            font-size: 14px;
            color: #6c757d;
        }
        .bg-primary-gradient {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        .bg-success-gradient {
            background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
        }
        .bg-warning-gradient {
            background: linear-gradient(135deg, #ffc107 0%, #d39e00 100%);
        }
        .bg-danger-gradient {
            background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
        }
        .bg-info-gradient {
            background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
        }
        .bg-orange-gradient {
            background: linear-gradient(135deg, #eb4e17 0%, #ff6b35 100%);
        }
        .recent-orders {
            margin-top: 30px;
        }
        .recent-orders-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .recent-orders-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }
        .badge-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-logo">
            <img src="../assets/images/Gt_Logo_white.png" alt="GT Sport Design">
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php" class="active"><i class="fas fa-tachometer-alt"></i> แดชบอร์ด</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i> คำสั่งซื้อ</a></li>
            <li><a href="products.php"><i class="fas fa-tshirt"></i> ค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-palette"></i> งานออกแบบ</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i> แกลเลอรี่</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i> ค้า</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i> รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i> ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> ออกจากระบบ</a></li>
        </ul>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <!-- Navbar -->
        <div class="navbar">
            <div class="navbar-brand">
                <i class="fas fa-tachometer-alt me-2"></i> แดชบอร์ด
            </div>
            <div class="navbar-user">
                <img src="../assets/images/admin-avatar.png" alt="Admin">
                <div class="navbar-user-info">
                    <div class="navbar-user-name"><?php echo htmlspecialchars($admin_name); ?></div>
                    <div class="navbar-user-role"><?php echo htmlspecialchars($admin_role); ?></div>
                </div>
            </div>
        </div>
        
        <!-- Dashboard Cards -->
        <div class="row">
            <div class="col-md-4 col-lg-3">
                <div class="dashboard-card">
                    <div class="dashboard-card-icon bg-orange-gradient">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="dashboard-card-title">คำสั่งซื้อวันนี้</div>
                    <div class="dashboard-card-value"><?php echo $orders_today; ?></div>
                    <div class="dashboard-card-subtitle">ยอดรวม</div>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="dashboard-card">
                    <div class="dashboard-card-icon bg-success-gradient">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="dashboard-card-title">รายได้วันนี้</div>
                    <div class="dashboard-card-value">฿<?php echo number_format($sales_today, 2); ?></div>
                    <div class="dashboard-card-subtitle">ยอดขายรวม</div>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="dashboard-card">
                    <div class="dashboard-card-icon bg-primary-gradient">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="dashboard-card-title">ลูกค้าใหม่</div>
                    <div class="dashboard-card-value"><?php echo $new_customers; ?></div>
                    <div class="dashboard-card-subtitle">วันนี้</div>
                </div>
            </div>
            <div class="col-md-4 col-lg-3">
                <div class="dashboard-card">
                    <div class="dashboard-card-icon bg-warning-gradient">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="dashboard-card-title">งานออกแบบ</div>
                    <div class="dashboard-card-value">0</div>
                    <div class="dashboard-card-subtitle">รอดำเนินการ</div>
                </div>
            </div>
        </div>
        
        <!-- Recent Orders -->
        <div class="recent-orders">
            <div class="recent-orders-header">
                <div class="recent-orders-title">คำสั่งซื้อล่าสุด</div>
                <a href="orders.php" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
            </div>
            <div class="dashboard-card">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>เลขที่คำสั่ง</th>
                                <th>ลูกค้า</th>
                                <th>ราคา</th>
                                <th>วันที่</th>
                                <th>สถานะ</th>
                                <th>รายละเอียด</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recent_orders)): ?>
                                <tr>
                                    <td colspan="6" class="text-center">ไม่มีคำสั่งซื้อล่าสุด</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($order["order_number"]); ?></td>
                                        <td><?php echo htmlspecialchars($order["customer_name"] ?? "ไม่พบข้อมูล"); ?></td>
                                        <td>฿<?php echo number_format($order["total_amount"], 2); ?></td>
                                        <td><?php echo date("d/m/Y", strtotime($order["created_at"])); ?></td>
                                        <td>
                                            <span class="badge bg-<?php echo getStatusClass($order["status"]); ?> badge-status">
                                                <?php echo getStatusThai($order["status"]); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="order_detail.php?id=<?php echo $order["id"]; ?>" class="btn btn-sm btn-outline-primary">ดูรายละเอียด</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
        file_put_contents('admin/dashboard.php', $admin_dashboard_content);
        
        // สร้างไฟล์ success.html
        $success_content = '<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตั้งสำเร็จ - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: "Kanit", sans-serif;
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .success-container {
            max-width: 700px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
        }
        .success-icon {
            font-size: 80px;
            color: #28a745;
            margin-bottom: 20px;
        }
        .success-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        .success-message {
            font-size: 18px;
            margin-bottom: 30px;
            color: #6c757d;
        }
        .login-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        .login-info h5 {
            margin-bottom: 15px;
            color: #333;
        }
        .next-steps {
            text-align: left;
            margin-bottom: 30px;
        }
        .next-steps h5 {
            margin-bottom: 15px;
            color: #333;
        }
        .next-steps ol {
            padding-left: 20px;
        }
        .next-steps li {
            margin-bottom: 10px;
        }
        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
            padding: 10px 30px;
            font-weight: 500;
        }
        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-container">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1 class="success-title">ตั้งระบบสำเร็จ!</h1>
            <p class="success-message">ระบบ GT Sport Design ได้ตั้งค่าเรียบร้อยแล้ว สามารถเริ่มใช้งานได้</p>
            
            <div class="login-info">
                <h5>ข้อมูลการเข้าสู่ระบบ</h5>
                <p>ชื่อผู้ใช้: admin</p>
                <p>รหัสผ่าน: admin123</p>
                <p>กรุณาเปลี่ยนรหัสผ่านหลังจากเข้าสู่ระบบครั้งแรก</p>
            </div>
            
            <div class="next-steps">
                <h5>ขั้นตอนถัดไป</h5>
                <ol>
                    <li>เข้าสู่ระบบด้วยชื่อผู้ใช้ "admin" และรหัสผ่าน "admin123"</li>
                    <li>เปลี่ยนรหัสผ่านในหน้าต่าง "ตั้งค่า" ภายใต้เมนู "แดชบอร์ด"</li>
                    <li>เริ่มต้นใช้งานระบบ GT Sport Design</li>
                </ol>
            </div>
            
            <a href="admin/login.php" class="btn btn-success">เข้าสู่ระบบ</a>
        </div>
    </div>
</body>
</html>';
        file_put_contents('success.html', $success_content);
        
        // สร้างไฟล์ error.html
        $error_content = '<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เกิดข้อผิดพลาด - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: "Kanit", sans-serif;
            background-color: #f8f9fa;
            padding: 50px 0;
        }
        .error-container {
            max-width: 700px;
            margin: 0 auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
        }
        .error-icon {
            font-size: 80px;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }
        .error-message {
            font-size: 18px;
            margin-bottom: 30px;
            color: #6c757d;
        }
        .btn-error {
            background-color: #dc3545;
            border-color: #dc3545;
            padding: 10px 30px;
            font-weight: 500;
        }
        .btn-error:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h1 class="error-title">เกิดข้อผิดพลาด</h1>
            <p class="error-message">เกิดข้อผิดพลาดในการตั้งค่าระบบ GT Sport Design</p>
            <p class="error-message">กรุณาตรวจสอบข้อมูลฐานข้อมูลและลองใหม่อีกครั้ง</p>
            <a href="install.php" class="btn btn-error">ลองใหม่</a>
        </div>
    </div>
</body>
</html>';
        file_put_contents('error.html', $error_content);
        
        // สร้างไฟล์ install.php
        $install_content = '<?php
// แสดงข้อความแสดงสถานะ
$message = '';
$error = '';
$success = '';

// ถ้ากดปุ่มตั้ง
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['install'])) {
    // ใช้ข้อมูลจากฟอร์มหรือค่าเริ่มต้น
    $db_host = $_POST['db_host'] ?: $db_host;
    $db_name = $_POST['db_name'] ?: $db_name;
    $db_user = $_POST['db_user'] ?: $db_user;
    $db_pass = $_POST['db_pass'] ?: $db_pass;
    
    try {
        // 1. ทดสอบการเชื่อมต่อ
        $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 2. สร้างฐานข้อมูลถ้าไม่
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // 3. เชื่อมต่อกับฐานข้อมูลที่สร้าง
        $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8mb4", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 4. สร้างโฟลเดอร์จำเป็น
        $directories = [
            'uploads',
            'uploads/products',
            'uploads/gallery',
            'uploads/designs',
            'uploads/profiles',
            'uploads/temp',
            'config',
            'admin',
            'includes',
            'assets',
            'assets/images',
            'assets/css',
            'assets/js',
            'assets/video'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
            
            // สร้างไฟล์ index.php ป้องกันไม่ให้สามารถเข้าได้ในโฟลเดอร์
            $index_file = "$dir/index.php";
            if (!file_exists($index_file)) {
                $content = "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n";
                file_put_contents($index_file, $content);
            }
        }
        
        // 5. สร้างไฟล์ config/database.php
        $config_content = "<?php
/**
 * GT-SportDesign Database Configuration
 * Auto-generated on " . date('Y-m-d H:i:s') . "
 */

function getDbConnection() {
    \$host = '$db_host';
    \$dbname = '$db_name';
    \$username = '$db_user';
    \$password = '$db_pass';
    
    try {
        \$pdo = new PDO(\"mysql:host=\$host;dbname=\$dbname;charset=utf8mb4\", \$username, \$password);
        \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        \$pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return \$pdo;
    } catch (PDOException \$e) {
        die('Database connection failed: ' . \$e->getMessage());
    }
}

// Global database connection
function getDB() {
    return getDbConnection();
}

// Backward compatibility
\$pdo = getDbConnection();
";
        file_put_contents('config/database.php', $config_content);
        
        // 6. สร้างตารางฐานข้อมูล
        $sql = "
        -- สร้างตาราง admins
        CREATE TABLE IF NOT EXISTS `admins` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `username` varchar(50) NOT NULL,
          `password` varchar(255) NOT NULL,
          `email` varchar(100) NOT NULL,
          `fullname` varchar(100) NOT NULL,
          `role` enum('admin','manager','staff') NOT NULL DEFAULT 'staff',
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `last_login` datetime DEFAULT NULL,
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `username` (`username`),
          UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง users
        CREATE TABLE IF NOT EXISTS `users` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `username` varchar(50) NOT NULL,
          `password` varchar(255) NOT NULL,
          `email` varchar(100) NOT NULL,
          `fullname` varchar(100) NOT NULL,
          `phone` varchar(20) DEFAULT NULL,
          `address` text DEFAULT NULL,
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `last_login` datetime DEFAULT NULL,
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `username` (`username`),
          UNIQUE KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง product_categories
        CREATE TABLE IF NOT EXISTS `product_categories` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `name` varchar(100) NOT NULL,
          `description` text DEFAULT NULL,
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง products
        CREATE TABLE IF NOT EXISTS `products` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `category_id` int(11) DEFAULT NULL,
          `name` varchar(100) NOT NULL,
          `description` text DEFAULT NULL,
          `price` decimal(10,2) NOT NULL,
          `stock` int(11) DEFAULT 0,
          `status` enum('active','inactive') NOT NULL DEFAULT 'active',
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `category_id` (`category_id`),
          CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง product_images
        CREATE TABLE IF NOT EXISTS `product_images` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `product_id` int(11) NOT NULL,
          `file_id` varchar(100) NOT NULL,
          `image_type` enum('main','gallery') NOT NULL DEFAULT 'gallery',
          `created_at` datetime NOT NULL,
          PRIMARY KEY (`id`),
          KEY `product_id` (`product_id`),
          CONSTRAINT `product_images_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง designs
        CREATE TABLE IF NOT EXISTS `designs` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `customer_id` int(11) DEFAULT NULL,
          `product_id` int(11) DEFAULT NULL,
          `design_data` longtext NOT NULL,
          `base_price` decimal(10,2) NOT NULL,
          `status` enum('draft','pending','approved','rejected','ordered') NOT NULL DEFAULT 'draft',
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          KEY `customer_id` (`customer_id`),
          KEY `product_id` (`product_id`),
          CONSTRAINT `designs_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
          CONSTRAINT `designs_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง orders
        CREATE TABLE IF NOT EXISTS `orders` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `order_number` varchar(20) NOT NULL,
          `customer_id` int(11) DEFAULT NULL,
          `design_id` int(11) DEFAULT NULL,
          `total_amount` decimal(10,2) NOT NULL,
          `status` enum('pending','paid','processing','shipped','completed','cancelled') NOT NULL DEFAULT 'pending',
          `payment_method` enum('bank_transfer','promptpay','credit_card','cash') NOT NULL,
          `payment_status` enum('pending','paid','refunded') NOT NULL DEFAULT 'pending',
          `shipping_address` text DEFAULT NULL,
          `shipping_method` varchar(50) DEFAULT NULL,
          `shipping_cost` decimal(10,2) DEFAULT 0.00,
          `notes` text DEFAULT NULL,
          `created_at` datetime NOT NULL,
          `updated_at` datetime DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `order_number` (`order_number`),
          KEY `customer_id` (`customer_id`),
          KEY `design_id` (`design_id`),
          CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
          CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`design_id`) REFERENCES `designs` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

        -- สร้างตาราง system_settings
        CREATE TABLE IF NOT EXISTS `system_settings` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `setting_key` varchar(50) NOT NULL,
          `setting_value` text NOT NULL,
          `setting_type` enum('text','number','boolean','json','html') NOT NULL DEFAULT 'text',
          `description` varchar(255) DEFAULT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `setting_key` (`setting_key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ";
        
        $statements = explode(';', $sql);
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // 7. สร้างข้อมูลเริ่มต้น
        $pdo->exec("
            INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
            ('site_name', 'GT-SportDesign', 'text', 'ชื่อเว็บไซต์'),
            ('site_description', 'เชี่ยวชาญด้านการออกแบบและเสื้อ', 'text', 'คำอธิบายเว็บไซต์'),
            ('contact_email', '<EMAIL>', 'text', 'อีเมลติดต่อ'),
            ('contact_phone', '************', 'text', 'เบอร์โทรศัพท์'),
            ('contact_line', '@gtsport', 'text', 'LINE ID'),
            ('facebook_page', 'https://www.facebook.com/GTSportDesign.1', 'text', 'Facebook Page'),
            ('min_order_amount', '500', 'number', 'ยอดสั่งซื้อขั้นต่ำ'),
            ('shipping_cost', '50', 'number', 'ค่าส่งมาตรฐาน'),
            ('free_shipping_amount', '1000', 'number', 'ยอดสั่งซื้อขั้นต่ำสำหรับการส่งฟรี'),
            ('promptpay_id', '0855599164', 'text', 'เบอร์ PromptPay'),
            ('primary_color', '#ee501b', 'text', 'สีหลักของเว็บไซต์'),
            ('secondary_color', '#ff6b35', 'text', 'สีรองของเว็บไซต์')
        ");
        
        // 8. สร้างผู้ดูแลระบบเริ่มต้น
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("
            INSERT INTO admins (username, password, email, fullname, role, status, created_at)
            VALUES ('admin', '$admin_password', '<EMAIL>', 'ผู้ดูแลระบบ', 'admin', 'active', NOW())
        ");
        
        // 9. สร้างไฟล์ admin/login.php
        $admin_login_content = '<?php
session_start();
require_once "../config/database.php";

$error = "";

// ถ้าส่งฟอร์มล็อกอิน
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST["username"] ?? "";
    $password = $_POST["password"] ?? "";
    
    if (empty($username) || empty($password)) {
        $error = "กรอกชื่อผู้ใช้และรหัสผ่าน";
    } else {
        try {
            $stmt = $pdo->prepare("SELECT * FROM admins WHERE username = ? AND status = \'active\'");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();
            
            if ($admin && password_verify($password, $admin["password"])) {
                // ล็อกอินสำเร็จ
                $_SESSION["admin_id"] = $admin



