/* Admin Panel Custom Styles */

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar .nav-link {
    font-weight: 500;
    color: #adb5bd;
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    margin: 0.2rem 0.5rem;
}

.sidebar .nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    color: #fff;
    background-color: #ee501b;
}

.sidebar .nav-link .feather {
    margin-right: 4px;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

/* Navbar */
.navbar-brand {
    padding-top: .75rem;
    padding-bottom: .75rem;
    font-size: 1rem;
    background-color: rgba(0, 0, 0, .25);
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .25);
}

/* Content */
.main-content {
    padding-top: 1.5rem;
}

/* Cards */
.card-stat {
    border-left: 4px solid #007bff;
    transition: transform 0.3s;
}

.card-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-stat.success {
    border-left-color: #28a745;
}

.card-stat.warning {
    border-left-color: #ffc107;
}

.card-stat.danger {
    border-left-color: #dc3545;
}

/* Tables */
.table-actions {
    white-space: nowrap;
}

/* Forms */
.required-field::after {
    content: " *";
    color: #dc3545;
}

/* Custom colors */
.bg-gt-primary {
    background-color: #ee501b !important;
}

.text-gt-primary {
    color: #ee501b !important;
}

.btn-gt-primary {
    background-color: #ee501b;
    border-color: #ee501b;
    color: #fff;
}

.btn-gt-primary:hover {
    background-color: #d04516;
    border-color: #d04516;
    color: #fff;
}

/* Dropdown menus in sidebar */
.sidebar .dropdown-menu {
    position: relative;
    width: 100%;
    padding: 0;
    margin: 0;
    border-radius: 0;
    border: none;
    background-color: #343a40;
    box-shadow: none;
}

.sidebar .dropdown-item {
    padding: 0.5rem 1.5rem;
    color: #adb5bd;
}

.sidebar .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding-top: 0;
    }
    
    main {
        margin-left: 0 !important;
    }
}

/* Image preview */
.img-preview {
    max-height: 150px;
    object-fit: contain;
}

/* Status badges */
.badge-pending {
    background-color: #ffc107;
    color: #212529;
}

.badge-processing {
    background-color: #17a2b8;
    color: #fff;
}

.badge-shipped {
    background-color: #007bff;
    color: #fff;
}

.badge-completed {
    background-color: #28a745;
    color: #fff;
}

.badge-cancelled {
    background-color: #dc3545;
    color: #fff;
}

/* Pagination custom style */
.pagination .page-item.active .page-link {
    background-color: #ee501b;
    border-color: #ee501b;
}

.pagination .page-link {
    color: #ee501b;
}

.pagination .page-link:hover {
    color: #d04516;
}
