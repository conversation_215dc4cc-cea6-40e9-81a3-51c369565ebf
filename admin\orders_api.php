<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get_order_details':
            getOrderDetails($pdo);
            break;
            
        case 'update_order_status':
            updateOrderStatus($pdo);
            break;
            
        case 'add_order_note':
            addOrderNote($pdo);
            break;
            
        case 'get_order_items':
            getOrderItems($pdo);
            break;
            
        case 'export_orders':
            exportOrders($pdo);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getOrderDetails($pdo) {
    $order_id = (int)($_GET['order_id'] ?? 0);
    
    if (!$order_id) {
        throw new Exception('Order ID required');
    }
    
    // ข้อมูลคำสั่งซื้อ
    $stmt = $pdo->prepare("
        SELECT o.*, c.name as customer_name, c.email as customer_email, 
               c.phone as customer_phone, c.address as customer_address
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Order not found');
    }
    
    // รายการสินค้า
    $stmt = $pdo->prepare("
        SELECT oi.*, p.name as product_name, p.description as product_description,
               pc.name as category_name
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        WHERE oi.order_id = ?
        ORDER BY oi.id
    ");
    $stmt->execute([$order_id]);
    $items = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'order' => $order,
        'items' => $items
    ]);
}

function updateOrderStatus($pdo) {
    $order_id = (int)($_POST['order_id'] ?? 0);
    $status = $_POST['status'] ?? '';
    
    if (!$order_id || !$status) {
        throw new Exception('Order ID and status required');
    }
    
    $valid_statuses = ['pending', 'processing', 'completed', 'cancelled'];
    if (!in_array($status, $valid_statuses)) {
        throw new Exception('Invalid status');
    }
    
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET status = ?, updated_at = NOW(), updated_by = ?
        WHERE id = ?
    ");
    $stmt->execute([$status, $_SESSION['admin_id'], $order_id]);
    
    // บันทึกประวัติการเปลี่ยนแปลง
    $stmt = $pdo->prepare("
        INSERT INTO order_status_history (order_id, status, changed_by, created_at)
        VALUES (?, ?, ?, NOW())
    ");
    
    try {
        $stmt->execute([$order_id, $status, $_SESSION['admin_id']]);
    } catch (Exception $e) {
        // ถ้าไม่มีตาราง order_status_history ก็ข้าม
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Order status updated successfully'
    ]);
}

function addOrderNote($pdo) {
    $order_id = (int)($_POST['order_id'] ?? 0);
    $note = trim($_POST['note'] ?? '');
    
    if (!$order_id || !$note) {
        throw new Exception('Order ID and note required');
    }
    
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET admin_note = ?, updated_at = NOW(), updated_by = ?
        WHERE id = ?
    ");
    $stmt->execute([$note, $_SESSION['admin_id'], $order_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Note added successfully'
    ]);
}

function getOrderItems($pdo) {
    $order_id = (int)($_GET['order_id'] ?? 0);
    
    if (!$order_id) {
        throw new Exception('Order ID required');
    }
    
    $stmt = $pdo->prepare("
        SELECT oi.*, p.name as product_name, p.description as product_description,
               pc.name as category_name,
               COALESCE(pi_main.file_id, pi_any.file_id) as product_image_id,
               COALESCE(fu_main.file_path, fu_any.file_path) as product_image_path
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        LEFT JOIN product_images pi_main ON p.id = pi_main.product_id AND pi_main.image_type = 'main'
        LEFT JOIN product_images pi_any ON p.id = pi_any.product_id
        LEFT JOIN file_uploads fu_main ON pi_main.file_id = fu_main.id
        LEFT JOIN file_uploads fu_any ON pi_any.file_id = fu_any.id
        WHERE oi.order_id = ?
        GROUP BY oi.id
        ORDER BY oi.id
    ");
    $stmt->execute([$order_id]);
    $items = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'items' => $items
    ]);
}

function exportOrders($pdo) {
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    $status = $_GET['status'] ?? '';
    
    $where_conditions = [];
    $params = [];
    
    if ($date_from) {
        $where_conditions[] = "DATE(o.created_at) >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $where_conditions[] = "DATE(o.created_at) <= ?";
        $params[] = $date_to;
    }
    
    if ($status) {
        $where_conditions[] = "o.status = ?";
        $params[] = $status;
    }
    
    $where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    $stmt = $pdo->prepare("
        SELECT o.order_number, o.total_amount, o.status, o.created_at,
               c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
               COUNT(oi.id) as total_items
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        $where_clause
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ");
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    // ส่งออกเป็น CSV
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="orders_export_' . date('Y-m-d') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // เขียน BOM สำหรับ UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // หัวตาราง
    fputcsv($output, [
        'เลขที่คำสั่งซื้อ',
        'ชื่อลูกค้า',
        'อีเมล',
        'เบอร์โทร',
        'จำนวนสินค้า',
        'ยอดรวม',
        'สถานะ',
        'วันที่สั่งซื้อ'
    ]);
    
    // ข้อมูล
    foreach ($orders as $order) {
        $status_text = [
            'pending' => 'รอดำเนินการ',
            'processing' => 'กำลังดำเนินการ',
            'completed' => 'เสร็จสิ้น',
            'cancelled' => 'ยกเลิก'
        ];
        
        fputcsv($output, [
            $order['order_number'],
            $order['customer_name'],
            $order['customer_email'],
            $order['customer_phone'],
            $order['total_items'],
            number_format($order['total_amount'], 2),
            $status_text[$order['status']] ?? $order['status'],
            date('d/m/Y H:i', strtotime($order['created_at']))
        ]);
    }
    
    fclose($output);
    exit;
}
?>
