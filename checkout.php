<?php
require_once './config/database.php';
$pdo = getDbConnection();

// ดึงข้อมูลการออกแบบจาก session หรือ parameter
session_start();
$design_id = $_GET['design_id'] ?? $_SESSION['current_design_id'] ?? null;

if (!$design_id) {
    header("Location: design.php");
    exit;
}

// ดึงข้อมูลการออกแบบ
$stmt = $pdo->prepare("
    SELECT d.*, p.name as product_name, p.base_price
    FROM designs d
    LEFT JOIN products p ON d.product_id = p.id
    WHERE d.id = ?
");
$stmt->execute([$design_id]);
$design = $stmt->fetch();

if (!$design) {
    header("Location: design.php");
    exit;
}

// คำนวณราคา
$design_data = json_decode($design['design_data'], true);
$quantity = (int)($design_data['quantity'] ?? 1);
$base_price = (float)$design['base_price'];

// ราคาเพิ่มเติมตามการออกแบบ
$design_fee = 50; // ค่าออกแบบ
$print_fee = 30; // ค่าพิมพ์ต่อตัว

$total_per_piece = $base_price + $design_fee + $print_fee;
$subtotal = $total_per_piece * $quantity;

// ส่วนลด (ถ้ามี)
$discount = 0;
if ($quantity >= 10) {
    $discount = $subtotal * 0.10; // ลด 10% เมื่อสั่ง 10 ตัวขึ้นไป
} elseif ($quantity >= 5) {
    $discount = $subtotal * 0.05; // ลด 5% เมื่อสั่ง 5 ตัวขึ้นไป
}

// ค่าจัดส่ง
$shipping_fee = ($subtotal - $discount) >= 1000 ? 0 : 50;

// ยอดรวม
$total = $subtotal - $discount + $shipping_fee;

// ข้อมูล PromptPay
$promptpay_id = "0855599164"; // เบอร์โทรศัพท์ที่ลงทะเบียน PromptPay
$amount = $total;

// จัดการการส่งข้อมูลการสั่งซื้อ
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_name = trim($_POST['customer_name'] ?? '');
    $customer_email = trim($_POST['customer_email'] ?? '');
    $customer_phone = trim($_POST['customer_phone'] ?? '');
    $customer_address = trim($_POST['customer_address'] ?? '');
    $payment_method = $_POST['payment_method'] ?? '';
    $notes = trim($_POST['notes'] ?? '');

    if (!$customer_name || !$customer_phone) {
        $error = "กรุณากรอกข้อมูลที่จำเป็น";
    } else {
        try {
            $pdo->beginTransaction();

            // เพิ่มหรืออัพเดตข้อมูลลูกค้า
            $stmt = $pdo->prepare("
                INSERT INTO customers (name, email, phone, address, created_at)
                VALUES (?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE
                name = VALUES(name),
                email = VALUES(email),
                address = VALUES(address),
                updated_at = NOW()
            ");
            $stmt->execute([$customer_name, $customer_email, $customer_phone, $customer_address]);

            // ดึง customer_id
            if ($pdo->lastInsertId()) {
                $customer_id = $pdo->lastInsertId();
            } else {
                $stmt = $pdo->prepare("SELECT id FROM customers WHERE phone = ?");
                $stmt->execute([$customer_phone]);
                $customer_id = $stmt->fetchColumn();
            }

            // สร้างคำสั่งซื้อ
            $order_number = 'GT' . date('Ymd') . sprintf('%04d', rand(1, 9999));

            $stmt = $pdo->prepare("
                INSERT INTO orders (
                    order_number, customer_id, design_id, quantity,
                    base_price, design_fee, print_fee, discount, shipping_fee, total_amount,
                    payment_method, payment_status, status, notes, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', 'pending', ?, NOW())
            ");
            $stmt->execute([
                $order_number, $customer_id, $design_id, $quantity,
                $base_price, $design_fee, $print_fee, $discount, $shipping_fee, $total,
                $payment_method, $notes
            ]);

            $order_id = $pdo->lastInsertId();

            // อัพเดต design ให้ผูกกับลูกค้า
            $stmt = $pdo->prepare("UPDATE designs SET customer_id = ?, status = 'ordered' WHERE id = ?");
            $stmt->execute([$customer_id, $design_id]);

            $pdo->commit();

            // เก็บข้อมูลใน session สำหรับหน้าการชำระเงิน
            $_SESSION['order_id'] = $order_id;
            $_SESSION['order_number'] = $order_number;
            $_SESSION['payment_amount'] = $total;
            $_SESSION['payment_method'] = $payment_method;

            // redirect ไปหน้าการชำระเงิน
            header("Location: payment.php?order=" . $order_number);
            exit;

        } catch (Exception $e) {
            $pdo->rollBack();
            $error = "เกิดข้อผิดพลาด: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ชำระเงิน - GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .checkout-container {
            max-width: 900px;
            margin: 0 auto;
        }

        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .card-header {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 20px 30px;
        }

        .card-body {
            padding: 30px;
        }

        .design-preview {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            margin-bottom: 20px;
        }

        .design-preview img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 10px;
        }

        .design-preview .placeholder {
            width: 200px;
            height: 200px;
            background: #e9ecef;
            border-radius: 10px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 48px;
        }

        .price-breakdown {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }

        .price-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .price-row:last-child {
            border-bottom: none;
            font-weight: 600;
            font-size: 1.1em;
            color: #11be97;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #11be97;
            box-shadow: 0 0 0 0.2rem rgba(17, 190, 151, 0.25);
        }

        .payment-method {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .payment-method:hover {
            border-color: #11be97;
            background: #f8f9fa;
        }

        .payment-method.selected {
            border-color: #11be97;
            background: linear-gradient(135deg, rgba(17, 190, 151, 0.1) 0%, rgba(14, 160, 128, 0.1) 100%);
        }

        .payment-method input[type="radio"] {
            display: none;
        }

        .payment-icon {
            font-size: 2rem;
            color: #11be97;
            margin-bottom: 10px;
        }

        .btn-checkout {
            background: linear-gradient(135deg, #11be97 0%, #0ea080 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 40px;
            font-weight: 600;
            font-size: 1.1em;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-checkout:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(17, 190, 151, 0.3);
            color: white;
        }

        .steps {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 20px;
            color: #6c757d;
        }

        .step.active {
            color: #11be97;
            font-weight: 600;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: 600;
        }

        .step.active .step-number {
            background: #11be97;
            color: white;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="checkout-container">
        <!-- Header -->
        <div class="text-center mb-4">
            <h2 class="text-white mb-3">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design
            </h2>
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <span>ออกแบบ</span>
                </div>
                <div class="step active">
                    <div class="step-number">2</div>
                    <span>ชำระเงิน</span>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <span>ดำเนินการ</span>
                </div>
            </div>
        </div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="row">
                <!-- สรุปคำสั่งซื้อ -->
                <div class="col-md-5">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-receipt me-2"></i>สรุปคำสั่งซื้อ</h5>
                        </div>
                        <div class="card-body">
                            <!-- ตัวอย่างการออกแบบ -->
                            <div class="design-preview">
                                <?php if ($design['preview_image']): ?>
                                    <img src="<?= htmlspecialchars($design['preview_image']) ?>" alt="Design Preview">
                                <?php else: ?>
                                    <div class="placeholder">
                                        <i class="fas fa-tshirt"></i>
                                    </div>
                                <?php endif; ?>
                                <h6 class="mt-3"><?= htmlspecialchars($design['design_name']) ?></h6>
                                <p class="text-muted"><?= htmlspecialchars($design['product_name']) ?></p>
                            </div>

                            <!-- รายละเอียดสินค้า -->
                            <div class="mb-3">
                                <strong>รายละเอียด:</strong>
                                <ul class="list-unstyled mt-2">
                                    <?php if (isset($design_data['selectedColor'])): ?>
                                        <li><i class="fas fa-palette me-2"></i>สี: <?= htmlspecialchars($design_data['selectedColor']) ?></li>
                                    <?php endif; ?>
                                    <?php if (isset($design_data['selectedSize'])): ?>
                                        <li><i class="fas fa-expand-arrows-alt me-2"></i>ขนาด: <?= htmlspecialchars($design_data['selectedSize']) ?></li>
                                    <?php endif; ?>
                                    <li><i class="fas fa-hashtag me-2"></i>จำนวน: <?= $quantity ?> ตัว</li>
                                </ul>
                            </div>

                            <!-- สรุปราคา -->
                            <div class="price-breakdown">
                                <div class="price-row">
                                    <span>ราคาสินค้า (<?= $quantity ?> ตัว)</span>
                                    <span><?= number_format($base_price * $quantity) ?> บาท</span>
                                </div>
                                <div class="price-row">
                                    <span>ค่าออกแบบ</span>
                                    <span><?= number_format($design_fee) ?> บาท</span>
                                </div>
                                <div class="price-row">
                                    <span>ค่าพิมพ์ (<?= $quantity ?> ตัว)</span>
                                    <span><?= number_format($print_fee * $quantity) ?> บาท</span>
                                </div>
                                <?php if ($discount > 0): ?>
                                    <div class="price-row text-success">
                                        <span>ส่วนลด</span>
                                        <span>-<?= number_format($discount) ?> บาท</span>
                                    </div>
                                <?php endif; ?>
                                <div class="price-row">
                                    <span>ค่าจัดส่ง</span>
                                    <span><?= $shipping_fee > 0 ? number_format($shipping_fee) . ' บาท' : 'ฟรี' ?></span>
                                </div>
                                <div class="price-row">
                                    <span>ยอดรวมทั้งสิ้น</span>
                                    <span><?= number_format($total) ?> บาท</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ข้อมูลลูกค้าและการชำระเงิน -->
                <div class="col-md-7">
                    <!-- ข้อมูลลูกค้า -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>ข้อมูลลูกค้า</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="customer_name" class="form-label">ชื่อ-นามสกุล *</label>
                                    <input type="text" class="form-control" id="customer_name" name="customer_name"
                                           value="<?= htmlspecialchars($_POST['customer_name'] ?? '') ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="customer_phone" class="form-label">เบอร์โทรศัพท์ *</label>
                                    <input type="tel" class="form-control" id="customer_phone" name="customer_phone"
                                           value="<?= htmlspecialchars($_POST['customer_phone'] ?? '') ?>" required>
                                </div>
                                <div class="col-12 mb-3">
                                    <label for="customer_email" class="form-label">อีเมล</label>
                                    <input type="email" class="form-control" id="customer_email" name="customer_email"
                                           value="<?= htmlspecialchars($_POST['customer_email'] ?? '') ?>">
                                </div>
                                <div class="col-12 mb-3">
                                    <label for="customer_address" class="form-label">ที่อยู่จัดส่ง *</label>
                                    <textarea class="form-control" id="customer_address" name="customer_address"
                                              rows="3" required><?= htmlspecialchars($_POST['customer_address'] ?? '') ?></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- วิธีการชำระเงิน -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-credit-card me-2"></i>วิธีการชำระเงิน</h5>
                        </div>
                        <div class="card-body">
                            <!-- PromptPay -->
                            <label class="payment-method" for="promptpay">
                                <input type="radio" id="promptpay" name="payment_method" value="promptpay" checked>
                                <div class="text-center">
                                    <div class="payment-icon">
                                        <i class="fas fa-qrcode"></i>
                                    </div>
                                    <h6>PromptPay QR Code</h6>
                                    <p class="text-muted mb-0">สแกน QR Code เพื่อชำระเงิน</p>
                                </div>
                            </label>

                            <!-- โอนธนาคาร -->
                            <label class="payment-method" for="bank_transfer">
                                <input type="radio" id="bank_transfer" name="payment_method" value="bank_transfer">
                                <div class="text-center">
                                    <div class="payment-icon">
                                        <i class="fas fa-university"></i>
                                    </div>
                                    <h6>โอนผ่านธนาคาร</h6>
                                    <p class="text-muted mb-0">โอนเงินผ่านแอปธนาคาร</p>
                                </div>
                            </label>

                            <!-- หมายเหตุ -->
                            <div class="mt-3">
                                <label for="notes" class="form-label">หมายเหตุเพิ่มเติม</label>
                                <textarea class="form-control" id="notes" name="notes" rows="2"
                                          placeholder="ระบุความต้องการพิเศษ (ถ้ามี)"><?= htmlspecialchars($_POST['notes'] ?? '') ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- ปุ่มสั่งซื้อ -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-checkout btn-lg">
                            <i class="fas fa-shopping-cart me-2"></i>
                            สั่งซื้อและชำระเงิน <?= number_format($total) ?> บาท
                        </button>

                        <div class="mt-3">
                            <a href="design.php" class="btn btn-outline-light">
                                <i class="fas fa-arrow-left me-2"></i>กลับไปแก้ไขการออกแบบ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Chat Widget -->
    <?php include 'chat_widget.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // จัดการการเลือกวิธีการชำระเงิน
        document.querySelectorAll('input[name="payment_method"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // ลบ class selected จากทุกอัน
                document.querySelectorAll('.payment-method').forEach(method => {
                    method.classList.remove('selected');
                });

                // เพิ่ม class selected ให้กับอันที่เลือก
                this.closest('.payment-method').classList.add('selected');
            });
        });

        // ตั้งค่าเริ่มต้น
        document.getElementById('promptpay').closest('.payment-method').classList.add('selected');

        // Validation ก่อนส่งฟอร์ม
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('customer_name').value.trim();
            const phone = document.getElementById('customer_phone').value.trim();
            const address = document.getElementById('customer_address').value.trim();

            if (!name || !phone || !address) {
                e.preventDefault();
                alert('กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน');
                return false;
            }

            // ตรวจสอบรูปแบบเบอร์โทร
            const phonePattern = /^[0-9]{9,10}$/;
            if (!phonePattern.test(phone)) {
                e.preventDefault();
                alert('กรุณากรอกเบอร์โทรศัพท์ให้ถูกต้อง');
                return false;
            }
        });
    </script>
</body>
</html>
