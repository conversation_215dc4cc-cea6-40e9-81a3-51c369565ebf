<?php
require_once './includes/db.php';
session_start();
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ออกแบบเสื้อออนไลน์ - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color:rgb(236, 69, 18);
            --secondary-color: #ee501b;
            --dark-color:rgb(177, 177, 177);
        }

        .design-header { background: var(--primary-color); color: white; padding: 15px 0; }
        .design-container { min-height: 80vh; }
        .design-sidebar { background: #f6f6f6; border-right: 1px solid #ddd; max-height: 80vh; overflow-y: auto; }
        .design-canvas { background: white; position: relative; min-height: 600px; }
        .shirt-preview { max-width: 100%; height: auto; }
        .tool-section { border-bottom: 1px solid #ddd; padding: 15px; }
        .tool-btn {
            background: white;
            border: 2px solid #ddd;
            padding: 12px;
            margin: 5px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        .tool-btn:hover, .tool-btn.active {
            border-color: var(--primary-color);
            background: var(--primary-color);
            color: white;
        }
        .style-option {
            width: 80px;
            height: 80px;
            border: 2px solid #ddd;
            margin: 5px;
            cursor: pointer;
            border-radius: 8px;
            overflow: hidden;
        }
        .style-option:hover, .style-option.active { border-color: var(--primary-color); }
        .style-option img { width: 100%; height: 100%; object-fit: cover; }
        .design-price {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--secondary-color);
            color: white;
            padding: 15px 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .color-picker {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #ddd;
            margin: 5px;
            cursor: pointer;
        }
        .text-controls { display: none; }
        .text-controls.active { display: block; }
        .fabric-option {
            border: 2px solid #ddd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 8px;
            cursor: pointer;
        }
        .fabric-option:hover, .fabric-option.active { border-color: var(--primary-color); }
    </style>
</head>
<body>
    <?php include './includes/header.php'; ?>

    <div class="container-fluid design-container">
        <div class="row">
            <!-- Design Tools Sidebar -->
            <div class="col-md-3 design-sidebar"style=" flex: 0 0 auto; width:100% show: 25%;height: 100%;max-height: 1000px;';">
                <!-- Pattern Tools -->
                <div class="tool-section">
                    <h6><i class="fas fa-tools"></i> เครื่องมือออกแบบ</h6>
                    <div class="d-flex flex-wrap">
                        <button class="tool-btn active" data-tool="pattern">
                            <i class="fas fa-shapes"></i><br><small>ลวดลาย</small>
                        </button>
                        <button class="tool-btn" data-tool="text">
                            <i class="fas fa-font"></i><br><small>ข้อความ</small>
                        </button>
                        <button class="tool-btn" data-tool="image">
                            <i class="fas fa-image"></i><br><small>รูปภาพ</small>
                        </button>
                        <button class="tool-btn" data-tool="collar">
                            <i class="fas fa-collar"></i><br><small>คอเสื้อ</small>
                        </button>
                        <button class="tool-btn" data-tool="sleeve">
                            <i class="fas fa-hand-paper"></i><br><small>แขนเสื้อ</small>
                        </button>
                        <button class="tool-btn" data-tool="pocket">
                            <i class="fas fa-square"></i><br><small>กระเป๋า</small>
                        </button>
                    </div>
                </div>

                <!-- Style Options -->
                <div class="tool-section" id="pattern-section">
                    <h6>เลือกสไตล์เสื้อ</h6>
                    <div class="d-flex flex-wrap">
                        <div class="style-option active">
                            <img src="https://ext.same-assets.com/3758631838/1999605391.png" alt="Default">
                            <small>Default</small>
                        </div>
                        <div class="style-option">
                            <img src="https://ext.same-assets.com/3758631838/3343018408.png" alt="Style2">
                            <small>Style2</small>
                        </div>
                        <div class="style-option">
                            <img src="https://ext.same-assets.com/3758631838/1394214016.png" alt="Style3">
                            <small>Style3</small>
                        </div>
                        <div class="style-option">
                            <img src="https://ext.same-assets.com/3758631838/3750747465.png" alt="Style4">
                            <small>Style4</small>
                        </div>
                    </div>
                </div>

                <!-- Color Selection -->
                <div class="tool-section">
                    <h6>เลือกสีเสื้อ</h6>
                    <div class="d-flex flex-wrap">
                        <div class="color-picker" style="background: #ffffff;" data-color="#ffffff"></div>
                        <div class="color-picker" style="background: #000000;" data-color="#000000"></div>
                        <div class="color-picker" style="background: #ee501b;" data-color="#ee501b"></div>
                        <div class="color-picker" style="background: #25a38c;" data-color="#25a38c"></div>
                        <div class="color-picker" style="background: #ff6b35;" data-color="#ff6b35"></div>
                        <div class="color-picker" style="background: #3498db;" data-color="#3498db"></div>
                        <div class="color-picker" style="background: #f39c12;" data-color="#f39c12"></div>
                        <div class="color-picker" style="background: #e74c3c;" data-color="#e74c3c"></div>
                    </div>
                </div>

                <!-- Text Controls -->
                <div class="tool-section text-controls" id="text-section">
                    <h6>เพิ่มข้อความ</h6>
                    <div class="mb-3">
                        <label class="form-label">ข้อความ</label>
                        <input type="text" class="form-control" id="textInput" placeholder="ใส่ข้อความที่ต้องการ">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ฟอนต์</label>
                        <select class="form-select" id="fontSelect">
                            <option value="Kanit">Kanit</option>
                            <option value="Prompt">Prompt</option>
                            <option value="Trirong">Trirong</option>
                            <option value="Arial">Arial</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ขนาด</label>
                        <input type="range" class="form-range" id="fontSize" min="12" max="72" value="24">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">สีข้อความ</label>
                        <input type="color" class="form-control form-control-color" id="textColor" value="#000000">
                    </div>
                    <button class="btn btn-primary btn-sm" onclick="addText()">เพิ่มข้อความ</button>
                </div>

                <!-- Image Upload -->
                <div class="tool-section text-controls" id="image-section">
                    <h6>อัพโหลดรูปภาพ</h6>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="imageUpload" accept="image/*">
                    </div>
                    <small class="text-muted">ไฟล์ PNG, JPG ขนาดไม่เกิน 2MB</small>
                </div>

                <!-- Fabric Selection -->
                <div class="tool-section">
                    <h6>เลือกเนื้อผ้า</h6>
                    <div class="fabric-option active" data-fabric="syntex" data-price="250">
                        <strong>Syntex™</strong><br>
                        <small>ผ้าโพลีเอสเตอร์ 100% ระบายอากาศดี</small><br>
                        <span class="text-primary">250 บาท/ตัว</span>
                    </div>
                    <div class="fabric-option" data-fabric="cotton" data-price="220">
                        <strong>Cotton Blend</strong><br>
                        <small>ผ้าคอตตอน 65% โพลีเอสเตอร์ 35%</small><br>
                        <span class="text-primary">220 บาท/ตัว</span>
                    </div>
                    <div class="fabric-option" data-fabric="coolmax" data-price="280">
                        <strong>Coolmax</strong><br>
                        <small>เนื้อผ้าระบายความชื้นพิเศษ</small><br>
                        <span class="text-primary">280 บาท/ตัว</span>
                    </div>
                </div>
            </div>

            <!-- Design Canvas -->
            <div class="col-md-6 design-canvas p-4">
                <div class="text-center">
                    <!-- Shirt View Controls -->
                    <div class="mb-3">
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-secondary active" data-view="front">
                                <i class="fas fa-tshirt"></i> หน้า
                            </button>
                            <button class="btn btn-outline-secondary" data-view="back">
                                <i class="fas fa-tshirt"></i> หลัง
                            </button>
                            <button class="btn btn-outline-secondary" data-view="left">
                                <i class="fas fa-tshirt"></i> ซ้าย
                            </button>
                            <button class="btn btn-outline-secondary" data-view="right">
                                <i class="fas fa-tshirt"></i> ขวา
                            </button>
                        </div>
                    </div>

                    <!-- Shirt Preview -->
                    <div id="shirtPreview" style="position: relative; display: inline-block;">
                        <img src="https://ext.same-assets.com/3758631838/1999605391.png"
                             alt="เสื้อโปโล"
                             class="shirt-preview"
                             id="shirtImage"
                             style="max-width: 400px;">

                        <!-- Design Elements Container -->
                        <div id="designElements" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"></div>
                    </div>

                    <!-- Zoom Controls -->
                    <div class="mt-3">
                        <button class="btn btn-outline-secondary btn-sm" onclick="zoomOut()">
                            <i class="fas fa-search-minus"></i> ซูมออก
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="zoomIn()">
                            <i class="fas fa-search-plus"></i> ซูมเข้า
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="resetZoom()">
                            <i class="fas fa-expand"></i> รีเซ็ต
                        </button>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="col-md-3 p-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-shopping-cart"></i> สรุปการสั่งซื้อ</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">จำนวน</label>
                            <div class="input-group">
                                <button class="btn btn-outline-secondary" onclick="decreaseQty()">-</button>
                                <input type="number" class="form-control text-center" id="quantity" value="10" min="1">
                                <button class="btn btn-outline-secondary" onclick="increaseQty()">+</button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">ขนาด</label>
                            <div class="row">
                                <div class="col-4"><input type="number" class="form-control form-control-sm" placeholder="S" id="sizeS"></div>
                                <div class="col-4"><input type="number" class="form-control form-control-sm" placeholder="M" id="sizeM"></div>
                                <div class="col-4"><input type="number" class="form-control form-control-sm" placeholder="L" id="sizeL"></div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-4"><input type="number" class="form-control form-control-sm" placeholder="XL" id="sizeXL"></div>
                                <div class="col-4"><input type="number" class="form-control form-control-sm" placeholder="2XL" id="size2XL"></div>
                                <div class="col-4"><input type="number" class="form-control form-control-sm" placeholder="3XL" id="size3XL"></div>
                            </div>
                        </div>

                        <hr>
                        <div class="d-flex justify-content-between">
                            <span>ราคาต่อตัว:</span>
                            <span id="unitPrice">250 บาท</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>จำนวน:</span>
                            <span id="totalQty">10 ตัว</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>รวมทั้งหมด:</span>
                            <span id="totalPrice" class="text-primary">2,500 บาท</span>
                        </div>

                        <div class="mt-3">
                            <button class="btn btn-success w-100 mb-2" onclick="saveDesign()">
                                <i class="fas fa-save"></i> บันทึกงานออกแบบ
                            </button>
                            <button class="btn btn-primary w-100" onclick="orderNow()">
                                <i class="fas fa-shopping-cart"></i> สั่งซื้อเลย
                            </button>
                        </div>

                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                ส่วนลด 20% สำหรับออเดอร์แรก<br>
                                ฟรีค่าจัดส่งเมื่อสั่ง 20 ตัวขึ้นไป
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Contact Info -->
                <div class="card mt-3">
                    <div class="card-body text-center">
                        <h6>ต้องการความช่วยเหลือ?</h6>
                        <a href="tel:0855599164" class="btn btn-outline-success btn-sm me-2">
                            <i class="fas fa-phone"></i> โทร
                        </a>
                        <a href="https://line.me/ti/p/@gtsport" class="btn btn-outline-success btn-sm" target="_blank">
                            <i class="fab fa-line"></i> แชท
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php include './includes/footer.php'; ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentPrice = 250;
        let currentView = 'front';

        // Tool switching
        document.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Hide all tool sections
                document.querySelectorAll('.text-controls').forEach(s => s.classList.remove('active'));

                // Show relevant section
                const tool = this.dataset.tool;
                if (tool === 'text') {
                    document.getElementById('text-section').classList.add('active');
                } else if (tool === 'image') {
                    document.getElementById('image-section').classList.add('active');
                }
            });
        });

        // Style selection
        document.querySelectorAll('.style-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.style-option').forEach(o => o.classList.remove('active'));
                this.classList.add('active');

                const imgSrc = this.querySelector('img').src;
                document.getElementById('shirtImage').src = imgSrc;
            });
        });

        // Color picker
        document.querySelectorAll('.color-picker').forEach(picker => {
            picker.addEventListener('click', function() {
                const color = this.dataset.color;
                // Apply color filter to shirt (simplified)
                const shirt = document.getElementById('shirtImage');
                shirt.style.filter = color === '#ffffff' ? 'none' : `hue-rotate(${Math.random() * 360}deg)`;
            });
        });

        // Fabric selection
        document.querySelectorAll('.fabric-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.fabric-option').forEach(o => o.classList.remove('active'));
                this.classList.add('active');

                currentPrice = parseInt(this.dataset.price);
                updatePrice();
            });
        });

        // View switching
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('[data-view]').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentView = this.dataset.view;

                // Change shirt image based on view (simplified)
                const shirtImg = document.getElementById('shirtImage');
                if (currentView === 'back') {
                    shirtImg.src = 'https://ext.same-assets.com/3758631838/2295757233.png';
                } else {
                    shirtImg.src = 'https://ext.same-assets.com/3758631838/1999605391.png';
                }
            });
        });

        // Quantity controls
        function increaseQty() {
            const qty = document.getElementById('quantity');
            qty.value = parseInt(qty.value) + 1;
            updatePrice();
        }

        function decreaseQty() {
            const qty = document.getElementById('quantity');
            if (parseInt(qty.value) > 1) {
                qty.value = parseInt(qty.value) - 1;
                updatePrice();
            }
        }

        function updatePrice() {
            const qty = parseInt(document.getElementById('quantity').value);
            const total = currentPrice * qty;
            document.getElementById('unitPrice').textContent = currentPrice + ' บาท';
            document.getElementById('totalQty').textContent = qty + ' ตัว';
            document.getElementById('totalPrice').textContent = total.toLocaleString() + ' บาท';
        }

        // Add text function
        function addText() {
            const text = document.getElementById('textInput').value;
            const font = document.getElementById('fontSelect').value;
            const size = document.getElementById('fontSize').value;
            const color = document.getElementById('textColor').value;

            if (text.trim()) {
                const textElement = document.createElement('div');
                textElement.textContent = text;
                textElement.style.cssText = `
                    position: absolute;
                    top: 40%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-family: ${font};
                    font-size: ${size}px;
                    color: ${color};
                    cursor: move;
                    user-select: none;
                    z-index: 10;
                `;

                document.getElementById('designElements').appendChild(textElement);

                // Make text draggable (simplified)
                makeDraggable(textElement);

                document.getElementById('textInput').value = '';
            }
        }

        function makeDraggable(element) {
            let isDragging = false;
            let startX, startY, startLeft, startTop;

            element.addEventListener('mousedown', function(e) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                startLeft = parseInt(this.style.left || '50%');
                startTop = parseInt(this.style.top || '40%');
            });

            document.addEventListener('mousemove', function(e) {
                if (!isDragging) return;

                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;

                element.style.left = (startLeft + deltaX) + 'px';
                element.style.top = (startTop + deltaY) + 'px';
            });

            document.addEventListener('mouseup', function() {
                isDragging = false;
            });
        }

        // Zoom functions
        let currentZoom = 1;

        function zoomIn() {
            currentZoom += 0.1;
            document.getElementById('shirtPreview').style.transform = `scale(${currentZoom})`;
        }

        function zoomOut() {
            if (currentZoom > 0.5) {
                currentZoom -= 0.1;
                document.getElementById('shirtPreview').style.transform = `scale(${currentZoom})`;
            }
        }

        function resetZoom() {
            currentZoom = 1;
            document.getElementById('shirtPreview').style.transform = 'scale(1)';
        }

        // Save design
        function saveDesign() {
            alert('งานออกแบบถูกบันทึกแล้ว! คุณสามารถกลับมาแก้ไขได้ในภายหลัง');
        }

        // Order now
        function orderNow() {
            const qty = document.getElementById('quantity').value;
            const total = currentPrice * qty;

            if (confirm(`ยืนยันการสั่งซื้อ\nจำนวน: ${qty} ตัว\nราคารวม: ${total.toLocaleString()} บาท\n\nต้องการดำเนินการต่อ?`)) {
                // Redirect to order form or contact
                window.location.href = 'contact.php?design=true&qty=' + qty + '&total=' + total;
            }
        }

        // Image upload
        document.getElementById('imageUpload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.cssText = `
                        position: absolute;
                        top: 30%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        max-width: 100px;
                        max-height: 100px;
                        cursor: move;
                        z-index: 10;
                    `;

                    document.getElementById('designElements').appendChild(img);
                    makeDraggable(img);
                };
                reader.readAsDataURL(file);
            }
        });

        // Initialize
        updatePrice();
    </script>
</body>
</html>
