<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ดึงข้อมูลหมวดหมู่สินค้า
$categories = [];
try {
    $stmt = $pdo->query("
        SELECT pc.*, COUNT(p.id) as product_count
        FROM product_categories pc
        LEFT JOIN products p ON pc.id = p.category_id AND p.status = 'active'
        GROUP BY pc.id
        ORDER BY pc.sort_order, pc.name
    ");
    $categories = $stmt->fetchAll();
} catch (Exception $e) {
    // ถ้าไม่มีตาราง ใช้ข้อมูลจำลอง
    $categories = [
        ['id' => 1, 'name' => 'เสื้อกีฬา', 'description' => 'เสื้อกีฬาทุกประเภท', 'status' => 'active', 'sort_order' => 1, 'product_count' => 5],
        ['id' => 2, 'name' => 'กางเกงกีฬา', 'description' => 'กางเกงกีฬาคุณภาพสูง', 'status' => 'active', 'sort_order' => 2, 'product_count' => 3],
        ['id' => 3, 'name' => 'ชุดทีม', 'description' => 'ชุดทีมกีฬาครบเซต', 'status' => 'active', 'sort_order' => 3, 'product_count' => 2],
        ['id' => 4, 'name' => 'อุปกรณ์เสริม', 'description' => 'อุปกรณ์เสริมสำหรับนักกีฬา', 'status' => 'active', 'sort_order' => 4, 'product_count' => 1]
    ];
}

// จัดการการเพิ่ม/แก้ไข/ลบหมวดหมู่
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_category') {
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $sort_order = (int)($_POST['sort_order'] ?? 0);
        
        if ($name) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO product_categories (name, description, sort_order, status, created_at, updated_at)
                    VALUES (?, ?, ?, 'active', NOW(), NOW())
                ");
                $stmt->execute([$name, $description, $sort_order]);
                $success = 'เพิ่มหมวดหมู่เรียบร้อยแล้ว';
            } catch (Exception $e) {
                $error = 'ไม่สามารถเพิ่มหมวดหมู่ได้: ' . $e->getMessage();
            }
        } else {
            $error = 'กรุณากรอกชื่อหมวดหมู่';
        }
    }
    
    if ($action === 'edit_category') {
        $category_id = (int)($_POST['category_id'] ?? 0);
        $name = trim($_POST['name'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $sort_order = (int)($_POST['sort_order'] ?? 0);
        $status = $_POST['status'] ?? 'active';
        
        if ($category_id > 0 && $name) {
            try {
                $stmt = $pdo->prepare("
                    UPDATE product_categories 
                    SET name = ?, description = ?, sort_order = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$name, $description, $sort_order, $status, $category_id]);
                $success = 'แก้ไขหมวดหมู่เรียบร้อยแล้ว';
            } catch (Exception $e) {
                $error = 'ไม่สามารถแก้ไขหมวดหมู่ได้: ' . $e->getMessage();
            }
        }
    }
    
    if ($action === 'delete_category') {
        $category_id = (int)($_POST['category_id'] ?? 0);
        
        if ($category_id > 0) {
            try {
                // ตรวจสอบว่ามีสินค้าในหมวดหมู่นี้หรือไม่
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
                $stmt->execute([$category_id]);
                $product_count = $stmt->fetchColumn();
                
                if ($product_count > 0) {
                    $error = 'ไม่สามารถลบหมวดหมู่ได้ เนื่องจากมีสินค้าในหมวดหมู่นี้ ' . $product_count . ' รายการ';
                } else {
                    $stmt = $pdo->prepare("DELETE FROM product_categories WHERE id = ?");
                    $stmt->execute([$category_id]);
                    $success = 'ลบหมวดหมู่เรียบร้อยแล้ว';
                }
            } catch (Exception $e) {
                $error = 'ไม่สามารถลบหมวดหมู่ได้: ' . $e->getMessage();
            }
        }
    }
    
    // รีเฟรชหน้า
    if ($success || $error) {
        header('Location: products_categories.php?msg=' . urlencode($success ?: $error) . '&type=' . ($success ? 'success' : 'error'));
        exit();
    }
}

// แสดงข้อความ
if (isset($_GET['msg'])) {
    $message = $_GET['msg'];
    $message_type = $_GET['type'] ?? 'info';
    if ($message_type === 'success') {
        $success = $message;
    } else {
        $error = $message;
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการหมวดหมู่สินค้า - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
        .category-card {
            transition: all 0.3s ease;
        }
        .category-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php" class="active"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">จัดการหมวดหมู่สินค้า</h4>
                <small class="text-muted">เพิ่ม แก้ไข และจัดการหมวดหมู่สินค้า</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="products.php">จัดการสินค้า</a></li>
                    <li class="breadcrumb-item active">หมวดหมู่สินค้า</li>
                </ol>
            </nav>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Add Category Button -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5>รายการหมวดหมู่สินค้า (<?php echo count($categories); ?> หมวดหมู่)</h5>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                    <i class="fas fa-plus me-2"></i>เพิ่มหมวดหมู่ใหม่
                </button>
            </div>

            <!-- Categories Grid -->
            <div class="row">
                <?php foreach ($categories as $category): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card category-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h6 class="card-title mb-0"><?php echo htmlspecialchars($category['name']); ?></h6>
                                <span class="badge bg-<?php echo $category['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                    <?php echo $category['status'] === 'active' ? 'ใช้งาน' : 'ปิดใช้งาน'; ?>
                                </span>
                            </div>
                            
                            <p class="card-text text-muted small">
                                <?php echo htmlspecialchars($category['description'] ?? 'ไม่มีคำอธิบาย'); ?>
                            </p>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-box me-1"></i>
                                    <?php echo $category['product_count']; ?> สินค้า
                                </small>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" data-bs-toggle="modal" 
                                            data-bs-target="#editCategoryModal<?php echo $category['id']; ?>">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" 
                                            onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['name']); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Edit Category Modal -->
                <div class="modal fade" id="editCategoryModal<?php echo $category['id']; ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">แก้ไขหมวดหมู่: <?php echo htmlspecialchars($category['name']); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form method="POST">
                                <div class="modal-body">
                                    <input type="hidden" name="action" value="edit_category">
                                    <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">ชื่อหมวดหมู่</label>
                                        <input type="text" name="name" class="form-control" 
                                               value="<?php echo htmlspecialchars($category['name']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">คำอธิบาย</label>
                                        <textarea name="description" class="form-control" rows="3"><?php echo htmlspecialchars($category['description'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">ลำดับการแสดง</label>
                                                <input type="number" name="sort_order" class="form-control" 
                                                       value="<?php echo $category['sort_order']; ?>" min="0">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">สถานะ</label>
                                                <select name="status" class="form-select">
                                                    <option value="active" <?php echo $category['status'] === 'active' ? 'selected' : ''; ?>>ใช้งาน</option>
                                                    <option value="inactive" <?php echo $category['status'] === 'inactive' ? 'selected' : ''; ?>>ปิดใช้งาน</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                    <button type="submit" class="btn btn-primary">บันทึก</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <!-- Add Category Modal -->
    <div class="modal fade" id="addCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">เพิ่มหมวดหมู่ใหม่</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add_category">
                        
                        <div class="mb-3">
                            <label class="form-label">ชื่อหมวดหมู่</label>
                            <input type="text" name="name" class="form-control" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">คำอธิบาย</label>
                            <textarea name="description" class="form-control" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">ลำดับการแสดง</label>
                            <input type="number" name="sort_order" class="form-control" value="0" min="0">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary">เพิ่มหมวดหมู่</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Form -->
    <form id="deleteForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="delete_category">
        <input type="hidden" name="category_id" id="deleteCategoryId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deleteCategory(categoryId, categoryName) {
            if (confirm(`คุณต้องการลบหมวดหมู่ "${categoryName}" หรือไม่?\n\nหมายเหตุ: หากมีสินค้าในหมวดหมู่นี้ จะไม่สามารถลบได้`)) {
                document.getElementById('deleteCategoryId').value = categoryId;
                document.getElementById('deleteForm').submit();
            }
        }
    </script>
</body>
</html>
