.elementor-kit-7 {
    --e-global-color-primary: #FF4805;
    --e-global-color-secondary: #F86800;
    --e-global-color-text: #333333;
    --e-global-color-accent: #FF4805;
    --e-global-color-15896d6: #FFFFFF;
    --e-global-color-d137d4c: #F51717;
    --e-global-color-80a9e88: #15CEB8;
    --e-global-color-6a05be6: #EFEFEF;
    --e-global-color-5677475: #EAEAEB;
    --e-global-color-cc2109e: #000000;
    --e-global-color-9a1277a: #E3E3E3;
    --e-global-typography-primary-font-family: "Kanit";
    --e-global-typography-primary-font-weight: 600;
    --e-global-typography-secondary-font-family: "Kanit";
    --e-global-typography-secondary-font-weight: 400;
    --e-global-typography-text-font-family: "Kanit";
    --e-global-typography-text-font-weight: 400;
    --e-global-typography-accent-font-family: "Kanit";
    --e-global-typography-accent-font-weight: 500;
    font-family: "Kanit", Kanit;
}

.elementor-kit-7 button,
.elementor-kit-7 input[type="button"],
.elementor-kit-7 input[type="submit"],
.elementor-kit-7 .elementor-button {
    background-color: transparent;
    background-image: linear-gradient(90deg, var(--e-global-color-accent) 0%, var(--e-global-color-secondary) 100%);
    border-radius: 50px 50px 50px 50px;
    padding: 15px 15px 16px 15px;
}

.elementor-kit-7 button:hover,
.elementor-kit-7 button:focus,
.elementor-kit-7 input[type="button"]:hover,
.elementor-kit-7 input[type="button"]:focus,
.elementor-kit-7 input[type="submit"]:hover,
.elementor-kit-7 input[type="submit"]:focus,
.elementor-kit-7 .elementor-button:hover,
.elementor-kit-7 .elementor-button:focus {
    background-color: transparent;
    background-image: linear-gradient(90deg, var(--e-global-color-secondary) 0%, var(--e-global-color-accent) 100%);
}

.elementor-kit-7 e-page-transition {
    background-color: #FFBC7D;
}

.elementor-kit-7 a {
    font-family: "Kanit", Kanit;
}

.elementor-kit-7 h1 {
    font-family: "Kanit", Kanit;
}

.elementor-kit-7 h2 {
    font-family: "Kanit", Kanit;
}

.elementor-kit-7 h3 {
    font-family: "Kanit", Kanit;
}

.elementor-kit-7 h4 {
    font-family: "Kanit", Kanit;
}

.elementor-kit-7 h5 {
    font-family: "Kanit", Kanit;
}

.elementor-kit-7 h6 {
    font-family: "Kanit", Kanit;
}

.elementor-section.elementor-section-boxed>.elementor-container {
    max-width: 1140px;
}

.e-con {
    --container-max-width: 1140px;
}

.elementor-widget:not(:last-child) {
    margin-block-end: 20px;
}

.elementor-element {
    --widgets-spacing: 20px 20px;
    --widgets-spacing-row: 20px;
    --widgets-spacing-column: 20px;
}

    {}

h1.entry-title {
    display: var(--page-title-display);
}

.site-header .site-branding {
    flex-direction: column;
    align-items: stretch;
}

.site-header {
    padding-inline-end: 0px;
    padding-inline-start: 0px;
}

.site-footer .site-branding {
    flex-direction: column;
    align-items: stretch;
}

@media(max-width:1150px) {
    .elementor-section.elementor-section-boxed>.elementor-container {
        max-width: 1024px;
    }

    .e-con {
        --container-max-width: 1024px;
    }
}

@media(max-width:767px) {
    .elementor-section.elementor-section-boxed>.elementor-container {
        max-width: 767px;
    }

    .e-con {
        --container-max-width: 767px;
    }
}

/* Start custom CSS */
strong {
    font-weight: 500;
}

b {
    font-weight: 500;
}

/* End custom CSS */