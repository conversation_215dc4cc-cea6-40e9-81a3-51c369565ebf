<?php
/**
 * GT-SportDesign Admin Messages Management
 * Professional message management system
 */

require_once './includes/session.php';
requireAdminAuth();

require_once '../config/database.php';

$db = getDbConnection();
$current_admin = getCurrentAdmin();

// Handle actions
$action = $_GET['action'] ?? 'list';
$message_id = $_GET['id'] ?? null;
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'reply':
            $message_id = $_POST['message_id'];
            $reply_text = trim($_POST['reply_text']);

            if (!empty($reply_text)) {
                try {
                    // Update message with reply
                    $stmt = $db->prepare("
                        UPDATE contacts
                        SET admin_reply = ?, status = 'replied', updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$reply_text, $message_id]);

                    // Get contact info for email
                    $stmt = $db->prepare("SELECT * FROM contacts WHERE id = ?");
                    $stmt->execute([$message_id]);
                    $contact = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($contact) {
                        // Send reply email
                        $email_sent = sendReplyEmail($contact, $reply_text);
                        $success_message = 'ตอบกลับข้อความเรียบร้อยแล้ว' . ($email_sent ? ' และส่งอีเมลแล้ว' : '');
                    }

                    logAdminActivity('reply_message', "Replied to message ID: $message_id");
                } catch (Exception $e) {
                    $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
                }
            } else {
                $error_message = 'กรุณากรอกข้อความตอบกลับ';
            }
            break;

        case 'mark_read':
            $message_id = $_POST['message_id'];
            try {
                $stmt = $db->prepare("UPDATE contacts SET status = 'read', updated_at = NOW() WHERE id = ?");
                $stmt->execute([$message_id]);
                $success_message = 'อัพเดทสถานะเรียบร้อยแล้ว';
                logAdminActivity('mark_read', "Marked message ID: $message_id as read");
            } catch (Exception $e) {
                $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            }
            break;

        case 'delete':
            $message_id = $_POST['message_id'];
            try {
                $stmt = $db->prepare("DELETE FROM contacts WHERE id = ?");
                $stmt->execute([$message_id]);
                $success_message = 'ลบข้อความเรียบร้อยแล้ว';
                logAdminActivity('delete_message', "Deleted message ID: $message_id");
            } catch (Exception $e) {
                $error_message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            }
            break;
    }
}

// Get messages based on action
if ($action === 'view' && $message_id) {
    // Get single message
    $stmt = $db->prepare("SELECT * FROM contacts WHERE id = ?");
    $stmt->execute([$message_id]);
    $message = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$message) {
        header('Location: messages.php');
        exit;
    }

    // Mark as read if unread
    if ($message['status'] === 'unread') {
        $stmt = $db->prepare("UPDATE contacts SET status = 'read', updated_at = NOW() WHERE id = ?");
        $stmt->execute([$message_id]);
    }
} else {
    // Get all messages with pagination
    $page = max(1, intval($_GET['page'] ?? 1));
    $per_page = 20;
    $offset = ($page - 1) * $per_page;

    $filter = $_GET['filter'] ?? 'all';
    $search = $_GET['search'] ?? '';

    // Build query
    $where_conditions = [];
    $params = [];

    if ($filter !== 'all') {
        $where_conditions[] = "status = ?";
        $params[] = $filter;
    }

    if (!empty($search)) {
        $where_conditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
        $search_term = "%$search%";
        $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Get total count
    $count_stmt = $db->prepare("SELECT COUNT(*) FROM contacts $where_clause");
    $count_stmt->execute($params);
    $total_messages = $count_stmt->fetchColumn();
    $total_pages = ceil($total_messages / $per_page);

    // Get messages
    $stmt = $db->prepare("
        SELECT * FROM contacts
        $where_clause
        ORDER BY created_at DESC
        LIMIT $per_page OFFSET $offset
    ");
    $stmt->execute($params);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Send reply email to customer
 */
function sendReplyEmail($contact, $reply_text) {
    try {
        $subject = 'ตอบกลับ: ' . $contact['subject'];

        $message = "
        <html>
        <head><title>ตอบกลับจาก GT-SportDesign</title></head>
        <body style='font-family: Arial, sans-serif;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h2 style='color: #eb4e17;'>🎽 GT-SportDesign</h2>
                <h3>ตอบกลับข้อความของคุณ</h3>

                <p>สวัสดีคุณ {$contact['name']},</p>

                <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h4>ข้อความเดิมของคุณ:</h4>
                    <p><strong>หัวข้อ:</strong> {$contact['subject']}</p>
                    <p><strong>ข้อความ:</strong></p>
                    <div style='background: white; padding: 15px; border-radius: 5px;'>
                        " . nl2br(htmlspecialchars($contact['message'])) . "
                    </div>
                </div>

                <div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h4>คำตอบจากเรา:</h4>
                    <div style='background: white; padding: 15px; border-radius: 5px;'>
                        " . nl2br(htmlspecialchars($reply_text)) . "
                    </div>
                </div>

                <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                    <h4>ช่องทางติดต่ออื่นๆ</h4>
                    <p>📞 โทรศัพท์: 085-559-9164</p>
                    <p>📱 LINE: @gtsport</p>
                    <p>📧 อีเมล: <EMAIL></p>
                    <p>📍 ที่อยู่: 339/7 ม.4 ต.บ้านดู่ อ.เมือง จ.เชียงราย</p>
                </div>

                <p>ขอบคุณที่ติดต่อมายัง GT-SportDesign</p>

                <hr>
                <p style='text-align: center; color: #666; font-size: 12px;'>
                    © 2024 GT-SportDesign. All rights reserved.
                </p>
            </div>
        </body>
        </html>
        ";

        return sendEmail($contact['email'], $subject, $message);
    } catch (Exception $e) {
        error_log("Failed to send reply email: " . $e->getMessage());
        return false;
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการข้อความ - GT-SportDesign Admin</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .main-content {
            margin-top: 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #eb4e17 0%, #d63916 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 20px;
        }

        .message-item {
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .message-item.unread {
            border-left-color: #eb4e17;
            background: #fff8f6;
        }

        .message-item:hover {
            background: #f8f9fa;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .status-badge {
            font-size: 0.8em;
            padding: 4px 8px;
        }

        .btn-action {
            padding: 5px 10px;
            font-size: 0.8em;
            margin: 2px;
        }

        .reply-form {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-tshirt me-2"></i>GT-SportDesign Admin
            </a>

            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="dashboard.php">
                    <i class="fas fa-dashboard me-1"></i>Dashboard
                </a>
                <a class="nav-link text-white" href="logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>ออกจากระบบ
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-content">
        <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error_message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($action === 'view' && isset($message)): ?>
        <!-- Single Message View -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-envelope me-2"></i>ข้อความจาก <?php echo htmlspecialchars($message['name']); ?>
                            </h5>
                            <a href="messages.php" class="btn btn-light btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>กลับ
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h6><strong>หัวข้อ:</strong> <?php echo htmlspecialchars($message['subject']); ?></h6>
                                <p><strong>จาก:</strong> <?php echo htmlspecialchars($message['name']); ?> (<?php echo htmlspecialchars($message['email']); ?>)</p>
                                <?php if ($message['phone']): ?>
                                <p><strong>โทรศัพท์:</strong> <?php echo htmlspecialchars($message['phone']); ?></p>
                                <?php endif; ?>
                                <p><strong>วันที่:</strong> <?php echo formatDate($message['created_at']); ?></p>

                                <div class="mt-4">
                                    <h6><strong>ข้อความ:</strong></h6>
                                    <div class="border p-3 rounded bg-light">
                                        <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                    </div>
                                </div>

                                <?php if ($message['admin_reply']): ?>
                                <div class="mt-4">
                                    <h6><strong>คำตอบ:</strong></h6>
                                    <div class="border p-3 rounded bg-info bg-opacity-10">
                                        <?php echo nl2br(htmlspecialchars($message['admin_reply'])); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="mb-0">การจัดการ</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>สถานะ:</strong>
                                            <span class="badge bg-<?php echo $message['status'] === 'unread' ? 'danger' : ($message['status'] === 'read' ? 'warning' : 'success'); ?>">
                                                <?php echo $message['status'] === 'unread' ? 'ยังไม่อ่าน' : ($message['status'] === 'read' ? 'อ่านแล้ว' : 'ตอบกลับแล้ว'); ?>
                                            </span>
                                        </p>

                                        <div class="d-grid gap-2">
                                            <?php if ($message['status'] !== 'replied'): ?>
                                            <button class="btn btn-primary btn-sm" onclick="showReplyForm()">
                                                <i class="fas fa-reply me-1"></i>ตอบกลับ
                                            </button>
                                            <?php endif; ?>

                                            <form method="POST" class="d-inline">
                                                <input type="hidden" name="action" value="mark_read">
                                                <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                                <button type="submit" class="btn btn-warning btn-sm w-100">
                                                    <i class="fas fa-eye me-1"></i>ทำเครื่องหมายอ่านแล้ว
                                                </button>
                                            </form>

                                            <form method="POST" class="d-inline" onsubmit="return confirm('ต้องการลบข้อความนี้?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                                <button type="submit" class="btn btn-danger btn-sm w-100">
                                                    <i class="fas fa-trash me-1"></i>ลบ
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Reply Form -->
                        <div id="replyForm" class="reply-form" style="display: none;">
                            <h6><i class="fas fa-reply me-2"></i>ตอบกลับข้อความ</h6>
                            <form method="POST">
                                <input type="hidden" name="action" value="reply">
                                <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">

                                <div class="mb-3">
                                    <label class="form-label">ข้อความตอบกลับ</label>
                                    <textarea name="reply_text" class="form-control" rows="5" required
                                              placeholder="พิมพ์ข้อความตอบกลับที่นี่..."></textarea>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-1"></i>ส่งคำตอบ
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="hideReplyForm()">
                                        ยกเลิก
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php else: ?>
        <!-- Messages List -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-envelope me-2"></i>จัดการข้อความ
                            </h5>
                            <span class="badge bg-light text-dark">
                                ทั้งหมด <?php echo number_format($total_messages); ?> ข้อความ
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Filters -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <form method="GET" class="d-flex gap-2">
                                    <select name="filter" class="form-select">
                                        <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>ทั้งหมด</option>
                                        <option value="unread" <?php echo $filter === 'unread' ? 'selected' : ''; ?>>ยังไม่อ่าน</option>
                                        <option value="read" <?php echo $filter === 'read' ? 'selected' : ''; ?>>อ่านแล้ว</option>
                                        <option value="replied" <?php echo $filter === 'replied' ? 'selected' : ''; ?>>ตอบกลับแล้ว</option>
                                    </select>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i>
                                    </button>
                                </form>
                            </div>
                            <div class="col-md-6">
                                <form method="GET" class="d-flex gap-2">
                                    <input type="hidden" name="filter" value="<?php echo htmlspecialchars($filter); ?>">
                                    <input type="text" name="search" class="form-control"
                                           placeholder="ค้นหาข้อความ..." value="<?php echo htmlspecialchars($search); ?>">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Messages -->
                        <?php if (empty($messages)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">ไม่มีข้อความ</h5>
                        </div>
                        <?php else: ?>
                        <div class="list-group">
                            <?php foreach ($messages as $msg): ?>
                            <div class="list-group-item message-item <?php echo $msg['status'] === 'unread' ? 'unread' : ''; ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="mb-0">
                                                <strong><?php echo htmlspecialchars($msg['name']); ?></strong>
                                                <small class="text-muted">(<?php echo htmlspecialchars($msg['email']); ?>)</small>
                                            </h6>
                                            <span class="badge status-badge bg-<?php echo $msg['status'] === 'unread' ? 'danger' : ($msg['status'] === 'read' ? 'warning' : 'success'); ?>">
                                                <?php echo $msg['status'] === 'unread' ? 'ยังไม่อ่าน' : ($msg['status'] === 'read' ? 'อ่านแล้ว' : 'ตอบกลับแล้ว'); ?>
                                            </span>
                                        </div>

                                        <p class="mb-2"><strong>หัวข้อ:</strong> <?php echo htmlspecialchars($msg['subject']); ?></p>

                                        <div class="message-content">
                                            <p class="text-muted mb-2"><?php echo nl2br(htmlspecialchars(substr($msg['message'], 0, 200))); ?>
                                            <?php if (strlen($msg['message']) > 200): ?>...<?php endif; ?></p>
                                        </div>

                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i><?php echo formatDate($msg['created_at']); ?>
                                        </small>
                                    </div>

                                    <div class="ms-3">
                                        <a href="messages.php?action=view&id=<?php echo $msg['id']; ?>"
                                           class="btn btn-primary btn-action">
                                            <i class="fas fa-eye"></i> ดู
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                        <nav class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                                <?php endfor; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function showReplyForm() {
            document.getElementById('replyForm').style.display = 'block';
            document.querySelector('textarea[name="reply_text"]').focus();
        }

        function hideReplyForm() {
            document.getElementById('replyForm').style.display = 'none';
        }

        // Auto-refresh unread count every 30 seconds
        setInterval(function() {
            fetch('api/get_unread_count.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update unread count in navigation if exists
                        const badge = document.querySelector('.unread-count');
                        if (badge) {
                            badge.textContent = data.count;
                            badge.style.display = data.count > 0 ? 'inline' : 'none';
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
        }, 30000);
    </script>
</body>
</html>