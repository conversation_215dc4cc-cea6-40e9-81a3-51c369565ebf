<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบแบนเนอร์ GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link href="assets/css/banner.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Kanit', sans-serif;
        }
        
        .test-section {
            padding: 40px 0;
            background: #f8f9fa;
        }
        
        .banner-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .btn-hover-effect {
            transition: all 0.3s ease;
        }
        
        .btn-hover-effect:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .counter {
            font-weight: 700;
            color: white;
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: #ee501b !important;
        }
        
        .nav-link {
            color: #2c3e50 !important;
            font-weight: 500;
        }
        
        .nav-link:hover {
            color: #ee501b !important;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#products">สินค้า</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#design">ออกแบบ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">ติดต่อ</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Banner -->
    <section class="hero-banner" id="home" data-aos="fade-in">
        <div class="hero-content">
            <div class="hero-badge mb-3" data-aos="fade-up" data-aos-delay="200">
                <span class="badge bg-light text-primary px-3 py-2 rounded-pill">
                    <i class="fas fa-star me-1"></i>ผู้เชี่ยวชาญมากกว่า 10 ปี
                </span>
            </div>
            
            <h1 class="hero-title" data-aos="fade-up" data-aos-delay="400">
                GT SPORT DESIGN
            </h1>
            
            <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="600">
                ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง<br>
                ด้วยประสบการณ์กว่า 10 ปี พร้อมบริการครบวงจร
            </p>
            
            <!-- Stats -->
            <div class="row g-3 mb-4" data-aos="fade-up" data-aos-delay="800">
                <div class="col-4">
                    <div class="text-center">
                        <h3 class="mb-0 counter" data-target="1000">0</h3>
                        <small>ลูกค้าพึงพอใจ</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="text-center">
                        <h3 class="mb-0 counter" data-target="50">0</h3>
                        <small>แบบเสื้อ</small>
                    </div>
                </div>
                <div class="col-4">
                    <div class="text-center">
                        <h3 class="mb-0">24/7</h3>
                        <small>บริการ</small>
                    </div>
                </div>
            </div>
            
            <!-- Buttons -->
            <div class="hero-buttons mb-4" data-aos="fade-up" data-aos-delay="1000">
                <a href="#design" class="btn btn-light btn-lg me-3 btn-hover-effect">
                    <i class="fas fa-paint-brush me-2"></i>เริ่มออกแบบฟรี
                </a>
                <a href="#products" class="btn btn-outline-light btn-lg btn-hover-effect">
                    <i class="fas fa-shopping-cart me-2"></i>ดูสินค้า
                </a>
            </div>
            
            <!-- Features -->
            <div class="d-flex flex-wrap gap-3" data-aos="fade-up" data-aos-delay="1200">
                <div class="d-flex align-items-center">
                    <i class="fas fa-shipping-fast me-2"></i>
                    <small>จัดส่งฟรี</small>
                </div>
                <div class="d-flex align-items-center">
                    <i class="fas fa-medal me-2"></i>
                    <small>คุณภาพพรีเมียม</small>
                </div>
                <div class="d-flex align-items-center">
                    <i class="fas fa-headset me-2"></i>
                    <small>ปรึกษาฟรี 24/7</small>
                </div>
            </div>
        </div>
    </section>

    <!-- Test Section -->
    <section class="test-section">
        <div class="container">
            <div class="text-center mb-5">
                <h2>🎨 ทดสอบแบนเนอร์ GT Sport Design</h2>
                <p class="lead">แบนเนอร์ที่สร้างขึ้นพร้อม Responsive Design</p>
            </div>
            
            <div class="row">
                <div class="col-lg-6">
                    <div class="banner-info">
                        <h4><i class="fas fa-desktop me-2"></i>Desktop Banner</h4>
                        <p><strong>ขนาด:</strong> 1200 x 400 px</p>
                        <p><strong>ไฟล์:</strong> main-banner-desktop.jpg</p>
                        <p>แสดงบนหน้าจอขนาดใหญ่ (มากกว่า 1024px)</p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="banner-info">
                        <h4><i class="fas fa-tablet-alt me-2"></i>Tablet Banner</h4>
                        <p><strong>ขนาด:</strong> 800 x 300 px</p>
                        <p><strong>ไฟล์:</strong> main-banner-tablet.jpg</p>
                        <p>แสดงบนแท็บเล็ต (768px - 1024px)</p>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-lg-6">
                    <div class="banner-info">
                        <h4><i class="fas fa-mobile-alt me-2"></i>Mobile Banner</h4>
                        <p><strong>ขนาด:</strong> 600 x 250 px</p>
                        <p><strong>ไฟล์:</strong> main-banner-mobile.jpg</p>
                        <p>แสดงบนมือถือ (น้อยกว่า 768px)</p>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="banner-info">
                        <h4><i class="fas fa-tv me-2"></i>Full HD Banner</h4>
                        <p><strong>ขนาด:</strong> 1920 x 600 px</p>
                        <p><strong>ไฟล์:</strong> hero-banner-fullhd.jpg</p>
                        <p>แสดงบนหน้าจอ Full HD (1920px ขึ้นไป)</p>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle me-2"></i>แบนเนอร์พร้อมใช้งาน!</h5>
                    <p class="mb-0">ลองปรับขนาดหน้าจอเพื่อดูการเปลี่ยนแปลงของแบนเนอร์</p>
                </div>
                
                <div class="mt-3">
                    <a href="index.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-home me-2"></i>ไปหน้าแรก
                    </a>
                    <a href="create_banner.php" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-cog me-2"></i>สร้างแบนเนอร์ใหม่
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });

        // Counter animation
        function animateCounters() {
            const counters = document.querySelectorAll('.counter');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target + '+';
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current) + '+';
                    }
                }, 20);
            });
        }

        // Trigger counter animation when hero banner is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        const heroBanner = document.querySelector('.hero-banner');
        if (heroBanner) {
            observer.observe(heroBanner);
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar background on scroll
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 100) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });
    </script>
</body>
</html>
