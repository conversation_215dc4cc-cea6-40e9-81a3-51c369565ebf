<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

$success = '';
$error = '';

// ดึงข้อมูลคำสั่งซื้อที่กำลังผลิต
$orders = [];
try {
    $stmt = $pdo->query("
        SELECT o.*, c.name as customer_name, c.phone as customer_phone, c.email as customer_email
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.status = 'processing'
        ORDER BY o.created_at ASC
    ");
    $orders = $stmt->fetchAll();
} catch (Exception $e) {
    // ข้อมูลจำลอง
    $orders = [
        [
            'id' => 3,
            'order_number' => 'ORD-2024-003',
            'customer_name' => 'นาย ค มีสุข',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'total_amount' => 1200.00,
            'status' => 'processing',
            'payment_status' => 'paid',
            'production_start_date' => date('Y-m-d'),
            'estimated_completion' => date('Y-m-d', strtotime('+7 days')),
            'progress_percentage' => 30,
            'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
            'notes' => 'ชุดทีมบาสเกตบอล 12 ตัว สีน้ำเงิน-ขาว'
        ],
        [
            'id' => 4,
            'order_number' => 'ORD-2024-004',
            'customer_name' => 'นางสาว ง สุขใจ',
            'customer_phone' => '************',
            'customer_email' => '<EMAIL>',
            'total_amount' => 850.00,
            'status' => 'processing',
            'payment_status' => 'paid',
            'production_start_date' => date('Y-m-d', strtotime('-1 day')),
            'estimated_completion' => date('Y-m-d', strtotime('+5 days')),
            'progress_percentage' => 60,
            'created_at' => date('Y-m-d H:i:s', strtotime('-4 days')),
            'notes' => 'เสื้อโปโลบริษัท 20 ตัว'
        ]
    ];
}

// จัดการการอัปเดตสถานะ
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $order_id = (int)($_POST['order_id'] ?? 0);

    if ($action === 'update_progress' && $order_id > 0) {
        $progress = (int)($_POST['progress_percentage'] ?? 0);
        $notes = trim($_POST['production_notes'] ?? '');

        try {
            $stmt = $pdo->prepare("
                UPDATE orders
                SET progress_percentage = ?, production_notes = ?, updated_at = NOW(), updated_by = ?
                WHERE id = ? AND status = 'processing'
            ");
            $stmt->execute([$progress, $notes, $_SESSION['admin_id'], $order_id]);

            $success = 'อัปเดตความคืบหน้าเรียบร้อยแล้ว';
        } catch (Exception $e) {
            $error = 'ไม่สามารถอัปเดตความคืบหน้าได้: ' . $e->getMessage();
        }
    }

    if ($action === 'complete_order' && $order_id > 0) {
        try {
            $stmt = $pdo->prepare("
                UPDATE orders
                SET status = 'completed', progress_percentage = 100, completed_at = NOW(), updated_at = NOW(), updated_by = ?
                WHERE id = ? AND status = 'processing'
            ");
            $stmt->execute([$_SESSION['admin_id'], $order_id]);

            $success = 'ทำเครื่องหมายเสร็จสิ้นเรียบร้อยแล้ว';
        } catch (Exception $e) {
            $error = 'ไม่สามารถทำเครื่องหมายเสร็จสิ้นได้: ' . $e->getMessage();
        }
    }

    // รีเฟรชหน้า
    if ($success || $error) {
        header('Location: orders_processing.php?msg=' . urlencode($success ?: $error) . '&type=' . ($success ? 'success' : 'error'));
        exit();
    }
}

// แสดงข้อความ
if (isset($_GET['msg'])) {
    $message = $_GET['msg'];
    $message_type = $_GET['type'] ?? 'info';
    if ($message_type === 'success') {
        $success = $message;
    } else {
        $error = $message;
    }
}

function getProgressColor($percentage) {
    if ($percentage >= 80) return 'success';
    if ($percentage >= 50) return 'info';
    if ($percentage >= 25) return 'warning';
    return 'danger';
}

function getDaysRemaining($estimated_date) {
    $today = new DateTime();
    $estimated = new DateTime($estimated_date);
    $diff = $today->diff($estimated);

    if ($estimated < $today) {
        return ['text' => 'เกินกำหนด ' . $diff->days . ' วัน', 'class' => 'danger'];
    } else {
        return ['text' => 'เหลือ ' . $diff->days . ' วัน', 'class' => 'info'];
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>คำสั่งซื้อกำลังผลิต - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        .order-card {
            border-left: 5px solid #17a2b8;
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        .btn-primary {
            background: #ee501b;
            border-color: #ee501b;
        }
        .btn-primary:hover {
            background: #d63916;
            border-color: #d63916;
        }
        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php" class="active"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">คำสั่งซื้อกำลังผลิต</h4>
                <small class="text-muted">ติดตามความคืบหน้าการผลิต</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="dashboard.php">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="orders.php">คำสั่งซื้อ</a></li>
                    <li class="breadcrumb-item active">กำลังผลิต</li>
                </ol>
            </nav>

            <!-- Order Status Tabs -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <a href="orders_pending.php" class="btn btn-outline-warning w-100">
                                <i class="fas fa-clock me-2"></i>รอดำเนินการ
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders_processing.php" class="btn btn-info w-100">
                                <i class="fas fa-cogs me-2"></i>กำลังผลิต
                                <span class="badge bg-white text-info ms-2"><?php echo count($orders); ?></span>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders_completed.php" class="btn btn-outline-success w-100">
                                <i class="fas fa-check me-2"></i>เสร็จสิ้น
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-list me-2"></i>ทั้งหมด
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Orders List -->
            <?php if (empty($orders)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                    <h3>ไม่มีคำสั่งซื้อที่กำลังผลิต</h3>
                    <p class="text-muted">คำสั่งซื้อที่ยืนยันแล้วจะแสดงที่นี่</p>
                </div>
            <?php else: ?>
                <?php foreach ($orders as $order): ?>
                    <?php
                    $progress_color = getProgressColor($order['progress_percentage'] ?? 0);
                    $days_info = getDaysRemaining($order['estimated_completion'] ?? date('Y-m-d', strtotime('+7 days')));
                    ?>
                <div class="card order-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-2 text-center">
                                <div class="progress-circle bg-<?php echo $progress_color; ?>">
                                    <?php echo $order['progress_percentage'] ?? 0; ?>%
                                </div>
                                <small class="text-muted mt-2 d-block">ความคืบหน้า</small>
                            </div>
                            <div class="col-md-6">
                                <h6 class="mb-2"><?php echo htmlspecialchars($order['order_number']); ?></h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>ลูกค้า:</strong> <?php echo htmlspecialchars($order['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                        <strong>ยอดรวม:</strong> ฿<?php echo number_format($order['total_amount'], 2); ?><br>
                                        <strong>เริ่มผลิต:</strong> <?php echo date('d/m/Y', strtotime($order['production_start_date'] ?? $order['created_at'])); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>กำหนดเสร็จ:</strong> <?php echo date('d/m/Y', strtotime($order['estimated_completion'] ?? date('Y-m-d', strtotime('+7 days')))); ?><br>
                                        <span class="badge bg-<?php echo $days_info['class']; ?>"><?php echo $days_info['text']; ?></span><br>
                                        <?php if (!empty($order['notes'])): ?>
                                            <small class="text-muted"><?php echo htmlspecialchars($order['notes']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div class="mt-3">
                                    <div class="progress" style="height: 8px;">
                                        <div class="progress-bar bg-<?php echo $progress_color; ?>"
                                             style="width: <?php echo $order['progress_percentage'] ?? 0; ?>%"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group-vertical w-100">
                                    <button class="btn btn-outline-primary btn-sm mb-2" data-bs-toggle="modal" data-bs-target="#progressModal<?php echo $order['id']; ?>">
                                        <i class="fas fa-edit me-1"></i>อัปเดตความคืบหน้า
                                    </button>
                                    <button class="btn btn-outline-info btn-sm mb-2" data-bs-toggle="modal" data-bs-target="#orderDetailModal<?php echo $order['id']; ?>">
                                        <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                    </button>
                                    <?php if (($order['progress_percentage'] ?? 0) >= 100): ?>
                                    <button class="btn btn-success btn-sm" onclick="completeOrder(<?php echo $order['id']; ?>)">
                                        <i class="fas fa-check me-1"></i>ทำเครื่องหมายเสร็จสิ้น
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Update Modal -->
                <div class="modal fade" id="progressModal<?php echo $order['id']; ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">อัปเดตความคืบหน้า: <?php echo htmlspecialchars($order['order_number']); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form method="POST">
                                <div class="modal-body">
                                    <input type="hidden" name="action" value="update_progress">
                                    <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">

                                    <div class="mb-3">
                                        <label class="form-label">ความคืบหน้า (%)</label>
                                        <input type="range" name="progress_percentage" class="form-range"
                                               min="0" max="100" value="<?php echo $order['progress_percentage'] ?? 0; ?>"
                                               oninput="updateProgressValue(this, <?php echo $order['id']; ?>)">
                                        <div class="d-flex justify-content-between">
                                            <small>0%</small>
                                            <span id="progressValue<?php echo $order['id']; ?>" class="fw-bold">
                                                <?php echo $order['progress_percentage'] ?? 0; ?>%
                                            </span>
                                            <small>100%</small>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">หมายเหตุการผลิต</label>
                                        <textarea name="production_notes" class="form-control" rows="3"
                                                  placeholder="อัปเดตสถานะการผลิต..."><?php echo htmlspecialchars($order['production_notes'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                    <button type="submit" class="btn btn-primary">บันทึก</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Order Detail Modal -->
                <div class="modal fade" id="orderDetailModal<?php echo $order['id']; ?>" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">รายละเอียดคำสั่งซื้อ: <?php echo htmlspecialchars($order['order_number']); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>ข้อมูลลูกค้า</h6>
                                        <p>
                                            <strong>ชื่อ:</strong> <?php echo htmlspecialchars($order['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                            <strong>โทร:</strong> <?php echo htmlspecialchars($order['customer_phone'] ?? 'ไม่ระบุ'); ?><br>
                                            <strong>อีเมล:</strong> <?php echo htmlspecialchars($order['customer_email'] ?? 'ไม่ระบุ'); ?>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>ข้อมูลการผลิต</h6>
                                        <p>
                                            <strong>เริ่มผลิต:</strong> <?php echo date('d/m/Y', strtotime($order['production_start_date'] ?? $order['created_at'])); ?><br>
                                            <strong>กำหนดเสร็จ:</strong> <?php echo date('d/m/Y', strtotime($order['estimated_completion'] ?? date('Y-m-d', strtotime('+7 days')))); ?><br>
                                            <strong>ความคืบหน้า:</strong> <?php echo $order['progress_percentage'] ?? 0; ?>%
                                        </p>
                                    </div>
                                </div>

                                <?php if (!empty($order['notes'])): ?>
                                <div class="mt-3">
                                    <h6>หมายเหตุคำสั่งซื้อ</h6>
                                    <p class="text-muted"><?php echo htmlspecialchars($order['notes']); ?></p>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($order['production_notes'])): ?>
                                <div class="mt-3">
                                    <h6>หมายเหตุการผลิต</h6>
                                    <p class="text-muted"><?php echo htmlspecialchars($order['production_notes']); ?></p>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#progressModal<?php echo $order['id']; ?>" data-bs-dismiss="modal">
                                    <i class="fas fa-edit me-1"></i>อัปเดตความคืบหน้า
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Complete Form -->
    <form id="completeForm" method="POST" style="display: none;">
        <input type="hidden" name="action" value="complete_order">
        <input type="hidden" name="order_id" id="completeOrderId">
    </form>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateProgressValue(slider, orderId) {
            document.getElementById('progressValue' + orderId).textContent = slider.value + '%';
        }

        function completeOrder(orderId) {
            if (confirm('คุณต้องการทำเครื่องหมายคำสั่งซื้อนี้เป็นเสร็จสิ้นหรือไม่?')) {
                document.getElementById('completeOrderId').value = orderId;
                document.getElementById('completeForm').submit();
            }
        }
    </script>
</body>
</html>