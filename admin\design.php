<?php
session_start();

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['admin_logged_in'])) {
    header("Location: login.php");
    exit;
}

require_once '../config/database.php';
$pdo = getDbConnection();

// จัดการการอัพเดตสถานะดีไซน์
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $design_id = (int)$_POST['design_id'];
    $action = $_POST['action'];

    try {
        switch($action) {
            case 'approve':
                $stmt = $pdo->prepare("UPDATE designs SET status = 'approved', admin_notes = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$_POST['admin_notes'] ?? '', $design_id]);
                $message = "อนุมัติดีไซน์สำเร็จ";
                break;

            case 'reject':
                $stmt = $pdo->prepare("UPDATE designs SET status = 'rejected', admin_notes = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$_POST['admin_notes'] ?? '', $design_id]);
                $message = "ปฏิเสธดีไซน์สำเร็จ";
                break;

            case 'request_changes':
                $stmt = $pdo->prepare("UPDATE designs SET status = 'changes_requested', admin_notes = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$_POST['admin_notes'] ?? '', $design_id]);
                $message = "ส่งคำขอแก้ไขดีไซน์สำเร็จ";
                break;

            case 'in_production':
                $stmt = $pdo->prepare("UPDATE designs SET status = 'in_production', admin_notes = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$_POST['admin_notes'] ?? '', $design_id]);
                $message = "อัพเดตสถานะการผลิตสำเร็จ";
                break;
        }

        $_SESSION['message'] = $message;
        $_SESSION['message_type'] = 'success';

    } catch (Exception $e) {
        $_SESSION['message'] = "เกิดข้อผิดพลาด: " . $e->getMessage();
        $_SESSION['message_type'] = 'error';
    }

    header("Location: designs.php");
    exit;
}

// ค้นหาและกรองข้อมูล
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// สร้าง query สำหรับค้นหา
$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(d.design_name LIKE ? OR c.name LIKE ? OR c.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($status_filter) {
    $where_conditions[] = "d.status = ?";
    $params[] = $status_filter;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// ดึงข้อมูลดีไซน์
$stmt = $pdo->prepare("
    SELECT
        d.*,
        c.name as customer_name,
        c.email as customer_email,
        c.phone as customer_phone,
        p.name as product_name,
        p.base_price
    FROM designs d
    LEFT JOIN customers c ON d.customer_id = c.id
    LEFT JOIN products p ON d.product_id = p.id
    $where_clause
    ORDER BY d.created_at DESC
    LIMIT $limit OFFSET $offset
");
$stmt->execute($params);
$designs = $stmt->fetchAll();

// นับจำนวนทั้งหมด
$count_stmt = $pdo->prepare("
    SELECT COUNT(*) FROM designs d
    LEFT JOIN customers c ON d.customer_id = c.id
    $where_clause
");
$count_stmt->execute($params);
$total_designs = $count_stmt->fetchColumn();
$total_pages = ceil($total_designs / $limit);

// สถิติดีไซน์
$stats_stmt = $pdo->query("
    SELECT
        status,
        COUNT(*) as count
    FROM designs
    GROUP BY status
");
$stats = [];
while ($row = $stats_stmt->fetch()) {
    $stats[$row['status']] = $row['count'];
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการการออกแบบ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Kanit', sans-serif;
            background: #f8f9fa;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 600;
            color: white !important;
        }

        .sidebar {
            min-height: calc(100vh - 76px);
            background: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: #495057;
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background: #e9ecef;
            color: #495057;
        }

        .sidebar .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px 20px;
        }

        .stats-card {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            color: white;
            margin-bottom: 20px;
        }

        .stats-pending { background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%); }
        .stats-approved { background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%); }
        .stats-rejected { background: linear-gradient(135deg, #ef5350 0%, #f44336 100%); }
        .stats-production { background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%); }

        .design-card {
            margin-bottom: 20px;
            border-radius: 15px;
            overflow: hidden;
        }

        .design-preview {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 10px;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-approved { background: #d1ecf1; color: #0c5460; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .status-changes_requested { background: #e2e3e5; color: #383d41; }
        .status-in_production { background: #cce5ff; color: #004085; }

        .btn-action {
            margin: 2px;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 12px;
        }

        .design-details {
            font-size: 14px;
        }

        .modal-content {
            border-radius: 15px;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-tshirt me-2"></i>GT Sport Design Admin
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link text-white" href="logout.php">
                    <i class="fas fa-sign-out-alt"></i> ออกจากระบบ
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <nav class="nav flex-column py-3">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-chart-bar me-2"></i>แดชบอร์ด
                        </a>
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-box me-2"></i>จัดการสินค้า
                        </a>
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>คำสั่งซื้อ
                        </a>
                        <a class="nav-link active" href="designs.php">
                            <i class="fas fa-palette me-2"></i>จัดการการออกแบบ
                        </a>
                        <a class="nav-link" href="customers.php">
                            <i class="fas fa-users me-2"></i>ลูกค้า
                        </a>
                        <a class="nav-link" href="chat.php">
                            <i class="fas fa-comments me-2"></i>แชทสด
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <div class="main-content">
                    <!-- แสดงข้อความแจ้งเตือน -->
                    <?php if (isset($_SESSION['message'])): ?>
                        <div class="alert alert-<?= $_SESSION['message_type'] === 'success' ? 'success' : 'danger' ?> alert-dismissible fade show" role="alert">
                            <?= htmlspecialchars($_SESSION['message']) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php
                        unset($_SESSION['message']);
                        unset($_SESSION['message_type']);
                        ?>
                    <?php endif; ?>

                    <!-- สถิติ -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="stats-card stats-pending">
                                <h4><?= $stats['pending'] ?? 0 ?></h4>
                                <p class="mb-0">รอตรวจสอบ</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card stats-approved">
                                <h4><?= $stats['approved'] ?? 0 ?></h4>
                                <p class="mb-0">อนุมัติแล้ว</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card stats-production">
                                <h4><?= $stats['in_production'] ?? 0 ?></h4>
                                <p class="mb-0">กำลังผลิต</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card stats-rejected">
                                <h4><?= $stats['rejected'] ?? 0 ?></h4>
                                <p class="mb-0">ถูกปฏิเสธ</p>
                            </div>
                        </div>
                    </div>

                    <!-- ค้นหาและกรอง -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-palette me-2"></i>จัดการการออกแบบ</h5>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row g-3 mb-4">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" name="search" value="<?= htmlspecialchars($search) ?>"
                                           placeholder="ค้นหาชื่อดีไซน์, ลูกค้า, อีเมล...">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" name="status">
                                        <option value="">ทุกสถานะ</option>
                                        <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>รอตรวจสอบ</option>
                                        <option value="approved" <?= $status_filter === 'approved' ? 'selected' : '' ?>>อนุมัติแล้ว</option>
                                        <option value="rejected" <?= $status_filter === 'rejected' ? 'selected' : '' ?>>ถูกปฏิเสธ</option>
                                        <option value="changes_requested" <?= $status_filter === 'changes_requested' ? 'selected' : '' ?>>ขอแก้ไข</option>
                                        <option value="in_production" <?= $status_filter === 'in_production' ? 'selected' : '' ?>>กำลังผลิต</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-1"></i>ค้นหา
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <a href="designs.php" class="btn btn-outline-secondary w-100">
                                        <i class="fas fa-refresh me-1"></i>รีเซ็ต
                                    </a>
                                </div>
                            </form>

                            <!-- รายการดีไซน์ -->
                            <?php if (empty($designs)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-palette fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">ไม่พบข้อมูลการออกแบบ</h5>
                                    <p class="text-muted">ยังไม่มีลูกค้าส่งการออกแบบเข้ามา</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($designs as $design): ?>
                                    <div class="card design-card">
                                        <div class="card-body">
                                            <div class="row align-items-center">
                                                <div class="col-md-2">
                                                    <?php if ($design['preview_image']): ?>
                                                        <img src="<?= htmlspecialchars($design['preview_image']) ?>"
                                                             alt="Preview" class="design-preview">
                                                    <?php else: ?>
                                                        <div class="design-preview bg-light d-flex align-items-center justify-content-center">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="col-md-4">
                                                    <h6 class="mb-1"><?= htmlspecialchars($design['design_name']) ?></h6>
                                                    <p class="design-details text-muted mb-1">
                                                        <i class="fas fa-user me-1"></i>
                                                        <?= htmlspecialchars($design['customer_name'] ?? 'ไม่ระบุ') ?>
                                                    </p>
                                                    <p class="design-details text-muted mb-1">
                                                        <i class="fas fa-tshirt me-1"></i>
                                                        <?= htmlspecialchars($design['product_name']) ?>
                                                    </p>
                                                    <p class="design-details text-muted mb-0">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <?= date('d/m/Y H:i', strtotime($design['created_at'])) ?>
                                                    </p>
                                                </div>

                                                <div class="col-md-2">
                                                    <span class="status-badge status-<?= $design['status'] ?>">
                                                        <?php
                                                        $status_text = [
                                                            'pending' => 'รอตรวจสอบ',
                                                            'approved' => 'อนุมัติแล้ว',
                                                            'rejected' => 'ถูกปฏิเสธ',
                                                            'changes_requested' => 'ขอแก้ไข',
                                                            'in_production' => 'กำลังผลิต'
                                                        ];
                                                        echo $status_text[$design['status']] ?? $design['status'];
                                                        ?>
                                                    </span>
                                                </div>

                                                <div class="col-md-4 text-end">
                                                    <button class="btn btn-outline-primary btn-action"
                                                            onclick="viewDesign(<?= $design['id'] ?>)">
                                                        <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                                                    </button>

                                                    <?php if ($design['status'] === 'pending'): ?>
                                                        <button class="btn btn-success btn-action"
                                                                onclick="updateStatus(<?= $design['id'] ?>, 'approve')">
                                                            <i class="fas fa-check me-1"></i>อนุมัติ
                                                        </button>
                                                        <button class="btn btn-warning btn-action"
                                                                onclick="updateStatus(<?= $design['id'] ?>, 'request_changes')">
                                                            <i class="fas fa-edit me-1"></i>ขอแก้ไข
                                                        </button>
                                                        <button class="btn btn-danger btn-action"
                                                                onclick="updateStatus(<?= $design['id'] ?>, 'reject')">
                                                            <i class="fas fa-times me-1"></i>ปฏิเสธ
                                                        </button>
                                                    <?php elseif ($design['status'] === 'approved'): ?>
                                                        <button class="btn btn-info btn-action"
                                                                onclick="updateStatus(<?= $design['id'] ?>, 'in_production')">
                                                            <i class="fas fa-cog me-1"></i>เริ่มผลิต
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                            <?php if ($design['admin_notes']): ?>
                                                <div class="row mt-2">
                                                    <div class="col-12">
                                                        <div class="alert alert-info py-2 mb-0">
                                                            <strong>หมายเหตุ:</strong> <?= htmlspecialchars($design['admin_notes']) ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>

                                <!-- Pagination -->
                                <?php if ($total_pages > 1): ?>
                                    <nav aria-label="Page navigation">
                                        <ul class="pagination justify-content-center">
                                            <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                                    <a class="page-link" href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>">
                                                        <?= $i ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>
                                        </ul>
                                    </nav>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal สำหรับการอัพเดตสถานะ -->
    <div class="modal fade" id="statusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">อัพเดตสถานะ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="design_id" id="modalDesignId">
                        <input type="hidden" name="action" id="modalAction">

                        <div class="mb-3">
                            <label for="admin_notes" class="form-label">หมายเหตุ</label>
                            <textarea class="form-control" name="admin_notes" id="admin_notes" rows="3"
                                      placeholder="เพิ่มหมายเหตุ (ถ้ามี)"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                        <button type="submit" class="btn btn-primary" id="modalSubmitBtn">บันทึก</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal สำหรับดูรายละเอียดดีไซน์ -->
    <div class="modal fade" id="designDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">รายละเอียดการออกแบบ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="designDetailContent">
                    <!-- เนื้อหาจะถูกโหลดผ่าน JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateStatus(designId, action) {
            const modal = new bootstrap.Modal(document.getElementById('statusModal'));
            const actionTexts = {
                'approve': 'อนุมัติการออกแบบ',
                'reject': 'ปฏิเสธการออกแบบ',
                'request_changes': 'ขอแก้ไขการออกแบบ',
                'in_production': 'เริ่มกระบวนการผลิต'
            };

            document.getElementById('modalTitle').textContent = actionTexts[action];
            document.getElementById('modalDesignId').value = designId;
            document.getElementById('modalAction').value = action;
            document.getElementById('admin_notes').value = '';

            // เปลี่ยนสีปุ่มตาม action
            const submitBtn = document.getElementById('modalSubmitBtn');
            submitBtn.className = 'btn ';
            switch(action) {
                case 'approve': submitBtn.className += 'btn-success'; break;
                case 'reject': submitBtn.className += 'btn-danger'; break;
                case 'request_changes': submitBtn.className += 'btn-warning'; break;
                case 'in_production': submitBtn.className += 'btn-info'; break;
            }

            modal.show();
        }

        function viewDesign(designId) {
            // โหลดรายละเอียดการออกแบบผ่าน AJAX
            fetch(`design_detail.php?id=${designId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('designDetailContent').innerHTML = html;
                    const modal = new bootstrap.Modal(document.getElementById('designDetailModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
                });
        }
    </script>
</body>
</html>
