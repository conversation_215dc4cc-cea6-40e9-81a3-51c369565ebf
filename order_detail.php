<?php
session_start();

// ตรวจสอบการเข้าสู่ระบบ
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'customer') {
    header('Location: login.php');
    exit;
}

require_once 'includes/config.php';

$user_id = $_SESSION['user_id'];
$order_id = $_GET['id'] ?? 0;
$order = null;
$order_items = [];

// ดึงข้อมูลคำสั่งซื้อ
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        
        if (isset($pdo)) {
            // ดึงข้อมูลคำสั่งซื้อ
            $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ? AND customer_id = ?");
            $stmt->execute([$order_id, $user_id]);
            $order = $stmt->fetch();
            
            if ($order) {
                // ดึงรายการสินค้าในคำสั่งซื้อ
                $stmt = $pdo->prepare("
                    SELECT oi.*, p.name as product_name, p.image_path 
                    FROM order_items oi 
                    LEFT JOIN products p ON oi.product_id = p.id 
                    WHERE oi.order_id = ?
                ");
                $stmt->execute([$order_id]);
                $order_items = $stmt->fetchAll();
            }
        }
    }
} catch (Exception $e) {
    $error_message = 'เกิดข้อผิดพลาดในการโหลดข้อมูล';
}

// ถ้าไม่พบคำสั่งซื้อ
if (!$order) {
    header('Location: customer_orders.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>รายละเอียดคำสั่งซื้อ #<?php echo htmlspecialchars($order['order_number']); ?> - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>

<body>
<?php include 'includes/header.php'; ?>

<div class="container py-5">
    <!-- Back Button -->
    <div class="mb-4">
        <a href="customer_orders.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>กลับไปคำสั่งซื้อ
        </a>
    </div>

    <!-- Order Header -->
    <div class="card mb-4">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h4 class="mb-0">คำสั่งซื้อ #<?php echo htmlspecialchars($order['order_number']); ?></h4>
                    <p class="text-muted mb-0">วันที่สั่ง: <?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></p>
                </div>
                <div class="col-md-6 text-end">
                    <?php
                    $status_class = match($order['status']) {
                        'pending' => 'warning',
                        'processing' => 'info',
                        'shipped' => 'primary',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        default => 'secondary'
                    };
                    $status_text = match($order['status']) {
                        'pending' => 'รอดำเนินการ',
                        'processing' => 'กำลังผลิต',
                        'shipped' => 'จัดส่งแล้ว',
                        'completed' => 'เสร็จสิ้น',
                        'cancelled' => 'ยกเลิก',
                        default => 'ไม่ทราบสถานะ'
                    };
                    ?>
                    <span class="badge bg-<?php echo $status_class; ?> fs-6 px-3 py-2">
                        <?php echo $status_text; ?>
                    </span>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Order Items -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">รายการสินค้า</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($order_items)): ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>สินค้า</th>
                                    <th>ราคา</th>
                                    <th>จำนวน</th>
                                    <th>รวม</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($order_items as $item): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="product-image me-3">
                                                <?php if (!empty($item['image_path']) && file_exists($item['image_path'])): ?>
                                                <img src="<?php echo htmlspecialchars($item['image_path']); ?>" 
                                                     alt="<?php echo htmlspecialchars($item['product_name']); ?>" 
                                                     class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                <?php else: ?>
                                                <div class="placeholder d-flex align-items-center justify-content-center" 
                                                     style="width: 60px; height: 60px; background: var(--light-gray);">
                                                    <i class="fas fa-tshirt text-muted"></i>
                                                </div>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <h6 class="mb-1"><?php echo htmlspecialchars($item['product_name'] ?? 'สินค้า'); ?></h6>
                                                <?php if (!empty($item['product_options'])): ?>
                                                <small class="text-muted"><?php echo htmlspecialchars($item['product_options']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>฿<?php echo number_format($item['price'], 2); ?></td>
                                    <td><?php echo number_format($item['quantity']); ?></td>
                                    <td>฿<?php echo number_format($item['price'] * $item['quantity'], 2); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <p class="text-muted text-center py-4">ไม่พบรายการสินค้า</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Order Timeline -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">สถานะการสั่งซื้อ</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item <?php echo in_array($order['status'], ['pending', 'processing', 'shipped', 'completed']) ? 'completed' : ''; ?>">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h6>ได้รับคำสั่งซื้อ</h6>
                                <p class="text-muted small"><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></p>
                            </div>
                        </div>
                        
                        <div class="timeline-item <?php echo in_array($order['status'], ['processing', 'shipped', 'completed']) ? 'completed' : ''; ?>">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h6>กำลังผลิต</h6>
                                <p class="text-muted small">
                                    <?php echo $order['status'] !== 'pending' ? 'กำลังดำเนินการ' : 'รอดำเนินการ'; ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="timeline-item <?php echo in_array($order['status'], ['shipped', 'completed']) ? 'completed' : ''; ?>">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h6>จัดส่งสินค้า</h6>
                                <p class="text-muted small">
                                    <?php echo $order['status'] === 'shipped' || $order['status'] === 'completed' ? 'จัดส่งแล้ว' : 'ยังไม่จัดส่ง'; ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="timeline-item <?php echo $order['status'] === 'completed' ? 'completed' : ''; ?>">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h6>เสร็จสิ้น</h6>
                                <p class="text-muted small">
                                    <?php echo $order['status'] === 'completed' ? 'ส่งมอบเรียบร้อย' : 'ยังไม่เสร็จสิ้น'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="col-lg-4">
            <!-- Order Summary -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">สรุปคำสั่งซื้อ</h5>
                </div>
                <div class="card-body">
                    <div class="order-summary">
                        <div class="d-flex justify-content-between mb-2">
                            <span>ยอดรวมสินค้า:</span>
                            <span>฿<?php echo number_format($order['subtotal'] ?? $order['total_amount'], 2); ?></span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>ค่าจัดส่ง:</span>
                            <span>฿<?php echo number_format($order['shipping_cost'] ?? 0, 2); ?></span>
                        </div>
                        <?php if (!empty($order['discount_amount'])): ?>
                        <div class="d-flex justify-content-between mb-2 text-success">
                            <span>ส่วนลด:</span>
                            <span>-฿<?php echo number_format($order['discount_amount'], 2); ?></span>
                        </div>
                        <?php endif; ?>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>ยอดรวมทั้งสิ้น:</span>
                            <span class="text-primary">฿<?php echo number_format($order['total_amount'], 2); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">ข้อมูลการชำระเงิน</h5>
                </div>
                <div class="card-body">
                    <p><strong>วิธีชำระเงิน:</strong></p>
                    <p class="text-muted"><?php echo htmlspecialchars($order['payment_method'] ?? 'ไม่ระบุ'); ?></p>
                    
                    <p><strong>สถานะการชำระเงิน:</strong></p>
                    <?php
                    $payment_status_class = match($order['payment_status'] ?? 'pending') {
                        'paid' => 'success',
                        'pending' => 'warning',
                        'failed' => 'danger',
                        default => 'secondary'
                    };
                    $payment_status_text = match($order['payment_status'] ?? 'pending') {
                        'paid' => 'ชำระแล้ว',
                        'pending' => 'รอชำระเงิน',
                        'failed' => 'ชำระไม่สำเร็จ',
                        default => 'ไม่ทราบ'
                    };
                    ?>
                    <span class="badge bg-<?php echo $payment_status_class; ?>">
                        <?php echo $payment_status_text; ?>
                    </span>
                </div>
            </div>

            <!-- Shipping Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">ข้อมูลการจัดส่ง</h5>
                </div>
                <div class="card-body">
                    <p><strong>ที่อยู่จัดส่ง:</strong></p>
                    <p class="text-muted"><?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?></p>
                    
                    <?php if (!empty($order['tracking_number'])): ?>
                    <p><strong>เลขติดตามพัสดุ:</strong></p>
                    <p class="text-primary"><?php echo htmlspecialchars($order['tracking_number']); ?></p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($order['status'] === 'pending'): ?>
                        <button class="btn btn-outline-danger" onclick="cancelOrder(<?php echo $order['id']; ?>)">
                            <i class="fas fa-times me-2"></i>ยกเลิกคำสั่งซื้อ
                        </button>
                        <?php endif; ?>
                        
                        <?php if ($order['status'] === 'completed'): ?>
                        <a href="reorder.php?id=<?php echo $order['id']; ?>" class="btn btn-outline-success">
                            <i class="fas fa-redo me-2"></i>สั่งซื้อซ้ำ
                        </a>
                        <?php endif; ?>
                        
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>พิมพ์ใบสั่งซื้อ
                        </button>
                        
                        <a href="contact.php" class="btn btn-outline-info">
                            <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function cancelOrder(orderId) {
    if (confirm('คุณแน่ใจหรือไม่ที่จะยกเลิกคำสั่งซื้อนี้?')) {
        fetch('cancel_order.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ order_id: orderId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('ยกเลิกคำสั่งซื้อเรียบร้อยแล้ว');
                location.reload();
            } else {
                alert('เกิดข้อผิดพลาด: ' + data.message);
            }
        })
        .catch(error => {
            alert('เกิดข้อผิดพลาดในการยกเลิกคำสั่งซื้อ');
        });
    }
}
</script>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #dee2e6;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-item.completed .timeline-marker {
    background: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.timeline-content h6 {
    margin-bottom: 5px;
    color: var(--dark-color);
}

.order-summary {
    font-size: 0.95rem;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.card-header {
    background-color: var(--light-gray);
    border-bottom: 1px solid #eee;
}

@media print {
    .btn, .card-header, nav, footer {
        display: none !important;
    }
}
</style>

</body>
</html>
