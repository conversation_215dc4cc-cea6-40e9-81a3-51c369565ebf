<?php
session_start();
require_once '../config/database.php';
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: index.php');
    exit;
}

$page_title = "จัดการสินค้า";
$current_admin_page = 'products.php';

try {
    $db = getDbConnection();
    
    // Handle form submissions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if ($_POST['action'] === 'add_product') {
            $stmt = $db->prepare("
                INSERT INTO products (name, description, price, category, sizes, colors, is_active, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, 1, NOW())
            ");
            $stmt->execute([
                $_POST['name'],
                $_POST['description'],
                $_POST['price'],
                $_POST['category'],
                $_POST['sizes'],
                $_POST['colors']
            ]);
            $success_message = "เพิ่มสินค้าเรียบร้อย";
        }
        
        if ($_POST['action'] === 'update_product') {
            $stmt = $db->prepare("
                UPDATE products 
                SET name = ?, description = ?, price = ?, category = ?, sizes = ?, colors = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([
                $_POST['name'],
                $_POST['description'],
                $_POST['price'],
                $_POST['category'],
                $_POST['sizes'],
                $_POST['colors'],
                $_POST['product_id']
            ]);
            $success_message = "อัปเดตสินค้าเรียบร้อย";
        }
        
        if ($_POST['action'] === 'delete_product') {
            $stmt = $db->prepare("UPDATE products SET is_active = 0 WHERE id = ?");
            $stmt->execute([$_POST['product_id']]);
            $success_message = "ลบสินค้าเรียบร้อย";
        }
        
        if ($_POST['action'] === 'toggle_status') {
            $stmt = $db->prepare("UPDATE products SET is_active = ? WHERE id = ?");
            $stmt->execute([$_POST['status'], $_POST['product_id']]);
            $success_message = "เปลี่ยนสถานะสินค้าเรียบร้อย";
        }
    }
    
    // Get products
    $search = $_GET['search'] ?? '';
    $category_filter = $_GET['category'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    
    $where_conditions = [];
    $params = [];
    
    if ($search) {
        $where_conditions[] = "(name LIKE ? OR description LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    if ($category_filter) {
        $where_conditions[] = "category = ?";
        $params[] = $category_filter;
    }
    
    if ($status_filter !== '') {
        $where_conditions[] = "is_active = ?";
        $params[] = $status_filter;
    }
    
    $where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";
    
    $sql = "SELECT * FROM products $where_clause ORDER BY created_at DESC";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get categories
    $stmt = $db->query("SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != ''");
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Get product statistics
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_products,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_products,
            SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_products,
            AVG(price) as avg_price
        FROM products
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = "เกิดข้อผิดพลาด: " . $e->getMessage();
    $products = [];
    $categories = [];
    $stats = ['total_products' => 0, 'active_products' => 0, 'inactive_products' => 0, 'avg_price' => 0];
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-box"></i> จัดการสินค้า</h2>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#productModal" onclick="openProductModal()">
            <i class="fas fa-plus"></i> เพิ่มสินค้าใหม่
        </button>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-white bg-primary">
                <div class="card-body text-center">
                    <h4><?php echo number_format($stats['total_products']); ?></h4>
                    <small>สินค้าทั้งหมด</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-success">
                <div class="card-body text-center">
                    <h4><?php echo number_format($stats['active_products']); ?></h4>
                    <small>สินค้าที่เปิดใช้งาน</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-warning">
                <div class="card-body text-center">
                    <h4><?php echo number_format($stats['inactive_products']); ?></h4>
                    <small>สินค้าที่ปิดใช้งาน</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-white bg-info">
                <div class="card-body text-center">
                    <h4>฿<?php echo number_format($stats['avg_price']); ?></h4>
                    <small>ราคาเฉลี่ย</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">ค้นหา</label>
                    <input type="text" name="search" class="form-control" placeholder="ชื่อสินค้า หรือ คำอธิบาย" value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">หมวดหมู่</label>
                    <select name="category" class="form-select">
                        <option value="">ทั้งหมด</option>
                        <?php foreach ($categories as $category): ?>
                        <option value="<?php echo htmlspecialchars($category); ?>" <?php echo $category_filter === $category ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">สถานะ</label>
                    <select name="status" class="form-select">
                        <option value="">ทั้งหมด</option>
                        <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>เปิดใช้งาน</option>
                        <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>ปิดใช้งาน</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> ค้นหา
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="fas fa-list"></i> รายการสินค้า (<?php echo count($products); ?> รายการ)</h5>
        </div>
        <div class="card-body">
            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>รูปภาพ</th>
                            <th>ชื่อสินค้า</th>
                            <th>หมวดหมู่</th>
                            <th>ราคา</th>
                            <th>ไซส์</th>
                            <th>สี</th>
                            <th>สถานะ</th>
                            <th>วันที่สร้าง</th>
                            <th>การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($products)): ?>
                        <tr>
                            <td colspan="9" class="text-center text-muted py-4">ไม่พบสินค้า</td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($products as $product): ?>
                        <tr>
                            <td>
                                <?php if ($product['image_url']): ?>
                                <img src="<?php echo htmlspecialchars($product['image_url']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px; border-radius: 4px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div><strong><?php echo htmlspecialchars($product['name']); ?></strong></div>
                                <?php if ($product['description']): ?>
                                <small class="text-muted"><?php echo htmlspecialchars(substr($product['description'], 0, 50)); ?>...</small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($product['category'] ?? '-'); ?></td>
                            <td><strong>฿<?php echo number_format($product['price']); ?></strong></td>
                            <td>
                                <?php if ($product['sizes']): ?>
                                <small class="badge bg-secondary"><?php echo htmlspecialchars($product['sizes']); ?></small>
                                <?php else: ?>
                                <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($product['colors']): ?>
                                <small class="badge bg-info"><?php echo htmlspecialchars($product['colors']); ?></small>
                                <?php else: ?>
                                <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" <?php echo $product['is_active'] ? 'checked' : ''; ?> 
                                           onchange="toggleProductStatus(<?php echo $product['id']; ?>, this.checked)">
                                </div>
                            </td>
                            <td><?php echo date('d/m/Y', strtotime($product['created_at'])); ?></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="editProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="deleteProduct(<?php echo $product['id']; ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Product Modal -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productModalTitle">เพิ่มสินค้าใหม่</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="productForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add_product">
                    <input type="hidden" name="product_id" id="productId">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">ชื่อสินค้า <span class="text-danger">*</span></label>
                                <input type="text" name="name" id="productName" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">หมวดหมู่</label>
                                <input type="text" name="category" id="productCategory" class="form-control" list="categoryList">
                                <datalist id="categoryList">
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category); ?>">
                                    <?php endforeach; ?>
                                </datalist>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">คำอธิบาย</label>
                        <textarea name="description" id="productDescription" class="form-control" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">ราคา (บาท) <span class="text-danger">*</span></label>
                                <input type="number" name="price" id="productPrice" class="form-control" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">ไซส์</label>
                                <input type="text" name="sizes" id="productSizes" class="form-control" placeholder="S, M, L, XL">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">สี</label>
                                <input type="text" name="colors" id="productColors" class="form-control" placeholder="แดง, น้ำเงิน, เขียว">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-primary">บันทึก</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Open product modal for adding new product
function openProductModal() {
    document.getElementById('productModalTitle').textContent = 'เพิ่มสินค้าใหม่';
    document.getElementById('formAction').value = 'add_product';
    document.getElementById('productForm').reset();
    document.getElementById('productId').value = '';
}

// Edit product
function editProduct(product) {
    document.getElementById('productModalTitle').textContent = 'แก้ไขสินค้า';
    document.getElementById('formAction').value = 'update_product';
    document.getElementById('productId').value = product.id;
    document.getElementById('productName').value = product.name;
    document.getElementById('productCategory').value = product.category || '';
    document.getElementById('productDescription').value = product.description || '';
    document.getElementById('productPrice').value = product.price;
    document.getElementById('productSizes').value = product.sizes || '';
    document.getElementById('productColors').value = product.colors || '';
    
    new bootstrap.Modal(document.getElementById('productModal')).show();
}

// Toggle product status
function toggleProductStatus(productId, isActive) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="toggle_status">
        <input type="hidden" name="product_id" value="${productId}">
        <input type="hidden" name="status" value="${isActive ? 1 : 0}">
    `;
    document.body.appendChild(form);
    form.submit();
}

// Delete product
function deleteProduct(productId) {
    if (confirm('ต้องการลบสินค้านี้หรือไม่?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_product">
            <input type="hidden" name="product_id" value="${productId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

<?php include 'includes/footer.php'; ?>
