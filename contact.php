<?php
require_once './includes/db.php';

$message = '';
$success = false;

if ($_POST) {
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $subject = $_POST['subject'] ?? '';
    $message_text = $_POST['message'] ?? '';

    if ($name && $email && $message_text) {
        try {
            $db = getDB();
            // บันทึกข้อความติดต่อ (สร้างตารางง่ายๆ)
            $stmt = $db->prepare("
                INSERT INTO contact_messages (name, email, phone, subject, message, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE name=name
            ");
            // ใช้ ON DUPLICATE KEY เพื่อไม่ให้ error ถ้าตารางยังไม่มี
            $stmt->execute([$name, $email, $phone, $subject, $message_text]);

            $success = true;
            $message = "ส่งข้อความเรียบร้อยแล้ว เราจะติดต่อกลับไปในเร็วๆ นี้";
        } catch (Exception $e) {
            $message = "ส่งข้อความไม่สำเร็จ กรุณาลองใหม่อีกครั้ง";
        }
    } else {
        $message = "กรุณากรอกข้อมูลให้ครบถ้วน";
    }
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ติดต่อเรา - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #ee501b;
            --secondary-color: #ff6b35;
        }

        .navbar-brand { color: var(--primary-color) !important; font-weight: bold; font-size: 1.5rem; }
        .btn-primary { background: var(--primary-color); border: none; }
        .btn-primary:hover { background: #d44615; }
        .contact-card { border: none; box-shadow: 0 5px 15px rgba(0,0,0,0.1); border-radius: 15px; }
        .contact-icon { color: var(--primary-color); font-size: 2rem; }
        .map-container { height: 300px; border-radius: 15px; overflow: hidden; }
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 300px;
            max-height: 400px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 25px rgba(0,0,0,0.3);
            z-index: 1000;
            display: none;
        }
        .chat-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 1001;
        }
        .chat-header {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            border-radius: 15px 15px 0 0;
        }
        .chat-messages {
            height: 250px;
            overflow-y: auto;
            padding: 15px;
        }
        .contact-hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 80px 0;
        }
    </style>
</head>
<body>
<?php include './includes/header.php'; ?>

    <!-- Hero Section -->
    <section class="contact-hero">
        <div class="container">
            <div class="text-center">
                <h1 class="display-5 fw-bold mb-3">ติดต่อเรา</h1>
                <p class="lead">พร้อมให้คำปรึกษาและรับออกแบบเสื้อกีฬาตามความต้องการ</p>
            </div>
        </div>
    </section>

    <!-- Contact Information -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-5">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="contact-card card h-100 text-center p-4">
                        <div class="contact-icon mb-3">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h5>ที่อยู่</h5>
                        <p class="text-muted">339/7 ม.4 ต.บ้านดู่อ.เมือง<br>จ.เชียงราย ประเทศไทย</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="contact-card card h-100 text-center p-4">
                        <div class="contact-icon mb-3">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h5>โทรศัพท์</h5>
                        <p class="text-muted">
                            <a href="tel:0855599164" class="text-decoration-none">************</a><br>
                            <small>เปิดทุกวัน 8:00-18:00</small>
                        </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="contact-card card h-100 text-center p-4">
                        <div class="contact-icon mb-3">
                            <i class="fab fa-line"></i>
                        </div>
                        <h5>LINE</h5>
                        <p class="text-muted">
                            <a href="https://line.me/ti/p/@gtsport" target="_blank" class="text-decoration-none">@gtsport</a><br>
                            <small>แชทตลอด 24 ชม.</small>
                        </p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="contact-card card h-100 text-center p-4">
                        <div class="contact-icon mb-3">
                            <i class="fab fa-facebook"></i>
                        </div>
                        <h5>Facebook</h5>
                        <p class="text-muted">
                            <a href="https://www.facebook.com/GTSportDesign.1" target="_blank" class="text-decoration-none">GT Sport Design</a><br>
                            <small>ดูผลงานและรีวิว</small>
                        </p>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Contact Form -->
                <div class="col-lg-8 mb-4">
                    <div class="contact-card card p-4">
                        <h4 class="mb-4"><i class="fas fa-envelope"></i> ส่งข้อความหาเรา</h4>

                        <?php if ($message): ?>
                        <div class="alert <?= $success ? 'alert-success' : 'alert-danger' ?> alert-dismissible fade show">
                            <?= htmlspecialchars($message) ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        <?php endif; ?>

                        <form method="POST">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">ชื่อ-นามสกุล *</label>
                                    <input type="text" name="name" class="form-control" required
                                           value="<?= htmlspecialchars($_POST['name'] ?? '') ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">อีเมล *</label>
                                    <input type="email" name="email" class="form-control" required
                                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">เบอร์โทรศัพท์</label>
                                    <input type="tel" name="phone" class="form-control"
                                           value="<?= htmlspecialchars($_POST['phone'] ?? '') ?>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">หัวข้อ</label>
                                    <select name="subject" class="form-select">
                                        <option value="">เลือกหัวข้อ</option>
                                        <option value="สอบถามสินค้า" <?= ($_POST['subject'] ?? '') === 'สอบถามสินค้า' ? 'selected' : '' ?>>สอบถามสินค้า</option>
                                        <option value="ออกแบบเสื้อ" <?= ($_POST['subject'] ?? '') === 'ออกแบบเสื้อ' ? 'selected' : '' ?>>ออกแบบเสื้อ</option>
                                        <option value="สั่งผลิต" <?= ($_POST['subject'] ?? '') === 'สั่งผลิต' ? 'selected' : '' ?>>สั่งผลิต</option>
                                        <option value="ร้องเรียน" <?= ($_POST['subject'] ?? '') === 'ร้องเรียน' ? 'selected' : '' ?>>ร้องเรียน/แนะนำ</option>
                                        <option value="อื่นๆ" <?= ($_POST['subject'] ?? '') === 'อื่นๆ' ? 'selected' : '' ?>>อื่นๆ</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">ข้อความ *</label>
                                <textarea name="message" class="form-control" rows="5" required
                                          placeholder="รายละเอียดที่ต้องการสอบถาม..."><?= htmlspecialchars($_POST['message'] ?? '') ?></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane"></i> ส่งข้อความ
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Quick Contact -->
                <div class="col-lg-4">
                    <div class="contact-card card p-4 mb-4">
                        <h5 class="mb-3"><i class="fas fa-clock"></i> เวลาทำการ</h5>
                        <ul class="list-unstyled">
                            <li><strong>จันทร์ - เสาร์:</strong> 8:00 - 18:00</li>
                            <li><strong>อาทิทย์:</strong> 9:00 - 17:00</li>
                            <li><strong>วันหยุดนักขัตฤกษ์:</strong> ปิด</li>
                        </ul>
                        <hr>
                        <p class="text-muted mb-3">ติดต่อด่วน</p>
                        <div class="d-grid gap-2">
                            <a href="tel:0855599164" class="btn btn-success">
                                <i class="fas fa-phone"></i> โทรเลย
                            </a>
                            <a href="https://line.me/ti/p/@gtsport" target="_blank" class="btn btn-primary">
                                <i class="fab fa-line"></i> แชท LINE
                            </a>
                            <button class="btn btn-info" onclick="toggleChat()">
                                <i class="fas fa-comments"></i> แชทสด
                            </button>
                        </div>
                    </div>

                    <div class="contact-card card p-4">
                        <h5 class="mb-3"><i class="fas fa-question-circle"></i> คำถามที่พบบ่อย</h5>
                        <div class="accordion" id="faqAccordion">
                            <div class="accordion-item">
                                <h6 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#faq1">
                                        ใช้เวลาผลิตนานแค่ไหน?
                                    </button>
                                </h6>
                                <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        <small>โดยปกติใช้เวลา 7-14 วันทำการ ขึ้นอยู่กับจำนวนและความซับซ้อนของงาน</small>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h6 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#faq2">
                                        สั่งขั้นต่ำกี่ตัว?
                                    </button>
                                </h6>
                                <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        <small>สั่งขั้นต่ำ 10 ตัว สำหรับงานพิมพ์ และ 20 ตัว สำหรับงาน Fullprint</small>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h6 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#faq3">
                                        มีการรับประกันไหม?
                                    </button>
                                </h6>
                                <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        <small>รับประกันคุณภาพงาน 7 วัน หากมีปัญหาสามารถเคลมได้</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <h3 class="text-center mb-4">แผนที่ร้าน GT Sport Design</h3>
            <div class="map-container">
                <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3719.123456789!2d99.8123456!3d19.9123456!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTnCsDU0JzQ0LjQiTiA5OcKwNDgnNDQuNCJF!5e0!3m2!1sth!2sth!4v1234567890123!5m2!1sth!2sth"
                    width="100%"
                    height="100%"
                    style="border:0;"
                    allowfullscreen=""
                    loading="lazy">
                </iframe>
            </div>
            <div class="text-center mt-3">
                <a href="https://goo.gl/maps/example" target="_blank" class="btn btn-outline-primary">
                    <i class="fas fa-map-marker-alt"></i> เปิดใน Google Maps
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
   <?php include './includes/footer.php'; ?>

    <!-- Live Chat Widget -->
    <button class="chat-toggle" onclick="toggleChat()">
        <i class="fas fa-comments"></i>
    </button>

    <div class="chat-widget" id="chatWidget">
        <div class="chat-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-comments"></i> แชทสด</h6>
                <button class="btn btn-sm text-white" onclick="toggleChat()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="chat-messages" id="chatMessages">
            <div class="text-center text-muted py-3">
                <small>พิมพ์ข้อความเพื่อเริ่มการสนทนา</small>
            </div>
        </div>
        <div class="p-3">
            <div class="input-group">
                <input type="text" class="form-control" id="chatInput" placeholder="พิมพ์ข้อความ..."
                       onkeypress="if(event.key==='Enter') sendMessage()">
                <button class="btn btn-primary" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let chatOpen = false;

        function toggleChat() {
            const widget = document.getElementById('chatWidget');
            const toggle = document.querySelector('.chat-toggle');

            if (chatOpen) {
                widget.style.display = 'none';
                toggle.innerHTML = '<i class="fas fa-comments"></i>';
                chatOpen = false;
            } else {
                widget.style.display = 'block';
                toggle.innerHTML = '<i class="fas fa-times"></i>';
                chatOpen = true;

                // Auto message
                if (!localStorage.getItem('chatWelcome')) {
                    setTimeout(() => {
                        addMessage('GT Sport Design', 'สวัสดีครับ! มีอะไรให้เราช่วยไหมครับ?', 'admin');
                        localStorage.setItem('chatWelcome', 'shown');
                    }, 1000);
                }
            }
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message) {
                addMessage('คุณ', message, 'user');
                input.value = '';

                // Auto reply
                setTimeout(() => {
                    const replies = [
                        'ขอบคุณสำหรับข้อความครับ เราจะติดต่อกลับไปเร็วๆ นี้',
                        'สำหรับข้อมูลเพิ่มเติม สามารถโทร ************ ได้เลยครับ',
                        'หรือสามารถ LINE มาที่ @gtsport ได้ครับ'
                    ];
                    const reply = replies[Math.floor(Math.random() * replies.length)];
                    addMessage('GT Sport Design', reply, 'admin');
                }, 1500);
            }
        }

        function addMessage(sender, text, type) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `mb-2 ${type === 'user' ? 'text-end' : ''}`;

            messageDiv.innerHTML = `
                <div class="d-inline-block p-2 rounded ${type === 'user' ? 'bg-primary text-white' : 'bg-light'}">
                    <small class="fw-bold">${sender}</small><br>
                    <span>${text}</span>
                </div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Auto-resize contact form
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });
        });
    </script>
</body>
</html>
