<?php
session_start();

$success_message = '';
$error_message = '';

// ประมวลผลฟอร์ม
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // ตรวจสอบข้อมูล
    if (empty($name) || empty($email) || empty($message)) {
        $error_message = 'กรุณากรอกข้อมูลให้ครบถ้วน';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'รูปแบบอีเมลไม่ถูกต้อง';
    } else {
        // บันทึกข้อมูล (ในระบบจริงจะบันทึกลงฐานข้อมูลหรือส่งอีเมล)
        $success_message = 'ส่งข้อความเรียบร้อยแล้ว เราจะติดต่อกลับภายใน 24 ชั่วโมง';
        
        // ล้างข้อมูลฟอร์ม
        $name = $email = $phone = $subject = $message = '';
    }
}

// ข้อมูลติดต่อ
$contact_info = [
    'address' => '339/7 ม.4 ต.บ้าน จ.ราย',
    'phone' => '************',
    'email' => '<EMAIL>',
    'line' => '@gtsport',
    'facebook' => 'https://www.facebook.com/GTSportDesign.1',
    'hours' => [
        'จันทร์ - ศุกร์' => '08:00 - 18:00',
        'เสาร์ - อาทิตย์' => '09:00 - 17:00'
    ]
];
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>ติดต่อเรา - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="ติดต่อ GT Sport Design สำหรับการปรึกษาและสั่งทำเสื้อกีฬา โทร ************">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/banner.css" rel="stylesheet">
    
    <style>
        /* Contact Page Styles */
        .contact-hero {
            background: var(--gradient-primary);
            color: white;
            padding: 100px 0 60px;
            position: relative;
            overflow: hidden;
        }
        
        .contact-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle fill="rgba(255,255,255,0.1)" cx="200" cy="200" r="300"/><circle fill="rgba(255,255,255,0.05)" cx="800" cy="600" r="400"/></svg>');
            background-size: cover;
        }
        
        .contact-section {
            padding: 80px 0;
        }
        
        .contact-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: 100%;
            transition: all 0.3s ease;
        }
        
        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .contact-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 2rem;
            color: white;
        }
        
        .contact-form {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(238, 80, 27, 0.25);
        }
        
        .btn-submit {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-submit:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(238, 80, 27, 0.4);
            color: white;
        }
        
        .quick-contact {
            background: var(--light-gray);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .quick-contact-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            color: white;
            font-size: 1.5rem;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .quick-contact-btn:hover {
            transform: scale(1.1);
            color: white;
        }
        
        .btn-phone { background: #25d366; }
        .btn-line { background: #00c300; }
        .btn-facebook { background: #1877f2; }
        .btn-email { background: #ea4335; }
        
        .map-container {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .business-hours {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .hours-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .hours-item:last-child {
            border-bottom: none;
        }
        
        .faq-section {
            background: var(--light-gray);
            padding: 80px 0;
        }
        
        .faq-item {
            background: white;
            border-radius: 10px;
            margin-bottom: 15px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .faq-header {
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .faq-header:hover {
            background: var(--light-gray);
        }
        
        .faq-content {
            padding: 0 20px 20px;
            display: none;
        }
        
        .faq-content.show {
            display: block;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .contact-hero {
                padding: 80px 0 40px;
            }
            
            .contact-card,
            .contact-form {
                padding: 30px 20px;
            }
            
            .quick-contact-btn {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
                margin: 0 5px;
            }
        }
    </style>
</head>

<body>
<?php include 'includes/header.php'; ?>

<!-- Contact Hero Section -->
<section class="contact-hero" data-aos="fade-in"style="background-image: url(assets/images/Banner_BG_1-scaled.jpg);">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3" data-aos="fade-up">
                    ติดต่อเรา
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="200">
                    พร้อมให้คำปรึกษาและบริการด้านการออกแบบเสื้อกีฬา<br>
                    ติดต่อเราได้ทุกช่องทาง เราพร้อมช่วยเหลือคุณ 24/7
                </p>
                <div class="quick-contact" data-aos="fade-up" data-aos-delay="400">
                    <h5 class="mb-3">ติดต่อด่วน</h5>
                    <div class="d-flex justify-content-center">
                        <a href="tel:<?php echo $contact_info['phone']; ?>" class="quick-contact-btn btn-phone">
                            <i class="fas fa-phone"></i>
                        </a>
                        <a href="https://line.me/ti/p/<?php echo $contact_info['line']; ?>" class="quick-contact-btn btn-line">
                            <i class="fab fa-line"></i>
                        </a>
                        <a href="<?php echo $contact_info['facebook']; ?>" class="quick-contact-btn btn-facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="mailto:<?php echo $contact_info['email']; ?>" class="quick-contact-btn btn-email">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-center" data-aos="fade-left">
                <i class="fas fa-headset" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="py-5" style="background: var(--light-gray);">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="contact-form" data-aos="fade-right">
                    <h3 class="mb-4">
                        <i class="fas fa-paper-plane me-2 text-primary"></i>
                        ส่งข้อความถึงเรา
                    </h3>

                    <?php if ($success_message): ?>
                    <div class="alert alert-success" data-aos="fade-in">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($error_message): ?>
                    <div class="alert alert-danger" data-aos="fade-in">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>

                    <form method="POST" id="contactForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="name" name="name"
                                           placeholder="ชื่อ-นามสกุล" value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                                    <label for="name">ชื่อ-นามสกุล *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control" id="email" name="email"
                                           placeholder="อีเมล" value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                                    <label for="email">อีเมล *</label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           placeholder="เบอร์โทรศัพท์" value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                                    <label for="phone">เบอร์โทรศัพท์</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="subject" name="subject">
                                        <option value="">เลือกหัวข้อ</option>
                                        <option value="สอบถามราคา" <?php echo ($subject ?? '') === 'สอบถามราคา' ? 'selected' : ''; ?>>สอบถามราคา</option>
                                        <option value="ปรึกษาการออกแบบ" <?php echo ($subject ?? '') === 'ปรึกษาการออกแบบ' ? 'selected' : ''; ?>>ปรึกษาการออกแบบ</option>
                                        <option value="สั่งทำเสื้อ" <?php echo ($subject ?? '') === 'สั่งทำเสื้อ' ? 'selected' : ''; ?>>สั่งทำเสื้อ</option>
                                        <option value="ติดตามคำสั่งซื้อ" <?php echo ($subject ?? '') === 'ติดตามคำสั่งซื้อ' ? 'selected' : ''; ?>>ติดตามคำสั่งซื้อ</option>
                                        <option value="อื่นๆ" <?php echo ($subject ?? '') === 'อื่นๆ' ? 'selected' : ''; ?>>อื่นๆ</option>
                                    </select>
                                    <label for="subject">หัวข้อ</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating">
                            <textarea class="form-control" id="message" name="message"
                                      placeholder="ข้อความ" style="height: 150px;" required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                            <label for="message">ข้อความ *</label>
                        </div>

                        <button type="submit" class="btn btn-submit">
                            <i class="fas fa-paper-plane me-2"></i>ส่งข้อความ
                        </button>
                    </form>
                </div>
            </div>

            <!-- Business Hours & Additional Info -->
            <div class="col-lg-4">
                <div class="business-hours" data-aos="fade-left">
                    <h4 class="mb-4">
                        <i class="fas fa-clock me-2 text-primary"></i>
                        เวลาทำการ
                    </h4>
                    <?php foreach ($contact_info['hours'] as $day => $time): ?>
                    <div class="hours-item">
                        <span class="fw-bold"><?php echo $day; ?></span>
                        <span class="text-primary"><?php echo $time; ?></span>
                    </div>
                    <?php endforeach; ?>

                    <div class="mt-4 p-3" style="background: var(--light-gray); border-radius: 10px;">
                        <h6 class="text-primary mb-2">
                            <i class="fas fa-info-circle me-2"></i>หมายเหตุ
                        </h6>
                        <small class="text-muted">
                            • รับปรึกษาทาง Line 24 ชั่วโมง<br>
                            • ตอบอีเมลภายใน 24 ชั่วโมง<br>
                            • บริการหลังการขายครบครัน
                        </small>
                    </div>
                </div>

                <div class="mt-4">
                    <div class="contact-card" data-aos="fade-left" data-aos-delay="200">
                        <div class="contact-icon">
                            <i class="fab fa-line"></i>
                        </div>
                        <h5 class="text-center mb-3">Line Official</h5>
                        <p class="text-center">
                            <a href="https://line.me/ti/p/<?php echo $contact_info['line']; ?>"
                               class="btn btn-success btn-lg w-100">
                                <i class="fab fa-line me-2"></i>เพิ่มเพื่อน
                            </a>
                        </p>
                        <p class="text-center text-muted small">
                            แชทสดตลอด 24 ชั่วโมง
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="display-5 fw-bold mb-3">คำถามที่พบบ่อย</h2>
            <p class="lead">คำตอบสำหรับคำถามที่ลูกค้าสอบถามบ่อยที่สุด</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-8">
                <?php
                $faqs = [
                    [
                        'question' => 'ใช้เวลาผลิตเสื้อนานแค่ไหน?',
                        'answer' => 'ระยะเวลาผลิตปกติ 7-14 วันทำการ ขึ้นอยู่กับจำนวนและความซับซ้อนของงาน สำหรับงานด่วนสามารถปรึกษาได้'
                    ],
                    [
                        'question' => 'มีจำนวนขั้นต่ำในการสั่งทำไหม?',
                        'answer' => 'ไม่มีจำนวนขั้นต่ำ สามารถสั่งทำได้ตั้งแต่ 1 ตัวขึ้นไป แต่ราคาต่อตัวจะแตกต่างกันตามจำนวน'
                    ],
                    [
                        'question' => 'สามารถออกแบบให้ฟรีไหม?',
                        'answer' => 'ใช่ เรามีบริการออกแบบฟรี! ทีมดีไซเนอร์จะช่วยออกแบบตามความต้องการของคุณ โดยไม่มีค่าใช้จ่าย'
                    ],
                    [
                        'question' => 'มีการรับประกันสินค้าไหม?',
                        'answer' => 'เรารับประกันคุณภาพสินค้า หากมีปัญหาจากการผลิต สามารถเคลมได้ภายใน 7 วันหลังได้รับสินค้า'
                    ],
                    [
                        'question' => 'ชำระเงินอย่างไร?',
                        'answer' => 'รับชำระผ่านโอนเงิน, บัตรเครดิต, หรือเงินสดเมื่อรับสินค้า สามารถผ่อนชำระได้สำหรับออเดอร์ขนาดใหญ่'
                    ]
                ];

                foreach ($faqs as $index => $faq): ?>
                <div class="faq-item" data-aos="fade-up" data-aos-delay="<?php echo $index * 100; ?>">
                    <div class="faq-header" onclick="toggleFaq(<?php echo $index; ?>)">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><?php echo $faq['question']; ?></h6>
                            <i class="fas fa-chevron-down" id="faq-icon-<?php echo $index; ?>"></i>
                        </div>
                    </div>
                    <div class="faq-content" id="faq-content-<?php echo $index; ?>">
                        <p class="text-muted mb-0"><?php echo $faq['answer']; ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5" data-aos="fade-up">
            <h2 class="display-5 fw-bold mb-3">แผนที่</h2>
            <p class="lead">ที่ตั้งของเรา</p>
        </div>

        <div class="map-container" data-aos="fade-up">
            <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3875.5!2d100.5!3d13.7!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTPCsDQyJzAwLjAiTiAxMDDCsDMwJzAwLjAiRQ!5e0!3m2!1sth!2sth!4v1234567890"
                    width="100%" height="400" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<script>
// Initialize AOS
AOS.init({
    duration: 1000,
    once: true,
    offset: 100
});

// FAQ Toggle
function toggleFaq(index) {
    const content = document.getElementById(`faq-content-${index}`);
    const icon = document.getElementById(`faq-icon-${index}`);

    if (content.classList.contains('show')) {
        content.classList.remove('show');
        icon.style.transform = 'rotate(0deg)';
    } else {
        // Close all other FAQs
        document.querySelectorAll('.faq-content').forEach(item => {
            item.classList.remove('show');
        });
        document.querySelectorAll('[id^="faq-icon-"]').forEach(item => {
            item.style.transform = 'rotate(0deg)';
        });

        // Open clicked FAQ
        content.classList.add('show');
        icon.style.transform = 'rotate(180deg)';
    }
}

// Form validation
document.getElementById('contactForm').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const email = document.getElementById('email').value.trim();
    const message = document.getElementById('message').value.trim();

    if (!name || !email || !message) {
        e.preventDefault();
        alert('กรุณากรอกข้อมูลให้ครบถ้วน');
        return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        e.preventDefault();
        alert('รูปแบบอีเมลไม่ถูกต้อง');
        return;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังส่ง...';
    submitBtn.disabled = true;
});

// Phone number formatting
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 0) {
        if (value.length <= 3) {
            value = value;
        } else if (value.length <= 6) {
            value = value.slice(0, 3) + '-' + value.slice(3);
        } else {
            value = value.slice(0, 3) + '-' + value.slice(3, 6) + '-' + value.slice(6, 10);
        }
    }
    e.target.value = value;
});

// Smooth scroll to form when clicking quick contact
document.querySelectorAll('.quick-contact-btn').forEach(btn => {
    if (btn.getAttribute('href').includes('mailto:')) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            document.getElementById('contactForm').scrollIntoView({
                behavior: 'smooth'
            });
            document.getElementById('email').focus();
        });
    }
});
</script>



