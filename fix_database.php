<?php
/**
 * GT-SportDesign Database Fix Script
 * แก้ไขตารางขาดหายไป
 */

// แสดงข้อพลาดหมด
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'config/database.php';

// ตรวจสอบว่าเชื่อมต่อฐานข้อมูลไม่
if (!isset($pdo)) {
    die('ไม่สามารถเชื่อมต่อฐานข้อมูลได้');
}

// รายการตารางจำเป็น
$required_tables = [
    'admins', 'categories', 'customers', 'orders', 'order_items', 
    'products', 'product_images', 'file_uploads', 'gallery_images',
    'bookings', 'designs', 'customer_designs'
];

// ตรวจสอบตารางอยู่
$stmt = $pdo->query("SHOW TABLES");
$existing_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

// ตรวจสอบตารางขาดหายไป
$missing_tables = array_diff($required_tables, $existing_tables);

?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไขฐานข้อมูล GT-SportDesign</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .log { padding: 8px; margin-bottom: 5px; border-radius: 4px; }
        .info { background-color: #e2f0fb; color: #0c5460; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">แก้ไขฐานข้อมูล GT-SportDesign</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5>ผลการตรวจสอบ</h5>
            </div>
            <div class="card-body">
                <?php
                if (empty($missing_tables)) {
                    echo "<div class='log success'>✅ ตารางครบถ้วนแล้ว</div>";
                } else {
                    echo "<div class='log error'>❌ ตารางขาดหายไป: " . implode(', ', $missing_tables) . "</div>";
                    
                    if (isset($_POST['fix_database'])) {
                        echo "<div class='log info'>🔧 สร้างตารางขาดหายไป...</div>";
                        
                        // อ่านไฟล์ SQL และสร้างตาราง
                        if (file_exists('database/db.sql')) {
                            $sql_content = file_get_contents('database/db.sql');
                            $statements = explode(';', $sql_content);
                            
                            $pdo->beginTransaction();
                            
                            foreach ($statements as $statement) {
                                $statement = trim($statement);
                                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                                    try {
                                        $pdo->exec($statement);
                                    } catch (PDOException $e) {
                                        // ข้ามข้อพลาด=:ไม่
                                        if (strpos($e->getMessage(), 'already exists') === false && 
                                            strpos($e->getMessage(), 'Duplicate entry') === false) {
                                            echo "<div class='log error'>⚠️ " . $e->getMessage() . "</div>";
                                        }
                                    }
                                }
                            }
                            
                            $pdo->commit();
                            echo "<div class='log success'>✅ สร้างตารางเสร็จสิ้น</div>";
                            
                            // ตรวจสอบครั้ง
                            $stmt = $pdo->query("SHOW TABLES");
                            $final_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                            $still_missing = array_diff($required_tables, $final_tables);
                            
                            if (empty($still_missing)) {
                                echo "<div class='log success'>✅ ตารางครบถ้วนแล้ว</div>";
                            } else {
                                echo "<div class='log warning'>⚠️ ตารางขาดหายไป: " . implode(', ', $still_missing) . "</div>";
                            }
                        } else {
                            echo "<div class='log error'>❌ ไม่พบไฟล์ database/db.sql</div>";
                        }
                        
                        // สร้างโฟลเดอร์ uploads ถ้าไม่
                        echo "<div class='log info'>📁 ตรวจสอบโฟลเดอร์ uploads...</div>";
                        $upload_dirs = [
                            'uploads',
                            'uploads/products',
                            'uploads/gallery',
                            'uploads/designs',
                            'uploads/profiles',
                            'uploads/temp'
                        ];
                        
                        foreach ($upload_dirs as $dir) {
                            if (!is_dir($dir)) {
                                if (mkdir($dir, 0755, true)) {
                                    echo "<div class='log success'>✅ สร้างโฟลเดอร์ $dir</div>";
                                } else {
                                    echo "<div class='log error'>❌ ไม่สามารถสร้างโฟลเดอร์ $dir</div>";
                                }
                            } else {
                                echo "<div class='log info'>ℹ️ โฟลเดอร์ $dirอยู่แล้ว</div>";
                            }
                            
                            // สร้าง .htaccess
                            $htaccess_file = $dir . '/.htaccess';
                            if (!file_exists($htaccess_file)) {
                                file_put_contents($htaccess_file, "Options -Indexes\n");
                            }
                        }
                        
                        // สร้าง index.php ในโฟลเดอร์ uploads
                        $index_file = 'uploads/index.php';
                        if (!file_exists($index_file)) {
                            file_put_contents($index_file, "<?php\nheader('HTTP/1.0 403 Forbidden');\nexit('Access denied');\n");
                            echo "<div class='log success'>✅ สร้างไฟล์ป้องกัน uploads/index.php</div>";
                        }
                    } else {
                        ?>
                        <form method="post" class="mt-3">
                            <button type="submit" name="fix_database" class="btn btn-primary">แก้ไขฐานข้อมูล</button>
                        </form>
                        <?php
                    }
                }
                ?>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>ข้อมูลระบบ</h5>
            </div>
            <div class="card-body">
                <p><strong>PHP Version:</strong> <?php echo PHP_VERSION; ?></p>
                <p><strong>MySQL Version:</strong> 
                    <?php 
                    try {
                        $stmt = $pdo->query("SELECT VERSION() as version");
                        $version = $stmt->fetch();
                        echo $version['version'];
                    } catch (Exception $e) {
                        echo "ไม่สามารถตรวจสอบได้";
                    }
                    ?>
                </p>
                <p><strong>ตารางในฐานข้อมูล:</strong> <?php echo count($existing_tables); ?> ตาราง</p>
                <p><strong>ตารางจำเป็น:</strong> <?php echo count($required_tables); ?> ตาราง</p>
                
                <div class="mt-3">
                    <a href="index.php" class="btn btn-secondary">หน้าแรก</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>


