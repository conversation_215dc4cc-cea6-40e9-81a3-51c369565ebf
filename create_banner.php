<?php
/**
 * สร้างแบนเนอร์สำหรับเว็บไซต์ GT Sport Design
 */

// ตรวจสอบ GD Extension
if (!extension_loaded('gd')) {
    die('❌ ต้องการ GD Extension เพื่อสร้างรูปภาพ');
}

// สร้างโฟลเดอร์ถ้าไม่มี
if (!is_dir('assets/images/banner')) {
    mkdir('assets/images/banner', 0755, true);
}

echo "<h1>🎨 สร้างแบนเนอร์ GT Sport Design</h1>";

// ฟังก์ชันสร้างแบนเนอร์
function createBanner($width, $height, $filename, $text, $subtext = '') {
    // สร้าง canvas
    $image = imagecreatetruecolor($width, $height);
    
    // สี
    $orange = imagecolorallocate($image, 238, 80, 27);     // #ee501b
    $orange_light = imagecolorallocate($image, 255, 107, 53); // #ff6b35
    $white = imagecolorallocate($image, 255, 255, 255);
    $dark = imagecolorallocate($image, 44, 62, 80);        // #2c3e50
    $gray = imagecolorallocate($image, 108, 117, 125);     // #6c757d
    
    // สร้าง gradient background
    for ($i = 0; $i < $height; $i++) {
        $ratio = $i / $height;
        $r = 238 + ($ratio * (255 - 238));
        $g = 80 + ($ratio * (107 - 80));
        $b = 27 + ($ratio * (53 - 27));
        $color = imagecolorallocate($image, $r, $g, $b);
        imageline($image, 0, $i, $width, $i, $color);
    }
    
    // เพิ่มรูปแบบ geometric
    $triangle_color = imagecolorallocatealpha($image, 255, 255, 255, 100);
    $points = array(
        $width - 200, 0,
        $width, 0,
        $width, 150
    );
    imagefilledpolygon($image, $points, 3, $triangle_color);
    
    // เพิ่มวงกลม
    $circle_color = imagecolorallocatealpha($image, 255, 255, 255, 120);
    imagefilledellipse($image, $width - 100, $height - 100, 200, 200, $circle_color);
    
    // ข้อความหลัก
    $font_size_main = min($width / 15, 48);
    $font_size_sub = min($width / 25, 24);
    
    // คำนวณตำแหน่งข้อความ
    $text_x = 50;
    $text_y = $height / 2 - 20;
    
    // เงาข้อความ
    $shadow_color = imagecolorallocatealpha($image, 0, 0, 0, 50);
    imagettftext($image, $font_size_main, 0, $text_x + 2, $text_y + 2, $shadow_color, __DIR__ . '/arial.ttf', $text);
    
    // ข้อความหลัก
    imagettftext($image, $font_size_main, 0, $text_x, $text_y, $white, __DIR__ . '/arial.ttf', $text);
    
    // ข้อความรอง
    if ($subtext) {
        imagettftext($image, $font_size_sub, 0, $text_x, $text_y + 50, $white, __DIR__ . '/arial.ttf', $subtext);
    }
    
    // บันทึกไฟล์
    $filepath = "assets/images/banner/$filename";
    imagejpeg($image, $filepath, 90);
    imagedestroy($image);
    
    return $filepath;
}

// ฟังก์ชันสร้างแบนเนอร์แบบง่าย (ไม่ใช้ font file)
function createSimpleBanner($width, $height, $filename, $text, $subtext = '') {
    $image = imagecreatetruecolor($width, $height);
    
    // สี
    $orange = imagecolorallocate($image, 238, 80, 27);
    $orange_light = imagecolorallocate($image, 255, 107, 53);
    $white = imagecolorallocate($image, 255, 255, 255);
    $dark = imagecolorallocate($image, 44, 62, 80);
    
    // Gradient background
    for ($i = 0; $i < $height; $i++) {
        $ratio = $i / $height;
        $r = 238 + ($ratio * (255 - 238));
        $g = 80 + ($ratio * (107 - 80));
        $b = 27 + ($ratio * (53 - 27));
        $color = imagecolorallocate($image, $r, $g, $b);
        imageline($image, 0, $i, $width, $i, $color);
    }
    
    // รูปแบบ geometric
    $triangle_color = imagecolorallocatealpha($image, 255, 255, 255, 100);
    $points = array(
        $width - 200, 0,
        $width, 0,
        $width, 150
    );
    imagefilledpolygon($image, $points, 3, $triangle_color);
    
    // วงกลม
    for ($i = 0; $i < 3; $i++) {
        $circle_color = imagecolorallocatealpha($image, 255, 255, 255, 120 - ($i * 30));
        imagefilledellipse($image, $width - 100 + ($i * 20), $height - 100 + ($i * 20), 150 - ($i * 30), 150 - ($i * 30), $circle_color);
    }
    
    // ข้อความ (ใช้ built-in font)
    $font_size = 5; // ขนาด font built-in (1-5)
    $text_x = 50;
    $text_y = $height / 2 - 30;
    
    // เงาข้อความ
    $shadow_color = imagecolorallocatealpha($image, 0, 0, 0, 50);
    imagestring($image, $font_size, $text_x + 2, $text_y + 2, $text, $shadow_color);
    
    // ข้อความหลัก
    imagestring($image, $font_size, $text_x, $text_y, $text, $white);
    
    // ข้อความรอง
    if ($subtext) {
        imagestring($image, 3, $text_x, $text_y + 40, $subtext, $white);
    }
    
    // บันทึกไฟล์
    $filepath = "assets/images/banner/$filename";
    imagejpeg($image, $filepath, 90);
    imagedestroy($image);
    
    return $filepath;
}

// สร้างแบนเนอร์หลายขนาด
$banners = [
    [
        'width' => 1200,
        'height' => 400,
        'filename' => 'main-banner-desktop.jpg',
        'text' => 'GT SPORT DESIGN',
        'subtext' => 'ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬา'
    ],
    [
        'width' => 800,
        'height' => 300,
        'filename' => 'main-banner-tablet.jpg',
        'text' => 'GT SPORT DESIGN',
        'subtext' => 'ออกแบบเสื้อกีฬาคุณภาพสูง'
    ],
    [
        'width' => 600,
        'height' => 250,
        'filename' => 'main-banner-mobile.jpg',
        'text' => 'GT SPORT',
        'subtext' => 'เสื้อกีฬาคุณภาพ'
    ],
    [
        'width' => 1920,
        'height' => 600,
        'filename' => 'hero-banner-fullhd.jpg',
        'text' => 'GT SPORT DESIGN',
        'subtext' => 'ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง'
    ]
];

echo "<h2>📸 กำลังสร้างแบนเนอร์...</h2>";

foreach ($banners as $banner) {
    try {
        $filepath = createSimpleBanner(
            $banner['width'],
            $banner['height'],
            $banner['filename'],
            $banner['text'],
            $banner['subtext']
        );
        
        echo "<div style='margin: 15px 0; padding: 15px; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>";
        echo "<h4>✅ {$banner['filename']}</h4>";
        echo "<p><strong>ขนาด:</strong> {$banner['width']} x {$banner['height']} px</p>";
        echo "<p><strong>ไฟล์:</strong> $filepath</p>";
        echo "<img src='$filepath' alt='{$banner['filename']}' style='max-width: 100%; height: auto; border-radius: 5px;'>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "❌ ไม่สามารถสร้าง {$banner['filename']}: " . $e->getMessage();
        echo "</div>";
    }
}

// สร้างไฟล์ CSS สำหรับแบนเนอร์
$banner_css = "
/* Banner Styles for GT Sport Design */
.hero-banner {
    width: 100%;
    height: 400px;
    background-image: url('assets/images/banner/main-banner-desktop.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    overflow: hidden;
}

.hero-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(238, 80, 27, 0.8), rgba(255, 107, 53, 0.8));
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: white;
    padding: 0 50px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Responsive Banner */
@media (max-width: 1024px) {
    .hero-banner {
        background-image: url('assets/images/banner/main-banner-tablet.jpg');
        height: 300px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .hero-banner {
        background-image: url('assets/images/banner/main-banner-mobile.jpg');
        height: 250px;
    }
    
    .hero-content {
        padding: 0 20px;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
}

/* Full HD Banner */
@media (min-width: 1920px) {
    .hero-banner {
        background-image: url('assets/images/banner/hero-banner-fullhd.jpg');
        height: 600px;
    }
}
";

file_put_contents('assets/css/banner.css', $banner_css);
echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>✅ สร้างไฟล์ CSS แบนเนอร์</h3>";
echo "<p>ไฟล์: <code>assets/css/banner.css</code></p>";
echo "</div>";

// สร้างตัวอย่างการใช้งาน
$example_html = '
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตัวอย่างแบนเนอร์ GT Sport Design</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/banner.css" rel="stylesheet">
</head>
<body>
    <div class="hero-banner">
        <div class="hero-content">
            <h1 class="hero-title">GT SPORT DESIGN</h1>
            <p class="hero-subtitle">ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง</p>
            <a href="shirt-design.php" class="btn btn-light btn-lg me-3">
                <i class="fas fa-paint-brush me-2"></i>เริ่มออกแบบ
            </a>
            <a href="products.php" class="btn btn-outline-light btn-lg">
                <i class="fas fa-shopping-cart me-2"></i>ดูสินค้า
            </a>
        </div>
    </div>
</body>
</html>';

file_put_contents('banner_example.html', $example_html);

echo "<div style='background: #cce5ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
echo "<h2>🎯 วิธีใช้งานแบนเนอร์</h2>";
echo "<h4>1. เพิ่ม CSS ในหน้าเว็บ:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>&lt;link href=\"assets/css/banner.css\" rel=\"stylesheet\"&gt;</pre>";

echo "<h4>2. เพิ่ม HTML แบนเนอร์:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>&lt;div class=\"hero-banner\"&gt;
    &lt;div class=\"hero-content\"&gt;
        &lt;h1 class=\"hero-title\"&gt;GT SPORT DESIGN&lt;/h1&gt;
        &lt;p class=\"hero-subtitle\"&gt;ผู้เชี่ยวชาญด้านการออกแบบและผลิตเสื้อกีฬาคุณภาพสูง&lt;/p&gt;
        &lt;a href=\"shirt-design.php\" class=\"btn btn-light btn-lg\"&gt;เริ่มออกแบบ&lt;/a&gt;
    &lt;/div&gt;
&lt;/div&gt;</pre>";

echo "<h4>3. ดูตัวอย่าง:</h4>";
echo "<a href='banner_example.html' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ดูตัวอย่างแบนเนอร์</a>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>📋 ไฟล์ที่สร้างแล้ว:</h3>";
echo "<ul>";
echo "<li>✅ assets/images/banner/main-banner-desktop.jpg (1200x400)</li>";
echo "<li>✅ assets/images/banner/main-banner-tablet.jpg (800x300)</li>";
echo "<li>✅ assets/images/banner/main-banner-mobile.jpg (600x250)</li>";
echo "<li>✅ assets/images/banner/hero-banner-fullhd.jpg (1920x600)</li>";
echo "<li>✅ assets/css/banner.css</li>";
echo "<li>✅ banner_example.html</li>";
echo "</ul>";
echo "</div>";

echo "<p><small>สร้างเมื่อ: " . date('Y-m-d H:i:s') . "</small></p>";
?>

<style>
body {
    font-family: 'Kanit', sans-serif;
    max-width: 1000px;
    margin: 20px auto;
    padding: 20px;
    background: #f8f9fa;
}

h1, h2, h3, h4 {
    color: #ee501b;
}

code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 0.9rem;
}

img {
    border: 2px solid #dee2e6;
}
</style>
