<?php
session_start();
require_once '../config/database.php';
require_once '../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: index.php');
    exit;
}

$page_title = "จัดการคำสั่งซื้อ";
$current_admin_page = 'orders.php';

try {
    $db = getDbConnection();
    
    // Handle status update
    if ($_POST['action'] === 'update_status' && isset($_POST['order_id'], $_POST['status'])) {
        $stmt = $db->prepare("UPDATE payment_orders SET payment_status = ?, updated_at = NOW() WHERE id = ?");
        $stmt->execute([$_POST['status'], $_POST['order_id']]);
        $success_message = "อัปเดตสถานะเรียบร้อย";
    }
    
    // Handle order deletion
    if ($_POST['action'] === 'delete_order' && isset($_POST['order_id'])) {
        $stmt = $db->prepare("DELETE FROM payment_orders WHERE id = ?");
        $stmt->execute([$_POST['order_id']]);
        $success_message = "ลบคำสั่งซื้อเรียบร้อย";
    }
    
    // Get filter parameters
    $status_filter = $_GET['status'] ?? '';
    $search = $_GET['search'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';
    $page = max(1, intval($_GET['page'] ?? 1));
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    // Build query
    $where_conditions = [];
    $params = [];
    
    if ($status_filter) {
        $where_conditions[] = "payment_status = ?";
        $params[] = $status_filter;
    }
    
    if ($search) {
        $where_conditions[] = "(order_id LIKE ? OR customer_name LIKE ? OR customer_email LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }
    
    if ($date_from) {
        $where_conditions[] = "DATE(created_at) >= ?";
        $params[] = $date_from;
    }
    
    if ($date_to) {
        $where_conditions[] = "DATE(created_at) <= ?";
        $params[] = $date_to;
    }
    
    $where_clause = $where_conditions ? "WHERE " . implode(" AND ", $where_conditions) : "";
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM payment_orders $where_clause";
    $stmt = $db->prepare($count_sql);
    $stmt->execute($params);
    $total_orders = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_orders / $per_page);
    
    // Get orders
    $sql = "SELECT * FROM payment_orders $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get order statistics
    $stats_sql = "SELECT 
        payment_status,
        COUNT(*) as count,
        SUM(total_amount) as total_amount
        FROM payment_orders 
        GROUP BY payment_status";
    $stmt = $db->query($stats_sql);
    $order_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $error_message = "เกิดข้อผิดพลาด: " . $e->getMessage();
    $orders = [];
    $order_stats = [];
    $total_orders = 0;
    $total_pages = 1;
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-shopping-cart"></i> จัดการคำสั่งซื้อ</h2>
        <div>
            <button class="btn btn-success" onclick="exportOrders()">
                <i class="fas fa-download"></i> ส่งออก Excel
            </button>
            <button class="btn btn-primary" onclick="refreshOrders()">
                <i class="fas fa-sync"></i> รีเฟรช
            </button>
        </div>
    </div>

    <!-- Order Statistics -->
    <div class="row mb-4">
        <?php
        $status_colors = [
            'pending' => 'warning',
            'paid' => 'success',
            'processing' => 'info',
            'shipped' => 'primary',
            'completed' => 'success',
            'cancelled' => 'danger'
        ];
        $status_names = [
            'pending' => 'รอดำเนินการ',
            'paid' => 'ชำระแล้ว',
            'processing' => 'กำลังผลิต',
            'shipped' => 'จัดส่งแล้ว',
            'completed' => 'เสร็จสิ้น',
            'cancelled' => 'ยกเลิก'
        ];
        
        foreach ($order_stats as $stat):
            $color = $status_colors[$stat['payment_status']] ?? 'secondary';
            $name = $status_names[$stat['payment_status']] ?? $stat['payment_status'];
        ?>
        <div class="col-md-2">
            <div class="card text-white bg-<?php echo $color; ?>">
                <div class="card-body text-center">
                    <h5><?php echo $stat['count']; ?></h5>
                    <small><?php echo $name; ?></small>
                    <div class="mt-1">
                        <small>฿<?php echo number_format($stat['total_amount']); ?></small>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label">สถานะ</label>
                    <select name="status" class="form-select">
                        <option value="">ทั้งหมด</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>รอดำเนินการ</option>
                        <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>ชำระแล้ว</option>
                        <option value="processing" <?php echo $status_filter === 'processing' ? 'selected' : ''; ?>>กำลังผลิต</option>
                        <option value="shipped" <?php echo $status_filter === 'shipped' ? 'selected' : ''; ?>>จัดส่งแล้ว</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>เสร็จสิ้น</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ยกเลิก</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">ค้นหา</label>
                    <input type="text" name="search" class="form-control" placeholder="หมายเลขคำสั่งซื้อ, ชื่อ, อีเมล" value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">วันที่เริ่ม</label>
                    <input type="date" name="date_from" class="form-control" value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                <div class="col-md-2">
                    <label class="form-label">วันที่สิ้นสุด</label>
                    <input type="date" name="date_to" class="form-control" value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> ค้นหา
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Orders Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5><i class="fas fa-list"></i> คำสั่งซื้อ (<?php echo number_format($total_orders); ?> รายการ)</h5>
            <div>
                <small class="text-muted">หน้า <?php echo $page; ?> จาก <?php echo $total_pages; ?></small>
            </div>
        </div>
        <div class="card-body">
            <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>หมายเลขคำสั่งซื้อ</th>
                            <th>ลูกค้า</th>
                            <th>สินค้า</th>
                            <th>จำนวน</th>
                            <th>ยอดรวม</th>
                            <th>สถานะ</th>
                            <th>วันที่สั่ง</th>
                            <th>การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($orders)): ?>
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">ไม่พบคำสั่งซื้อ</td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($orders as $order): ?>
                        <tr>
                            <td>
                                <strong><?php echo htmlspecialchars($order['order_id']); ?></strong>
                                <?php if ($order['design_data']): ?>
                                <br><small class="text-muted"><i class="fas fa-palette"></i> มีการออกแบบ</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div><?php echo htmlspecialchars($order['customer_name']); ?></div>
                                <small class="text-muted"><?php echo htmlspecialchars($order['customer_email']); ?></small>
                                <?php if ($order['customer_phone']): ?>
                                <br><small class="text-muted"><?php echo htmlspecialchars($order['customer_phone']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div><?php echo htmlspecialchars($order['product_name']); ?></div>
                                <?php if ($order['product_size']): ?>
                                <small class="text-muted">ไซส์: <?php echo htmlspecialchars($order['product_size']); ?></small>
                                <?php endif; ?>
                            </td>
                            <td><?php echo number_format($order['quantity']); ?></td>
                            <td><strong>฿<?php echo number_format($order['total_amount']); ?></strong></td>
                            <td>
                                <select class="form-select form-select-sm" onchange="updateOrderStatus(<?php echo $order['id']; ?>, this.value)">
                                    <option value="pending" <?php echo $order['payment_status'] === 'pending' ? 'selected' : ''; ?>>รอดำเนินการ</option>
                                    <option value="paid" <?php echo $order['payment_status'] === 'paid' ? 'selected' : ''; ?>>ชำระแล้ว</option>
                                    <option value="processing" <?php echo $order['payment_status'] === 'processing' ? 'selected' : ''; ?>>กำลังผลิต</option>
                                    <option value="shipped" <?php echo $order['payment_status'] === 'shipped' ? 'selected' : ''; ?>>จัดส่งแล้ว</option>
                                    <option value="completed" <?php echo $order['payment_status'] === 'completed' ? 'selected' : ''; ?>>เสร็จสิ้น</option>
                                    <option value="cancelled" <?php echo $order['payment_status'] === 'cancelled' ? 'selected' : ''; ?>>ยกเลิก</option>
                                </select>
                            </td>
                            <td><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewOrder(<?php echo $order['id']; ?>)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <?php if ($order['design_data']): ?>
                                    <button class="btn btn-outline-info" onclick="viewDesign(<?php echo $order['id']; ?>)">
                                        <i class="fas fa-palette"></i>
                                    </button>
                                    <?php endif; ?>
                                    <button class="btn btn-outline-danger" onclick="deleteOrder(<?php echo $order['id']; ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
            <nav class="mt-4">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">ก่อนหน้า</a>
                    </li>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                    <li class="page-item">
                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">ถัดไป</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Order Details Modal -->
<div class="modal fade" id="orderModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">รายละเอียดคำสั่งซื้อ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderModalBody">
                <!-- Order details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Design Preview Modal -->
<div class="modal fade" id="designModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ดูการออกแบบ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="designModalBody">
                <!-- Design preview will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
// Update order status
function updateOrderStatus(orderId, status) {
    if (confirm('ต้องการเปลี่ยนสถานะคำสั่งซื้อนี้หรือไม่?')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="order_id" value="${orderId}">
            <input type="hidden" name="status" value="${status}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Delete order
function deleteOrder(orderId) {
    if (confirm('ต้องการลบคำสั่งซื้อนี้หรือไม่? การดำเนินการนี้ไม่สามารถยกเลิกได้')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_order">
            <input type="hidden" name="order_id" value="${orderId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// View order details
function viewOrder(orderId) {
    fetch(`ajax/get_order_details.php?id=${orderId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('orderModalBody').innerHTML = html;
            new bootstrap.Modal(document.getElementById('orderModal')).show();
        })
        .catch(error => {
            alert('เกิดข้อผิดพลาดในการโหลดข้อมูล');
        });
}

// View design
function viewDesign(orderId) {
    fetch(`ajax/get_design_preview.php?id=${orderId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('designModalBody').innerHTML = html;
            new bootstrap.Modal(document.getElementById('designModal')).show();
        })
        .catch(error => {
            alert('เกิดข้อผิดพลาดในการโหลดการออกแบบ');
        });
}

// Export orders
function exportOrders() {
    const params = new URLSearchParams(window.location.search);
    window.open(`ajax/export_orders.php?${params.toString()}`, '_blank');
}

// Refresh orders
function refreshOrders() {
    location.reload();
}

// Auto refresh every 60 seconds
setInterval(refreshOrders, 60000);
</script>

<?php include 'includes/footer.php'; ?>
