<?php
$admin_page_title = "ข้อความแชท";
include '../includes/header.php'; // Includes auth.php to protect the page
require_once '../../includes/config.php';
require_once '../../includes/db.php';
// Simulated data for chat messages (replace with actual database query)
$messages = [
    ['id' => 1, 'sender_name' => 'นชาย', 'sender_email' => '<EMAIL>', 'message' => 'ส่งคำถาม/ค่ะ คำถามสนใจเราจะส่งเสื้อคำถาม รบกวนขอคำถาม официальн้อม่ะ', 'created_at' => '2023-05-15 14:30:00', 'status' => 'unread'],
    ['id' => 2, 'sender_name' => 'น应用查看', 'sender_email' => '<EMAIL>', 'message' => 'อยากทราบราคาเสื้อโปโลคำถาม/ค่ะ จำนวน 50 คำถามค่ะ', 'created_at' => '2023-05-14 10:15:00', 'status' => 'read'],
    ['id' => 3, 'sender_name' => 'นมานะ', 'sender_email' => '<EMAIL>', 'message' => 'ส่งคำถาม/ไปเมื่อวานคำถาม/้ เลขออเดอร์ #12345 อยากทราบสถานะคำถาม/ส่งคำถาม', 'created_at' => '2023-05-13 16:45:00', 'status' => 'replied']
];

// Filter by status if requested
$message_status = isset($_GET['status']) ? $_GET['status'] : '';
if (!empty($message_status)) {
    $filtered_messages = [];
    foreach ($messages as $message) {
        if ($message['status'] == $message_status) {
            $filtered_messages[] = $message;
        }
    }
    $messages = $filtered_messages;
}

// Count messages by status
$unread_count = 0;
$read_count = 0;
$replied_count = 0;

foreach ($messages as $message) {
    if ($message['status'] == 'unread') {
        $unread_count++;
    } elseif ($message['status'] == 'read') {
        $read_count++;
    } elseif ($message['status'] == 'replied') {
        $replied_count++;
    }
}
?>

<div class="admin-content">
    <div class="admin-content-header">
        <h2><?php echo $admin_page_title; ?></h2>
    </div>
    
    <?php if (isset($_SESSION['message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['message_type']; ?>">
            <?php 
                echo $_SESSION['message']; 
                unset($_SESSION['message']);
                unset($_SESSION['message_type']);
            ?>
        </div>
    <?php endif; ?>

    <div class="admin-card">
        <div class="admin-card-header">
            <h3>ข้อความแชท</h3>
            <div class="filter-controls">
                <a href="?status=all" class="btn btn-sm <?php echo (!isset($_GET['status']) || $_GET['status'] == 'all') ? 'btn-primary' : 'btn-light'; ?>">หมด</a>
                <a href="?status=unread" class="btn btn-sm <?php echo (isset($_GET['status']) && $_GET['status'] == 'unread') ? 'btn-primary' : 'btn-light'; ?>">ไม่ได้อ่าน</a>
                <a href="?status=read" class="btn btn-sm <?php echo (isset($_GET['status']) && $_GET['status'] == 'read') ? 'btn-primary' : 'btn-light'; ?>">อ่านแล้ว</a>
                <a href="?status=replied" class="btn btn-sm <?php echo (isset($_GET['status']) && $_GET['status'] == 'replied') ? 'btn-primary' : 'btn-light'; ?>">ตอบแล้ว</a>
            </div>
        </div>
        <div class="admin-card-body">
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>ชื่อส่ง</th>
                        <th>ข้อความ</th>
                        <th>สร้างเมื่อ</th>
                        <th>สถานะ</th>
                        <th>การกระทำ</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($messages as $message): ?>
                        <tr class="<?php echo ($message['status'] == 'unread') ? 'unread-row' : ''; ?>">
                            <td><?php echo $message['id']; ?></td>
                            <td><?php echo $message['sender_name']; ?></td>
                            <td><?php echo substr($message['message'], 0, 50) . (strlen($message['message']) > 50 ? '...' : ''); ?></td>
                            <td><?php echo $message['created_at']; ?></td>
                            <td>
                                <span class="status-badge status-<?php echo $message['status']; ?>">
                                    <?php 
                                        switch($message['status']) {
                                            case 'unread': echo 'ไม่ได้อ่าน'; break;
                                            case 'read': echo 'อ่านแล้ว'; break;
                                            case 'replied': echo 'ตอบแล้ว'; break;
                                            default: echo $message['status'];
                                        }
                                    ?>
                                </span>
                            </td>
                            <td>
                                <a href="view.php?id=<?php echo $message['id']; ?>" class="btn btn-sm btn-info">ดู</a>
                                <a href="reply.php?id=<?php echo $message['id']; ?>" class="btn btn-sm btn-primary">ตอบ</a>
                                <?php if ($message['status'] == 'unread'): ?>
                                    <a href="process.php?action=mark_read&id=<?php echo $message['id']; ?>" class="btn btn-sm btn-secondary">ทำเครื�หมายว่าอ่านแล้ว</a>
                                <?php endif; ?>
                                <a href="process.php?action=delete&id=<?php echo $message['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('แน่ใจว่าต้องการลบข้อความ?')">ลบ</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style>
.filter-options {
    display: flex;
    gap: 15px;
}

.filter-options a {
    text-decoration: none;
    color: #555;
    padding: 5px 10px;
    border-radius: 3px;
}

.filter-options a.active {
    background-color: #ee521b;
    color: white;
}

.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    color: white;
}

.status-unread {
    background-color: #f0ad4e;
}

.status-read {
    background-color: #5bc0de;
}

.status-replied {
    background-color: #5cb85c;
}

.unread-row {
    font-weight: bold;
    background-color: #f9f9f9;
}

.message-preview {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.empty-state {
    text-align: center;
    padding: 30px;
    color: #777;
}
</style>

<?php include '../includes/footer.php'; ?>


