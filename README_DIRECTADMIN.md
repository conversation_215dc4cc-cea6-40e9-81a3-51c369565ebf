# GT-SportDesign - คู่มือติดตั้งใน DirectAdmin

## ข้อกำหนดระบบ
- PHP 7.4 หรือสูงกว่า
- MySQL 5.7 หรือสูงกว่า
- Extensions: PDO, PDO_MySQL, GD, mbstring, JSON
- พื้นที่ดิสก์: อย่างน้อย 100MB

## ขั้นตอนการติดตั้ง

### 1. อัปโหลดไฟล์
1. เข้าสู่ระบบ DirectAdmin
2. ไปที่ File Manager
3. อัปโหลดไฟล์ทั้งหมดไปยัง `public_html/`
4. แตกไฟล์ (Extract) หากเป็นไฟล์ ZIP

### 2. สร้างฐานข้อมูล
1. ไปที่ MySQL Management ใน DirectAdmin
2. สร้างฐานข้อมูลใหม่
3. สร้างผู้ใช้ฐานข้อมูล
4. กำหนดสิทธิ์ให้ผู้ใช้เข้าถึงฐานข้อมูล
5. จดบันทึกข้อมูลต่อไปนี้:
   - ชื่อฐานข้อมูล
   - ชื่อผู้ใช้
   - รหัสผ่าน
   - Host (มักจะเป็น localhost)

### 3. ตั้งค่าสิทธิ์ไฟล์
```bash
chmod 755 public_html/
chmod 755 public_html/config/
chmod 755 public_html/uploads/
chmod -R 755 public_html/uploads/
```

### 4. เริ่มการติดตั้ง
1. เปิดเว็บเบราว์เซอร์
2. ไปที่ `https://yourdomain.com/deploy.php`
3. ตรวจสอบระบบและแก้ไขปัญหา (ถ้ามี)
4. คลิก "เริ่มติดตั้ง" หรือไปที่ `install.php`

### 5. กรอกข้อมูลฐานข้อมูล
- Database Host: `localhost`
- Database Name: ชื่อฐานข้อมูลที่สร้างไว้
- Database Username: ชื่อผู้ใช้ฐานข้อมูล
- Database Password: รหัสผ่านฐานข้อมูล

### 6. เสร็จสิ้นการติดตั้ง
1. ระบบจะสร้างตารางฐานข้อมูลอัตโนมัติ
2. สร้างโฟลเดอร์ที่จำเป็น
3. ตั้งค่าความปลอดภัยเบื้องต้น

## ข้อมูลเข้าสู่ระบบเริ่มต้น

### แอดมิน
- URL: `https://yourdomain.com/admin/login.php`
- Username: `admin`
- Password: `admin123`

**⚠️ สำคัญ: เปลี่ยนรหัสผ่านทันทีหลังเข้าสู่ระบบครั้งแรก**

## การแก้ไขปัญหาที่พบบ่อย

### 1. ข้อผิดพลาด "Database connection failed"
- ตรวจสอบข้อมูลฐานข้อมูลใน `config/database.php`
- ตรวจสอบว่าฐานข้อมูลและผู้ใช้ถูกสร้างแล้ว
- ตรวจสอบสิทธิ์การเข้าถึงฐานข้อมูล

### 2. ข้อผิดพลาด "Permission denied"
- ตรวจสอบสิทธิ์โฟลเดอร์ `uploads/` และโฟลเดอร์ย่อย
- ตั้งค่าสิทธิ์ 755 หรือ 775

### 3. ไม่สามารถอัปโหลดรูปภาพได้
- ตรวจสอบขนาดไฟล์สูงสุดใน PHP
- ตรวจสอบสิทธิ์โฟลเดอร์ `uploads/`
- ตรวจสอบ PHP extension GD

### 4. หน้าเว็บแสดงข้อผิดพลาด 500
- ตรวจสอบ error log ใน DirectAdmin
- ตรวจสอบไฟล์ `.htaccess`
- ตรวจสอบ PHP version

## ไฟล์สำคัญ

### ไฟล์การตั้งค่า
- `config/database.php` - การตั้งค่าฐานข้อมูล
- `includes/config.php` - การตั้งค่าระบบ
- `.htaccess` - การตั้งค่าความปลอดภัย

### ไฟล์ติดตั้งและทดสอบ
- `install.php` - ไฟล์ติดตั้งระบบ
- `deploy.php` - แดชบอร์ดการติดตั้ง
- `test_connection.php` - ทดสอบการเชื่อมต่อ
- `fix_database.php` - แก้ไขฐานข้อมูล

### โฟลเดอร์สำคัญ
- `admin/` - ระบบจัดการแอดมิน
- `uploads/` - ไฟล์อัปโหลด
- `assets/` - ไฟล์ CSS, JS, รูปภาพ
- `includes/` - ไฟล์ PHP ที่ใช้ร่วมกัน

## การรักษาความปลอดภัย

### หลังติดตั้งเสร็จ
1. ลบไฟล์ `install.php`
2. ลบไฟล์ `deploy.php`
3. ลบไฟล์ `test_connection.php` (ถ้าไม่ต้องการ)
4. เปลี่ยนรหัสผ่านแอดมิน
5. ตั้งค่า SSL Certificate

### การสำรองข้อมูล
- สำรองฐานข้อมูลเป็นประจำ
- สำรองไฟล์ในโฟลเดอร์ `uploads/`
- สำรองไฟล์การตั้งค่า

## การอัปเดต
1. สำรองข้อมูลก่อนอัปเดต
2. อัปโหลดไฟล์ใหม่
3. รันไฟล์ `fix_database.php` (ถ้าจำเป็น)
4. ตรวจสอบการทำงาน

## การติดต่อสนับสนุน
- Email: <EMAIL>
- Line: @gtsport
- Phone: ************

## เวอร์ชัน
- Version: 1.0
- Last Updated: 2025-01-27
- Compatible with: DirectAdmin, cPanel, Plesk
