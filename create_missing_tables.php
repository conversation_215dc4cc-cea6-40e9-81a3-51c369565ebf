<?php
/**
 * GT-SportDesign - Create Missing Tables
 * สร้างตารางที่ขาดหายไปทีละตาราง
 */

require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='th'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>สร้างตารางที่ขาดหายไป - GT-SportDesign</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Kanit', sans-serif; background: #f8f9fa; }
        .container { max-width: 800px; margin: 50px auto; }
        .card { border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .log { background: #f8f9fa; border-radius: 10px; padding: 15px; margin: 10px 0; font-family: monospace; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='card'>
            <div class='card-header bg-primary text-white'>
                <h3><i class='fas fa-database'></i> สร้างตารางที่ขาดหายไป GT-SportDesign</h3>
            </div>
            <div class='card-body'>";

try {
    echo "<div class='log info'>🔧 เริ่มสร้างตารางที่ขาดหายไป...</div>";
    
    // 1. สร้างตาราง product_categories
    echo "<div class='log info'>📂 สร้างตาราง product_categories...</div>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `product_categories` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(100) NOT NULL,
              `description` text DEFAULT NULL,
              `image` varchar(255) DEFAULT NULL,
              `status` enum('active','inactive') DEFAULT 'active',
              `sort_order` int(11) DEFAULT 0,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `status` (`status`),
              KEY `sort_order` (`sort_order`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='log success'>✅ สร้างตาราง product_categories สำเร็จ</div>";
    } catch (Exception $e) {
        echo "<div class='log error'>❌ product_categories: " . $e->getMessage() . "</div>";
    }
    
    // 2. สร้างตาราง system_settings
    echo "<div class='log info'>⚙️ สร้างตาราง system_settings...</div>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `system_settings` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `setting_key` varchar(100) NOT NULL,
              `setting_value` text DEFAULT NULL,
              `setting_type` enum('text','number','boolean','json') DEFAULT 'text',
              `description` text DEFAULT NULL,
              `updated_by` int(11) DEFAULT NULL,
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              UNIQUE KEY `setting_key` (`setting_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='log success'>✅ สร้างตาราง system_settings สำเร็จ</div>";
    } catch (Exception $e) {
        echo "<div class='log error'>❌ system_settings: " . $e->getMessage() . "</div>";
    }
    
    // 3. สร้างตาราง file_uploads
    echo "<div class='log info'>📁 สร้างตาราง file_uploads...</div>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `file_uploads` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `original_name` varchar(255) NOT NULL,
              `file_name` varchar(255) NOT NULL,
              `file_path` varchar(500) NOT NULL,
              `file_size` int(11) NOT NULL,
              `file_type` varchar(100) NOT NULL,
              `uploaded_by` int(11) DEFAULT NULL,
              `upload_type` enum('product','gallery','design','profile','other') DEFAULT 'other',
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `uploaded_by` (`uploaded_by`),
              KEY `upload_type` (`upload_type`),
              KEY `created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='log success'>✅ สร้างตาราง file_uploads สำเร็จ</div>";
    } catch (Exception $e) {
        echo "<div class='log error'>❌ file_uploads: " . $e->getMessage() . "</div>";
    }
    
    // 4. สร้างตาราง product_images
    echo "<div class='log info'>🖼️ สร้างตาราง product_images...</div>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `product_images` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `product_id` int(11) NOT NULL,
              `file_id` int(11) NOT NULL,
              `image_type` enum('main','gallery','thumbnail') DEFAULT 'gallery',
              `sort_order` int(11) DEFAULT 0,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `product_id` (`product_id`),
              KEY `file_id` (`file_id`),
              KEY `image_type` (`image_type`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='log success'>✅ สร้างตาราง product_images สำเร็จ</div>";
    } catch (Exception $e) {
        echo "<div class='log error'>❌ product_images: " . $e->getMessage() . "</div>";
    }
    
    // 5. สร้างตาราง gallery_images
    echo "<div class='log info'>🎨 สร้างตาราง gallery_images...</div>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `gallery_images` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `title` varchar(200) NOT NULL,
              `description` text DEFAULT NULL,
              `file_id` int(11) DEFAULT NULL,
              `category` varchar(50) DEFAULT 'portfolio',
              `is_active` tinyint(1) DEFAULT 1,
              `sort_order` int(11) DEFAULT 0,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `file_id` (`file_id`),
              KEY `category` (`category`),
              KEY `is_active` (`is_active`),
              KEY `sort_order` (`sort_order`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='log success'>✅ สร้างตาราง gallery_images สำเร็จ</div>";
    } catch (Exception $e) {
        echo "<div class='log error'>❌ gallery_images: " . $e->getMessage() . "</div>";
    }
    
    // 6. สร้างตาราง bookings
    echo "<div class='log info'>📅 สร้างตาราง bookings...</div>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `bookings` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `customer_id` int(11) NOT NULL,
              `booking_type` enum('design_consultation','pickup','delivery') NOT NULL,
              `booking_date` date NOT NULL,
              `booking_time` time NOT NULL,
              `status` enum('pending','confirmed','completed','cancelled') DEFAULT 'pending',
              `notes` text DEFAULT NULL,
              `admin_notes` text DEFAULT NULL,
              `confirmed_by` int(11) DEFAULT NULL,
              `confirmed_at` datetime DEFAULT NULL,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `customer_id` (`customer_id`),
              KEY `booking_type` (`booking_type`),
              KEY `booking_date` (`booking_date`),
              KEY `status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='log success'>✅ สร้างตาราง bookings สำเร็จ</div>";
    } catch (Exception $e) {
        echo "<div class='log error'>❌ bookings: " . $e->getMessage() . "</div>";
    }
    
    // 7. สร้างตาราง pickup_locations
    echo "<div class='log info'>📍 สร้างตาราง pickup_locations...</div>";
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `pickup_locations` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `name` varchar(200) NOT NULL,
              `address` text NOT NULL,
              `phone` varchar(20) DEFAULT NULL,
              `latitude` decimal(10,8) DEFAULT NULL,
              `longitude` decimal(11,8) DEFAULT NULL,
              `operating_hours` text DEFAULT NULL,
              `is_active` tinyint(1) DEFAULT 1,
              `sort_order` int(11) DEFAULT 0,
              `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
              PRIMARY KEY (`id`),
              KEY `is_active` (`is_active`),
              KEY `sort_order` (`sort_order`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
        echo "<div class='log success'>✅ สร้างตาราง pickup_locations สำเร็จ</div>";
    } catch (Exception $e) {
        echo "<div class='log error'>❌ pickup_locations: " . $e->getMessage() . "</div>";
    }
    
    echo "<div class='log success'>🎉 สร้างตารางเสร็จสิ้น!</div>";
    
} catch (Exception $e) {
    echo "<div class='log error'>💥 เกิดข้อผิดพลาด: " . $e->getMessage() . "</div>";
}

echo "
            </div>
            <div class='card-footer'>
                <div class='d-flex justify-content-between'>
                    <a href='add_sample_data.php' class='btn btn-warning'>
                        <i class='fas fa-plus'></i> เพิ่มข้อมูลตัวอย่าง
                    </a>
                    <a href='admin/dashboard.php' class='btn btn-success'>
                        <i class='fas fa-tachometer-alt'></i> เข้าสู่ระบบ Admin
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>";
?>
