<?php
session_start();

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// เปลี่ยนเส้นทางไปหน้า dashboard
header('Location: dashboard.php');
exit();

// ดึงข้อมูลสถิติ
$db = getDB();
$stats = [];
try {
    $stats['total_products'] = $db->query("SELECT COUNT(*) FROM products")->fetchColumn();
    $stats['total_customers'] = $db->query("SELECT COUNT(*) FROM customers")->fetchColumn();
    $stats['total_orders'] = $db->query("SELECT COUNT(*) FROM orders")->fetchColumn();
    $stats['pending_orders'] = $db->query("SELECT COUNT(*) FROM orders WHERE status='pending'")->fetchColumn();
    $stats['total_revenue'] = $db->query("SELECT SUM(total_price) FROM orders WHERE status='completed'")->fetchColumn() ?: 0;
} catch (Exception $e) {
    // fallback ถ้า db ยังไม่มี
    $stats = ['total_products' => 0, 'total_customers' => 0, 'total_orders' => 0, 'pending_orders' => 0, 'total_revenue' => 0];
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <title>Admin Dashboard - GT Sport Design</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar { background: #343a40; min-height: 100vh; }
        .sidebar .nav-link { color: #adb5bd; }
        .sidebar .nav-link:hover, .sidebar .nav-link.active { color: #fff; background: #495057; }
        .card-stat { border-left: 4px solid #007bff; }
        .card-stat.success { border-left-color: #28a745; }
        .card-stat.warning { border-left-color: #ffc107; }
        .card-stat.danger { border-left-color: #dc3545; }
        .navbar-brand { color: #ee501b !important; font-weight: bold; }
    </style>
</head>
<body>
   

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <span class="text-muted">ผู้ดูแลระบบ: <?= getCurrentAdminName() ?>
                            <span class="badge bg-<?= getCurrentAdminRole() === 'super_admin' ? 'danger' : (getCurrentAdminRole() === 'admin' ? 'warning' : 'info') ?>">
                                <?= strtoupper(getCurrentAdminRole()) ?>
                            </span>
                        </span>
                    </div>
                </div>

                <!-- สถิติหลัก -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card card-stat">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <p class="card-title text-muted">สินค้าทั้งหมด</p>
                                        <h3 class="text-primary"><?= number_format($stats['total_products']) ?></h3>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-tshirt fa-2x text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card card-stat success">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <p class="card-title text-muted">ลูกค้าทั้งหมด</p>
                                        <h3 class="text-success"><?= number_format($stats['total_customers']) ?></h3>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-users fa-2x text-success"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card card-stat warning">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <p class="card-title text-muted">ออเดอร์รอดำเนินการ</p>
                                        <h3 class="text-warning"><?= number_format($stats['pending_orders']) ?></h3>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-warning"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card card-stat danger">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col">
                                        <p class="card-title text-muted">ยอดขายรวม</p>
                                        <h3 class="text-danger"><?= number_format($stats['total_revenue'], 2) ?> ฿</h3>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-dollar-sign fa-2x text-danger"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ออเดอร์ล่าสุด -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-shopping-cart"></i> ออเดอร์ล่าสุด</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>ลูกค้า</th>
                                                <th>ยอดรวม</th>
                                                <th>สถานะ</th>
                                                <th>วันที่</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>001</td>
                                                <td>สมชาย ใจดี</td>
                                                <td>299.00 ฿</td>
                                                <td><span class="badge bg-warning">รอดำเนินการ</span></td>
                                                <td>2025-01-04</td>
                                            </tr>
                                            <!-- เพิ่มออเดอร์ตัวอย่างเพิ่มเติม -->
                                        </tbody>
                                    </table>
                                </div>
                                <a href="orders.php" class="btn btn-outline-primary btn-sm">ดูทั้งหมด</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-comments"></i> แชทล่าสุด</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-info">
                                    <small><i class="fas fa-user"></i> สมชาย ใจดี<br>
                                    "สอบถามราคาเสื้อทีม 20 ตัว"<br>
                                    <span class="text-muted">5 นาทีที่แล้ว</span></small>
                                </div>
                                <a href="chats.php" class="btn btn-outline-success btn-sm">ตอบแชท</a>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5><i class="fas fa-calendar"></i> การจองวันนี้</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">ยังไม่มีการจองในวันนี้</p>
                                <a href="bookings.php" class="btn btn-outline-info btn-sm">จัดการการจอง</a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> ข้อมูลร้าน GT Sport Design</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>ชื่อร้าน:</strong> GT Sport Design</p>
                                        <p><strong>LINE:</strong> @gtsport</p>
                                        <p><strong>โทรศัพท์:</strong> 085-559-9164</p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>ที่อยู่:</strong> 339/7 ม.4 ต.บ้านดู่อ.เมือง จ.เชียงราย ประเทศไทย</p>
                                        <p><strong>เว็บไซต์:</strong> <a href="https://gtsportdesign.com/" target="_blank">gtsportdesign.com</a></p>
                                        <p><strong>Facebook:</strong> <a href="https://www.facebook.com/GTSportDesign.1" target="_blank">GT Sport Design</a></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // อัปเดตเวลาในหน้า dashboard
        setInterval(function() {
            const now = new Date();
            document.title = 'Admin Dashboard - GT Sport Design (' + now.toLocaleTimeString() + ')';
        }, 1000);
    </script>
</body>
</html>
