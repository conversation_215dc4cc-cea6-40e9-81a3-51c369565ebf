<?php
session_start();
require_once '../config/database.php';

// ตรวจสอบการล็อกอิน
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

$admin_name = $_SESSION['admin_fullname'] ?? $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin';
$admin_role = $_SESSION['admin_role'] ?? $_SESSION['role'] ?? 'Administrator';

// ดึงข้อมูลการออกแบบ
$designs = [];
$total_designs = 0;
$error = '';

try {
    // ลองดึงจากตาราง customer_designs
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM customer_designs");
        $total_designs = $stmt->fetchColumn();
        
        $stmt = $pdo->query("
            SELECT cd.*, c.name as customer_name, p.name as product_name
            FROM customer_designs cd
            LEFT JOIN customers c ON cd.customer_id = c.id
            LEFT JOIN products p ON cd.product_id = p.id
            ORDER BY cd.created_at DESC
            LIMIT 20
        ");
        $designs = $stmt->fetchAll();
    } catch (Exception $e) {
        // ถ้าไม่มีตาราง customer_designs ให้สร้างข้อมูลจำลอง
        $designs = [
            [
                'id' => 1,
                'design_name' => 'เสื้อทีม ABC FC',
                'customer_name' => 'นาย ก ใจดี',
                'product_name' => 'เสื้อกีฬา',
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s'),
                'notes' => 'ต้องการโลโก้ทีม และหมายเลข 10'
            ],
            [
                'id' => 2,
                'design_name' => 'เสื้อบริษัท XYZ',
                'customer_name' => 'นางสาว ข สวยงาม',
                'product_name' => 'เสื้อโปโล',
                'status' => 'in_progress',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'notes' => 'สีน้ำเงิน พร้อมโลโก้บริษัท'
            ]
        ];
        $total_designs = count($designs);
    }
} catch (Exception $e) {
    $error = 'เกิดข้อผิดพลาดในการโหลดข้อมูล: ' . $e->getMessage();
}

// จัดการการอัปเดตสถานะ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $design_id = (int)($_POST['design_id'] ?? 0);
    
    if ($action === 'update_status' && $design_id > 0) {
        $new_status = $_POST['status'] ?? '';
        $admin_notes = $_POST['admin_notes'] ?? '';
        
        try {
            $stmt = $pdo->prepare("
                UPDATE customer_designs 
                SET status = ?, admin_notes = ?, updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$new_status, $admin_notes, $design_id]);
            
            header('Location: designs.php?success=1');
            exit();
        } catch (Exception $e) {
            $error = 'ไม่สามารถอัปเดตสถานะได้: ' . $e->getMessage();
        }
    }
}

function getStatusBadge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning">รอดำเนินการ</span>',
        'in_progress' => '<span class="badge bg-info">กำลังดำเนินการ</span>',
        'completed' => '<span class="badge bg-success">เสร็จสิ้น</span>',
        'cancelled' => '<span class="badge bg-danger">ยกเลิก</span>'
    ];
    return $badges[$status] ?? '<span class="badge bg-secondary">' . $status . '</span>';
}
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>จัดการการออกแบบ - GT Sport Design Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Kanit', sans-serif;
        }
        .sidebar {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            width: 280px;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 1000;
        }
        .sidebar-brand {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }
        .sidebar-brand h4 {
            margin: 0;
            color: #ee501b;
            font-weight: 700;
        }
        .sidebar-menu {
            padding: 0;
            list-style: none;
            margin: 0;
        }
        .sidebar-menu li {
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        .sidebar-menu a {
            display: block;
            padding: 15px 20px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(238, 80, 27, 0.2);
            color: #ee501b;
        }
        .sidebar-menu a i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            margin-left: 280px;
            padding: 0;
        }
        .top-navbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content-area {
            padding: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .design-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        .design-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            background: #ee501b;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <h4><i class="fas fa-tshirt me-2"></i>GT Sport Design</h4>
            <small>ระบบจัดการ</small>
        </div>
        <ul class="sidebar-menu">
            <li><a href="dashboard.php"><i class="fas fa-tachometer-alt"></i>หน้าหลัก</a></li>
            <li><a href="products.php"><i class="fas fa-box"></i>จัดการสินค้า</a></li>
            <li><a href="orders.php"><i class="fas fa-shopping-cart"></i>คำสั่งซื้อ</a></li>
            <li><a href="customers.php"><i class="fas fa-users"></i>ลูกค้า</a></li>
            <li><a href="designs.php" class="active"><i class="fas fa-paint-brush"></i>การออกแบบ</a></li>
            <li><a href="chat.php"><i class="fas fa-comments"></i>แชทสด</a></li>
            <li><a href="bookings.php"><i class="fas fa-calendar-alt"></i>การจอง</a></li>
            <li><a href="gallery.php"><i class="fas fa-images"></i>แกลเลอรี่</a></li>
            <li><a href="reviews.php"><i class="fas fa-star"></i>รีวิว</a></li>
            <li><a href="reports.php"><i class="fas fa-chart-bar"></i>รายงาน</a></li>
            <li><a href="settings.php"><i class="fas fa-cog"></i>ตั้งค่า</a></li>
            <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i>ออกจากระบบ</a></li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar -->
        <div class="top-navbar d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">จัดการการออกแบบ</h4>
                <small class="text-muted">ดูและจัดการงานออกแบบของลูกค้า</small>
            </div>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                </div>
                <div>
                    <div class="fw-bold"><?php echo htmlspecialchars($admin_name); ?></div>
                    <small class="text-muted"><?php echo htmlspecialchars($admin_role); ?></small>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i>อัปเดตข้อมูลเรียบร้อยแล้ว
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Stats -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary"><?php echo $total_designs; ?></h3>
                            <p class="mb-0">งานออกแบบทั้งหมด</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning"><?php echo count(array_filter($designs, fn($d) => $d['status'] === 'pending')); ?></h3>
                            <p class="mb-0">รอดำเนินการ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info"><?php echo count(array_filter($designs, fn($d) => $d['status'] === 'in_progress')); ?></h3>
                            <p class="mb-0">กำลังดำเนินการ</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success"><?php echo count(array_filter($designs, fn($d) => $d['status'] === 'completed')); ?></h3>
                            <p class="mb-0">เสร็จสิ้น</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Designs List -->
            <?php if (empty($designs)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-paint-brush fa-3x text-muted mb-3"></i>
                    <h3>ยังไม่มีงานออกแบบ</h3>
                    <p class="text-muted">เมื่อลูกค้าส่งงานออกแบบเข้ามา จะแสดงที่นี่</p>
                </div>
            <?php else: ?>
                <?php foreach ($designs as $design): ?>
                <div class="design-card">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-2"><?php echo htmlspecialchars($design['design_name'] ?? 'งานออกแบบ #' . $design['id']); ?></h5>
                            <div class="mb-2">
                                <strong>ลูกค้า:</strong> <?php echo htmlspecialchars($design['customer_name'] ?? 'ไม่ระบุ'); ?><br>
                                <strong>สินค้า:</strong> <?php echo htmlspecialchars($design['product_name'] ?? 'ไม่ระบุ'); ?><br>
                                <strong>วันที่:</strong> <?php echo date('d/m/Y H:i', strtotime($design['created_at'])); ?>
                            </div>
                            <?php if (!empty($design['notes'])): ?>
                                <p class="text-muted mb-2">
                                    <strong>หมายเหตุ:</strong> <?php echo htmlspecialchars($design['notes']); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="mb-3">
                                <?php echo getStatusBadge($design['status']); ?>
                            </div>
                            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#updateModal<?php echo $design['id']; ?>">
                                <i class="fas fa-edit me-1"></i>จัดการ
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Update Modal -->
                <div class="modal fade" id="updateModal<?php echo $design['id']; ?>" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">อัปเดตสถานะงานออกแบบ</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form method="POST">
                                <div class="modal-body">
                                    <input type="hidden" name="action" value="update_status">
                                    <input type="hidden" name="design_id" value="<?php echo $design['id']; ?>">
                                    
                                    <div class="mb-3">
                                        <label class="form-label">สถานะ</label>
                                        <select name="status" class="form-select" required>
                                            <option value="pending" <?php echo $design['status'] === 'pending' ? 'selected' : ''; ?>>รอดำเนินการ</option>
                                            <option value="in_progress" <?php echo $design['status'] === 'in_progress' ? 'selected' : ''; ?>>กำลังดำเนินการ</option>
                                            <option value="completed" <?php echo $design['status'] === 'completed' ? 'selected' : ''; ?>>เสร็จสิ้น</option>
                                            <option value="cancelled" <?php echo $design['status'] === 'cancelled' ? 'selected' : ''; ?>>ยกเลิก</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label">หมายเหตุจากแอดมิน</label>
                                        <textarea name="admin_notes" class="form-control" rows="3" placeholder="เพิ่มหมายเหตุ..."><?php echo htmlspecialchars($design['admin_notes'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                                    <button type="submit" class="btn btn-primary">บันทึก</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
